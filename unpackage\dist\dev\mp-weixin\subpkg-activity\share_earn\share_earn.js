(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["subpkg-activity/share_earn/share_earn"],{128:function(t,e,a){"use strict";(function(t,e){var n=a(4);a(26);n(a(25));var i=n(a(129));t.__webpack_require_UNI_MP_PLUGIN__=a,e(i.default)}).call(this,a(1)["default"],a(2)["createPage"])},129:function(t,e,a){"use strict";a.r(e);var n=a(130),i=a(132);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a(134);var s,r=a(33),c=Object(r["default"])(i["default"],n["render"],n["staticRenderFns"],!1,null,null,null,!1,n["components"],s);c.options.__file="subpkg-activity/share_earn/share_earn.vue",e["default"]=c.exports},130:function(t,e,a){"use strict";a.r(e);var n=a(131);a.d(e,"render",(function(){return n["render"]})),a.d(e,"staticRenderFns",(function(){return n["staticRenderFns"]})),a.d(e,"recyclableRender",(function(){return n["recyclableRender"]})),a.d(e,"components",(function(){return n["components"]}))},131:function(t,e,a){"use strict";var n;a.r(e),a.d(e,"render",(function(){return i})),a.d(e,"staticRenderFns",(function(){return s})),a.d(e,"recyclableRender",(function(){return o})),a.d(e,"components",(function(){return n}));try{n={uniIcons:function(){return Promise.all([a.e("common/vendor"),a.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(a.bind(null,165))}}}catch(r){if(-1===r.message.indexOf("Cannot find module")||-1===r.message.indexOf(".vue"))throw r;console.error(r.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var i=function(){var t=this,e=t.$createElement,a=(t._self._c,t.inviteRecords.length);t.$mp.data=Object.assign({},{$root:{g0:a}})},o=!1,s=[];i._withStripped=!0},132:function(t,e,a){"use strict";a.r(e);var n=a(133),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},133:function(t,e,a){"use strict";(function(t,n){var i=a(4);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(a(30)),s=i(a(41)),r={data:function(){return{statusBarHeight:20,navbarHeight:80,activeTab:0,showPosterModal:!1,posterGenerated:!1,canvasWidth:300,canvasHeight:500,posterImagePath:"",screenWidth:375,pixelRatio:1,qrCodePath:"",shareData:{income:0,shareNumber:0},activityRules:"",inviteRecords:[],rewardRecords:[]}},computed:{navbarStyle:function(){return{height:this.navbarHeight+"px",paddingTop:this.statusBarHeight+"px"}}},onLoad:function(){this.setNavbarInfo(),this.initCanvasSize(),this.getShareData(),this.getInviteRecords(),this.getRewardRecords(),this.getActivityRules()},onShow:function(){this.getShareData(),this.getInviteRecords(),this.getRewardRecords()},methods:{setNavbarInfo:function(){try{var e=t.getSystemInfoSync();this.statusBarHeight=e.statusBarHeight;var a=t.getMenuButtonBoundingClientRect(),n=a.top,i=a.height,o=i+2*(n-this.statusBarHeight);this.navbarHeight=this.statusBarHeight+o}catch(s){console.error("获取系统信息失败:",s),this.statusBarHeight=20,this.navbarHeight=60}},initCanvasSize:function(){try{var e=t.getSystemInfoSync();this.screenWidth=e.screenWidth,this.pixelRatio=e.pixelRatio;var a=400,n=320,i=.8*this.screenWidth;i>a?i=a:i<n&&(i=n),this.canvasWidth=Math.floor(i*this.pixelRatio)/this.pixelRatio,this.canvasHeight=Math.floor(1.8*this.canvasWidth),console.log("画布尺寸:",this.canvasWidth,"x",this.canvasHeight),console.log("屏幕信息:",this.screenWidth,"x",e.screenHeight,"像素比:",this.pixelRatio)}catch(o){console.error("获取屏幕信息失败:",o),this.canvasWidth=300,this.canvasHeight=500}},goBack:function(){t.navigateBack()},getShareData:function(){var e=this;s.default.get("/app/share").then((function(t){t&&200===t.code&&t.data&&(e.shareData.income=t.data.income||0,e.shareData.shareNumber=t.data.shareNumber||0,console.log("分享数据获取成功:",e.shareData))})).catch((function(e){if(console.error("获取分享数据失败:",e),e&&500===e.code)return console.log("接口返回500错误，返回上一页"),void setTimeout((function(){t.navigateBack({delta:1})}),1e3);t.showToast({title:"获取数据失败",icon:"none"})}))},getInviteRecords:function(){var e=this;s.default.get("/app/share/user/list").then((function(t){t&&200===t.code&&t.data&&(e.inviteRecords=t.data.map((function(t){return{name:t.nickName||"微信用户",time:t.createTime||"",avatar:t.avatar||"/static/images/avatar.png"}})),console.log("邀请记录获取成功:",e.inviteRecords))})).catch((function(e){console.error("获取邀请记录失败:",e),t.showToast({title:"获取邀请记录失败",icon:"none"})}))},getRewardRecords:function(){var e=this;s.default.get("/app/share/list").then((function(t){t&&200===t.code&&t.rows&&(e.rewardRecords=t.rows.map((function(t){return{name:t.name||"用户",time:t.createTime||"",avatar:"/static/images/avatar.png",amount:t.orderAmout||0}})),console.log("奖励记录获取成功:",e.rewardRecords))})).catch((function(e){console.error("获取奖励记录失败:",e),t.showToast({title:"获取奖励记录失败",icon:"none"})}))},getActivityRules:function(){var t=this;s.default.get("/app/configure/activity_rules").then((function(e){e&&200===e.code&&(t.activityRules=e.msg||"",console.log("活动规则获取成功:",t.activityRules))})).catch((function(e){console.error("获取活动规则失败:",e),t.activityRules="邀请好友注册成功后，好友下单即可获得奖励\n每邀请一位好友最多可获得0.2元奖励\n奖励将在好友完成首单后发放"}))},showMore:function(){var e=this;t.showActionSheet({itemList:["分享给朋友","收藏"],success:function(t){0===t.tapIndex?e.shareToFriend():1===t.tapIndex&&e.collectActivity()}})},showTarget:function(){t.showToast({title:"功能开发中",icon:"none"})},showRules:function(){var e=this.activityRules||"1. 邀请好友注册成功后，好友下单即可获得奖励\n2. 每邀请一位好友最多可获得0.2元奖励\n3. 奖励将在好友完成首单后发放\n4. 活动最终解释权归平台所有";t.showModal({title:"活动规则",content:e,showCancel:!1})},generatePoster:function(){var t=this;this.initCanvasSize(),this.showPosterModal=!0,this.posterGenerated=!1,this.generateQRCode().then((function(){t.$nextTick((function(){t.drawPoster()}))})).catch((function(){t.qrCodePath="/static/images/avatar.png",t.$nextTick((function(){t.drawPoster()}))}))},generateQRCode:function(){var t=this;return new Promise((function(e,a){var i=o.default.getUserId();if(console.log("userId",i),!i)return console.warn("用户未登录，无法生成专属小程序码"),void a("用户未登录");var r=i,c="pages/login/login";s.default.post("/app/share/url",{scene:r,page:c,check_path:!1}).then((function(i){if(i&&200===i.code&&i.msg){var o=i.msg,s="".concat(n.env.USER_DATA_PATH,"/qrcode_").concat(Date.now(),".png");n.getFileSystemManager().writeFile({filePath:s,data:o,encoding:"base64",success:function(){t.qrCodePath=s,console.log("小程序码生成成功:",s),e()},fail:function(t){console.error("保存小程序码失败:",t),a(t)}})}else console.error("小程序码数据格式错误:",i),a("小程序码数据格式错误")})).catch((function(t){console.error("生成小程序码失败:",t),a(t)}))}))},generateQRCodeFallback:function(){var t=this;return new Promise((function(e,a){var n=o.default.getUserId();n?(console.log("使用备用方案生成小程序码，用户ID:",n),t.qrCodePath="/static/images/avatar.png",e()):a("用户未登录")}))},drawPoster:function(){var e=this,a=t.createCanvasContext("posterCanvas",this),n=20,i=a.createLinearGradient(0,0,0,this.canvasHeight);i.addColorStop(0,"#096ff8"),i.addColorStop(1,"#096ff8"),a.setFillStyle(i),this.fillRoundedRect(a,0,0,this.canvasWidth,this.canvasHeight,n);var o=Math.floor(.35*this.canvasHeight);a.drawImage("/subpkg-activity/static/images/hbheader.png",0,0,this.canvasWidth,o);var s=.85*this.canvasWidth,r=Math.floor(.45*this.canvasHeight),c=(this.canvasWidth-s)/2,h=o+Math.floor(.04*this.canvasHeight),l=h-Math.floor(.12*this.canvasHeight);a.setFillStyle("#ffffff"),this.fillRoundedRect(a,c,l,s,r,15);var u=Math.floor(.8*Math.min(s,r)),d=c+(s-u)/2,f=l+(r-u)/2-Math.floor(.05*r),v=this.qrCodePath||"/subpkg-activity/static/images/haibao1.png";a.drawImage(v,d,f,u,u),a.setFillStyle("#000000"),a.setFontSize(Math.floor(.045*this.canvasWidth)),a.setTextAlign("center");var g=l+r-Math.floor(.04*r);a.fillText("分享码",this.canvasWidth/2,g);var p=h+r-Math.floor(.08*this.canvasHeight),m=Math.floor(.22*this.canvasHeight),w=.9*this.canvasWidth,R=(this.canvasWidth-w)/2;a.setFillStyle("#ffffff"),this.fillRoundedRect(a,R,p,w,m,15),a.setFillStyle("#000000"),a.setFontSize(Math.floor(.055*this.canvasWidth)),a.setTextAlign("center"),a.setTextBaseline("top");var T=p+Math.floor(.1*m);a.fillText("活动规则",this.canvasWidth/2,T),a.setFillStyle("#666666"),a.setFontSize(Math.floor(.045*this.canvasWidth)),a.setTextAlign("left"),a.setTextBaseline("top");var y=.9*w,b=R+(w-y)/2,x=Math.floor(.045*this.canvasWidth),M=Math.floor(.055*this.canvasWidth*1.2),P=T+M+Math.floor(.05*m),I=P;if(this.activityRules){var S=this.activityRules.split("\n").filter((function(t){return t.trim()}));S.forEach((function(t){if(t.trim()){var n=e.drawMultilineText(a,t.trim(),b,I,y,x);I+=n*x+Math.floor(.3*x)}}))}else{var _=["邀请好友注册成功后，好友下单即可获得奖励","奖励将在好友完成首单后发放"];_.forEach((function(t){var n=e.drawMultilineText(a,t,b,I,y,x);I+=n*x+Math.floor(.3*x)}))}a.setTextBaseline("alphabetic"),a.draw(!1,(function(){e.posterGenerated=!0,t.canvasToTempFilePath({canvasId:"posterCanvas",success:function(t){e.posterImagePath=t.tempFilePath}},e)}))},drawRoundedRect:function(t,e,a,n,i,o){t.beginPath(),t.moveTo(e+o,a),t.lineTo(e+n-o,a),t.quadraticCurveTo(e+n,a,e+n,a+o),t.lineTo(e+n,a+i-o),t.quadraticCurveTo(e+n,a+i,e+n-o,a+i),t.lineTo(e+o,a+i),t.quadraticCurveTo(e,a+i,e,a+i-o),t.lineTo(e,a+o),t.quadraticCurveTo(e,a,e+o,a),t.closePath()},fillRoundedRect:function(t,e,a,n,i,o){this.drawRoundedRect(t,e,a,n,i,o),t.fill()},wrapText:function(t,e,a){for(var n=e.split(""),i=[],o="",s=0;s<n.length;s++){var r=o+n[s],c=t.measureText(r),h=c.width;h>a&&""!==o?(i.push(o),o=n[s]):o=r}return""!==o&&i.push(o),i},drawMultilineText:function(t,e,a,n,i,o){var s=this.wrapText(t,e,i);return s.forEach((function(e,i){t.fillText(e,a,n+i*o)})),s.length},closePosterModal:function(){this.showPosterModal=!1,this.posterGenerated=!1},savePosterLocal:function(){this.posterImagePath?t.saveImageToPhotosAlbum({filePath:this.posterImagePath,success:function(){t.showToast({title:"保存成功",icon:"success"})},fail:function(){t.showToast({title:"保存失败",icon:"none"})}}):t.showToast({title:"海报还未生成完成",icon:"none"})},shareToWechat:function(){this.posterImagePath?t.showToast({title:"分享到微信好友",icon:"none"}):t.showToast({title:"海报还未生成完成",icon:"none"})},shareToMoments:function(){this.posterImagePath?t.showToast({title:"分享到朋友圈",icon:"none"}):t.showToast({title:"海报还未生成完成",icon:"none"})},withdraw:function(){t.navigateTo({url:"/subpkg-activity/withdraw/withdraw?amount=".concat(this.shareData.income)})},switchTab:function(t){this.activeTab=t},shareToFriend:function(){t.share({provider:"weixin",scene:"WXSceneSession",type:0,href:"https://your-domain.com/share",title:"邀请好友，得好礼",summary:"邀请好友注册成功后，好友下单即得现金",imageUrl:"/static/images/share_poster.png"})},collectActivity:function(){t.showToast({title:"收藏成功",icon:"success"})},getImageInfo:function(e){var a={width:280,height:140};try{var n=t.getImageInfoSync(e);n&&(a.width=n.width,a.height=n.height)}catch(i){console.error("获取图片信息失败:",i)}return a}}};e.default=r}).call(this,a(2)["default"],a(1)["default"])},134:function(t,e,a){"use strict";a.r(e);var n=a(135),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},135:function(t,e,a){}},[[128,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/subpkg-activity/share_earn/share_earn.js.map