<template>
	<view class="record-container">
		<!-- 页面顶部标签页 -->
		<view class="tab-container">
			<view class="tab-item" :class="{ active: activeTab === 0 }" @click="switchTab(0)">
				<text class="tab-text">全部订单</text>
				<view v-if="activeTab === 0" class="active-line"></view>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 1 }" @click="switchTab(1)">
				<text class="tab-text">待出行<text v-if="pendingCount > 0"
						class="tab-count">({{ pendingCount }})</text></text>
				<view v-if="activeTab === 1" class="active-line"></view>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 2 }" @click="switchTab(2)">
				<text class="tab-text">已出行<text v-if="completedCount > 0"
						class="tab-count">({{ completedCount }})</text></text>
				<view v-if="activeTab === 2" class="active-line"></view>
			</view>
		</view>

		<!-- 订单列表 -->
		<view class="order-list">
			<!-- 加载状态 -->
			<view v-if="loading" class="loading-tip">
				<text>{{ loadingText }}</text>
			</view>
			<!-- 空数据提示 -->
			<view v-else-if="filteredOrders.length === 0" class="empty-tip">
				<text>暂无订单记录</text>
			</view>
			<!-- 使用v-for遍历订单 -->
			<view v-for="(order, index) in filteredOrders" :key="order.id" class="order-item"
				@click="goToOrderDetail(order.id)">
				<view class="order-header">
					<view class="order-type">
						<uni-icons :type="getStatusIcon(order.status)" size="22" :color="getStatusColor(order.status)"
							class="ticket-icon"></uni-icons>
						<text class="ticket-type">{{ order.type }}</text>
					</view>
					<text :class="['order-status', getStatusClass(order.status)]">{{ order.status }}</text>
				</view>
				<view class="order-route">
					<text class="route-text">{{ order.route }}</text>
					<text class="route-price">¥{{ order.price.toFixed(2) }}</text>
				</view>
				<view class="order-time">
					<text class="time-text">{{ order.departureTime }} 出发</text>
				</view>
				<view class="order-divider"></view>
				<!-- 合并"已付"和按钮到同一行 -->
				<view class="order-payment-actions">
					<view class="payment-part">
						<text class="payment-text">已付: <text
								style="color: #EE0A24;">¥{{ order.paid.toFixed(2) }}</text></text>
					</view>
					<view class="actions-part">
						<!-- <button v-if="order.canCancel" class="action-btn cancel-btn" @click.stop="cancelOrder(order.id)">取消订单</button> -->
						<button v-if="order.canRefund" class="action-btn refund-btn"
							@click.stop="applyRefund(order.id)">申请售后</button>
						<button class="action-btn qr-btn" @click.stop="showGroupQrCode">群二维码</button>
					</view>
				</view>
			</view>
		</view>
		<!-- 群二维码弹窗 -->
		<view class="qr-popup" v-if="showQrPopup" @click="hideGroupQrCode">
			<view class="qr-popup-content" @click.stop>
				<view class="qr-popup-header">
					<text class="qr-popup-title">购票成功</text>
					<view class="qr-popup-close" @click="hideGroupQrCode">
						<text class="close-icon">×</text>
					</view>
				</view>
				<view class="qr-popup-body">
					<image v-if="groupQrCodeUrl" :src="groupQrCodeUrl" class="qr-code-image" mode="aspectFit"></image>
					<view v-else class="qr-loading">
						<text>加载中...</text>
					</view>
					<text class="qr-popup-desc">扫码进群，专业售后服务和时效通知</text>
				</view>
				<view class="qr-popup-footer">
					<button class="qr-save-btn" @click="saveQrCode">保存二维码</button>
				</view>
			</view>
		</view>
	</view>


</template>

<script>
import { orderApi, configApi } from '@/utils/api.js';

export default {
	data() {
		return {
			activeTab: 0,  // 默认选中"全部订单"
			orders: [],  // 从接口获取的订单数据
			loading: false,  // 加载状态
			loadingText: '正在加载订单...',
			showQrPopup: false,  // 群二维码弹窗显示状态
			groupQrCodeUrl: ''  // 群二维码图片URL
		};
	},
	computed: {
		// 根据当前选中的标签页筛选订单
		filteredOrders() {
			if (this.activeTab === 0) {
				// 全部订单
				return this.orders;
			} else if (this.activeTab === 1) {
				// 待出行（包括已支付和未支付的订单）
				return this.orders.filter(order =>
					order.status === '待出行'
				);
			} else {
				// 已出行（包括已出行、已退款、待退款）
				return this.orders.filter(order =>
					order.status === '已出行' || order.status === '已退款' || order.status === '待退款'
				);
			}
		},
		// 待出行订单的数量（包括未支付和已支付待出行）
		pendingCount() {
			return this.orders.filter(order =>
				order.status === '待出行'
			).length;
		},
		// 已出行订单的数量（包括已出行、已退款、待退款）
		completedCount() {
			return this.orders.filter(order =>
				order.status === '已出行' || order.status === '已退款' || order.status === '待退款'
			).length;
		}
	},
	onLoad(options) {
		// 页面加载时获取订单数据
		// this.loadOrderList();

		// 检查是否是支付成功跳转过来的
		if (options && options.paymentSuccess === 'true') {
			// 延迟显示弹窗，确保页面加载完成
			setTimeout(() => {
				this.showGroupQrCode();
			}, 500);
		}
	},
	onShow() {
		// 页面显示时刷新数据（从其他页面返回时也会刷新）
		this.loadOrderList();
	},
	methods: {
		// 加载订单列表数据
		async loadOrderList() {
			try {
				this.loading = true;
				this.loadingText = '正在加载订单...';

				// 调用接口获取订单数据
				const response = await orderApi.getOrderList();

				if (response && response.code === 200) {
					// 处理接口返回的数据
					this.orders = this.processOrderData(response);
				} else {
					this.orders = [];
					console.log('订单数据为空或接口返回异常:', response);
				}
			} catch (error) {
				console.error('获取订单列表失败:', error);
				this.orders = [];
				uni.showToast({
					title: '获取订单失败，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 处理接口返回的订单数据
		processOrderData(data) {
			// 处理接口返回的订单数据，转换为页面需要的格式
			if (!data || !data.rows || !Array.isArray(data.rows)) {
				return [];
			}

			return data.rows.map(order => {
				// 根据支付状态确定订单状态
				let status = '';
				let canCancel = false;
				let canRefund = false;

				switch (order.payStatus) {
					case 0: // 未支付
						status = '未支付';
						canCancel = true;
						canRefund = false;
						break;
					case 1: // 已支付（待出行）
						status = '待出行';
						canCancel = true;
						canRefund = true;
						break;
					case 2: // 待退款
						status = '待退款';
						canCancel = false;
						canRefund = false;
						break;
					case 3: // 已退款
						status = '已退款';
						canCancel = false;
						canRefund = false;
						break;
					case 4: // 已出行
						status = '已出行';
						canCancel = false;
						canRefund = false;
						break;
					default:
						status = '未知状态';
						canCancel = false;
						canRefund = false;
				}

				// 格式化上车时间
				let departureTime = '';
				if (order.upTicketTime) {
					// 如果有具体时间，使用具体时间
					departureTime = this.formatDateTime(order.upTicketTime);
				} else {
					// 如果没有具体时间，显示待确定
					departureTime = '待确定';
				}

				// 构建路线信息
				const route = `${order.upAddress || '未知'} → ${order.downAddress || '未知'}`;

				return {
					id: order.id,
					orderNo: order.orderNo,
					type: "汽车票", // 固定为汽车票
					status: status,
					route: route,
					price: parseFloat(order.payAmout || 0),
					departureTime: departureTime,
					paid: parseFloat(order.payAmout || 0),
					canCancel: canCancel,
					canRefund: canRefund,
					// 保留原始数据，便于后续操作
					originalData: order
				};
			});
		},

		// 格式化日期时间
		formatDateTime(dateStr) {
			if (!dateStr) return '待确定';

			try {
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');

				return `${year}-${month}-${day} ${hours}:${minutes}`;
			} catch (error) {
				console.error('日期格式化失败:', error);
				return '待确定';
			}
		},

		// 根据订单状态获取图标
		getStatusIcon(status) {
			switch (status) {
				case '待出行':
				case '未支付':
					return 'paperplane-filled';
				case '已出行':
					return 'paperplane';
				case '已退款':
				case '待退款':
					return 'undo';
				default:
					return 'paperplane';
			}
		},

		// 根据订单状态获取颜色
		getStatusColor(status) {
			switch (status) {
				case '待出行':
					return '#3F8DF9';
				case '未支付':
					return '#FF9500';
				case '已出行':
					return '#888888';
				case '已退款':
					return '#00C851';
				case '待退款':
					return '#FF6B6B';
				default:
					return '#888888';
			}
		},

		// 根据订单状态获取样式类
		getStatusClass(status) {
			switch (status) {
				case '待出行':
					return 'pending';
				case '未支付':
					return 'unpaid';
				case '已出行':
					return 'completed';
				case '已退款':
					return 'refunded';
				case '待退款':
					return 'refunding';
				default:
					return 'completed';
			}
		},
		// 切换标签页
		switchTab(index) {
			this.activeTab = index;
		},

		// 跳转到订单详情页
		goToOrderDetail(orderId) {
			// 找到对应的订单数据
			const orderData = this.orders.find(order => order.id === orderId);
			if (!orderData) {
				uni.showToast({
					title: '订单信息不存在',
					icon: 'none'
				});
				return;
			}

			// 将订单数据存储到本地，供详情页使用
			uni.setStorageSync('orderDetailData', orderData);

			// 跳转到订单详情页
			uni.navigateTo({
				url: `/subpkg-booking/order_detail/order_detail?id=${orderId}`
			});
		},

		// 取消订单
		cancelOrder(orderId) {
			uni.showModal({
				title: '提示',
				content: '确定要取消该订单吗？',
				success: (res) => {
					if (res.confirm) {
						// 这里添加取消订单的逻辑
						uni.showToast({
							title: '订单已取消',
							icon: 'success'
						});
					}
				}
			});
		},

		// 申请售后
		async applyRefund(orderId) {
			try {
				// 显示确认弹框
				const result = await new Promise((resolve) => {
					uni.showModal({
						title: '申请售后',
						content: '确定要申请售后吗？',
						success: (res) => {
							resolve(res.confirm);
						}
					});
				});

				if (!result) {
					return; // 用户取消
				}

				// 显示加载状态
				uni.showLoading({
					title: '申请中...'
				});

				// 调用申请售后接口
				const response = await orderApi.applyRefund(orderId);

				uni.hideLoading();

				if (response && response.code === 200) {
					uni.showToast({
						title: '申请售后成功',
						icon: 'success'
					});

					// 刷新订单列表
					this.loadOrderList();
				} else {
					uni.showToast({
						title: response.msg || '申请售后失败',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('申请售后失败:', error);
				uni.showToast({
					title: '申请售后失败，请重试',
					icon: 'none'
				});
			}
		},

		// 显示群二维码弹窗
		async showGroupQrCode() {
			try {
				// 显示弹窗
				this.showQrPopup = true;

				// 如果已经有二维码URL，直接显示
				if (this.groupQrCodeUrl) {
					return;
				}

				// 调用接口获取群二维码
				const response = await configApi.getConfig('group_qr_code');

				if (response && response.code === 200) {
					this.groupQrCodeUrl = response.msg || '';
				} else {
					uni.showToast({
						title: '获取群二维码失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取群二维码失败:', error);
				uni.showToast({
					title: '获取群二维码失败',
					icon: 'none'
				});
			}
		},

		// 隐藏群二维码弹窗
		hideGroupQrCode() {
			this.showQrPopup = false;
		},

		// 保存二维码到相册
		async saveQrCode() {
			if (!this.groupQrCodeUrl) {
				uni.showToast({
					title: '二维码加载中，请稍后',
					icon: 'none'
				});
				return;
			}

			try {
				// 显示加载提示
				uni.showLoading({
					title: '正在保存...'
				});

				// 先下载图片到本地
				const downloadResult = await new Promise((resolve, reject) => {
					uni.downloadFile({
						url: this.groupQrCodeUrl,
						success: (res) => {
							if (res.statusCode === 200) {
								resolve(res.tempFilePath);
							} else {
								reject(new Error('下载失败'));
							}
						},
						fail: reject
					});
				});

				// 保存到相册
				await new Promise((resolve, reject) => {
					uni.saveImageToPhotosAlbum({
						filePath: downloadResult,
						success: resolve,
						fail: reject
					});
				});

				uni.hideLoading();
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
			} catch (error) {
				uni.hideLoading();
				console.error('保存失败:', error);

				let errorMsg = '保存失败，请重试';
				if (error.errMsg && error.errMsg.includes('auth')) {
					errorMsg = '请授权访问相册';
				} else if (error.errMsg && error.errMsg.includes('download')) {
					errorMsg = '图片下载失败';
				}

				uni.showToast({
					title: errorMsg,
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #F6F7F9;
	min-height: 100vh;
}

.record-container {
	padding-bottom: 20rpx;
}

/* 标签页样式 */
.tab-container {
	display: flex;
	justify-content: space-around;
	background-color: #FFFFFF;
	padding-top: 10rpx;
	border-bottom: 1px solid #EEEEEE;
	position: sticky;
	top: 0;
	z-index: 10;
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	padding: 20rpx 0;
}

.tab-text {
	font-size: 28rpx;
	color: #333333;
	padding-bottom: 16rpx;
}

.tab-count {
	font-size: 24rpx;
	color: #3F8DF9;
	margin-left: 4rpx;
}

.active .tab-text {
	font-weight: 500;
	color: #3F8DF9;
}

.active-line {
	position: absolute;
	bottom: 0;
	width: 80rpx;
	height: 6rpx;
	background-color: #3F8DF9;
	border-radius: 3rpx;
}

/* 订单列表样式 */
.order-list {
	padding: 20rpx;
}

/* 加载状态样式 */
.loading-tip {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 300rpx;
	background-color: #FFFFFF;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);
}

.loading-tip text {
	font-size: 30rpx;
	color: #3F8DF9;
}

/* 空订单提示样式 */
.empty-tip {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 300rpx;
	background-color: #FFFFFF;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);
}

.empty-tip text {
	font-size: 30rpx;
	color: #999999;
}

.order-item {
	background-color: #FFFFFF;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);
}

/* 订单头部 */
.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.order-type {
	display: flex;
	align-items: center;
}

.ticket-icon {
	margin-right: 10rpx;
}

.ticket-type {
	font-size: 28rpx;
	color: #333333;
}

.order-status {
	font-size: 28rpx;
}

.pending {
	color: #3F8DF9;
}

.unpaid {
	color: #FF9500;
}

.completed {
	color: #888888;
}

.refunded {
	color: #00C851;
}

.refunding {
	color: #FF6B6B;
}

/* 路线信息 */
.order-route {
	display: flex;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.route-text {
	font-size: 32rpx;
	color: #333333;
	font-weight: bold;
	flex: 1;
}

.route-price {
	font-size: 32rpx;
	color: #333333;
	font-weight: bold;
}

/* 出发时间 */
.order-time {
	margin-bottom: 20rpx;
}

.time-text {
	font-size: 28rpx;
	color: #666666;
}

/* 分割线 */
.order-divider {
	height: 1rpx;
	background-color: #EEEEEE;
	margin: 20rpx 0;
}

/* 支付信息和操作按钮合并行 */
.order-payment-actions {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.payment-part {
	flex: 1;
}

.actions-part {
	display: flex;
	justify-content: flex-end;
}

/* 支付信息 */
.payment-text {
	font-size: 28rpx;
	color: #666666;
}

/* 操作按钮 */
.action-btn {
	font-size: 26rpx;
	height: 60rpx;
	line-height: 60rpx;
	padding: 0 30rpx;
	margin-left: 20rpx;
	background-color: #FFFFFF;
	border-radius: 30rpx;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.cancel-btn {
	color: #666666;
	border: 1rpx solid #DDDDDD;
}

.refund-btn {
	color: #666666;
	border: 1rpx solid #DDDDDD;
}

.qr-btn {
	color: #3F8DF9;
	border: 1rpx solid #3F8DF9;
}

/* 群二维码弹窗样式 */
.qr-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}

.qr-popup-content {
	background-color: #FFFFFF;
	border-radius: 20rpx;
	width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.qr-popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.qr-popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.qr-popup-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;
	background-color: #f5f5f5;
}

.close-icon {
	font-size: 40rpx;
	color: #999999;
	line-height: 1;
}

.qr-popup-body {
	padding: 40rpx;
	text-align: center;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.qr-code-image {
	width: 400rpx;
	height: 400rpx;
	margin: 0 auto 30rpx;
	border-radius: 10rpx;
}

.qr-loading {
	width: 400rpx;
	height: 400rpx;
	margin: 0 auto 30rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 10rpx;
}

.qr-loading text {
	color: #999999;
	font-size: 28rpx;
}

.qr-popup-desc {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.5;
}

.qr-popup-footer {
	padding: 20rpx 40rpx 40rpx;
}

.qr-save-btn {
	width: 100%;
	height: 80rpx;
	background-color: #333333;
	color: #FFFFFF;
	font-size: 32rpx;
	border-radius: 40rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>