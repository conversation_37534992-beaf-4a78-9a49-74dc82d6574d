<template>
	<view class="content">
		<!-- 日期选择器 -->
		<scroll-view class="date-scroll" scroll-x="true" show-scrollbar="false" :scroll-left="scrollLeft">
			<view class="date-list">
				<view v-for="(item, index) in dateList" :key="index"
					:class="['date-item', { 'date-item-active': currentDateIndex === index }]"
					@click="selectDate(index)">
					<text class="week-day">{{ item.weekDay }}</text>
					<view :class="['day-num', { 'today-circle': item.isToday }]">
						<text v-if="item.isToday" style="font-size: 24rpx;">今天</text>
						<text v-else style="font-size: 28rpx;">{{ item.day }}</text>
					</view>
				</view>
			</view>
		</scroll-view>
		<!-- 日历图标 -->
		<view class="calendar-icon" @click="showDatePicker">
			<image src="/static/icons/calendar.png" class="calendar-img"></image>
			<!-- // <image src="/static/icons/back.png" class="arrow-img"></image> -->
		</view>


		<!-- 车票列表 -->
		<view class="ticket-list">
			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<view class="loading-text">{{ loadingText }}</view>
			</view>

			<!-- 无数据状态 -->
			<view v-else-if="!loading && ticketList.length === 0" class="empty-container">
				<view class="empty-text">暂无车票信息</view>
				<view class="empty-tip">请尝试选择其他日期</view>
			</view>

			<!-- 车票列表 -->
			<view v-else v-for="(ticket, index) in ticketList" :key="index"
				:class="['ticket-item', ticket.type, { 'ticket-disabled': ticket.status === '无票' }]"
				@click="goToTicketConfirm(ticket)">
				<!-- 即将发车标签 - 左上角 -->
				<text v-if="ticket.isImmediateDeparture" class="departure-tag">即将发车</text>

				<view class="ticket-content">
					<view class="ticket-left">
						<!-- 时间和时长 -->
						<view class="time-section">
							<text class="ticket-time">{{ ticket.time }}</text>
							<text class="ticket-duration">约{{ ticket.duration }}小时 <text class="info-icon">ⓘ</text></text>
						</view>

						<!-- 车票类型 -->
						<view class="ticket-type-tag">
							<text class="type-text">{{ ticket.typeName }}</text>
						</view>
					</view>

					<!-- 站点信息 - 中间 -->
					<view class="ticket-stations">
						<view class="station-line">
							<view class="station-dot start"></view>
							<view class="station-connector"></view>
							<view class="station-dot end"></view>
						</view>
						<view class="station-names">
							<text class="station-name">{{ departureStation }}</text>
							<text class="station-name">{{ arrivalStation }}</text>
						</view>
					</view>

					<!-- 价格信息 - 右侧 -->
					<view class="ticket-right">
						<view class="price-box">
							<view class="price-original">
								<text class="price-through">¥{{ ticket.originalPrice.toFixed(2) }}</text>
							</view>
							<view class="price-current">
								<text class="price-symbol">¥</text>
								<text class="price-value">{{ ticket.currentPrice }}</text>
							</view>
						</view>
						<view class="discount-tag">
							<text>优惠¥{{ ticket.discount }}</text>
						</view>
						<text class="ticket-status" v-if="ticket.status.includes('张')" style="color: red;">{{ ticket.status }}</text>
						<text class="ticket-status" v-else>{{ ticket.status }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 日期选择器弹窗 -->
		<view class="calendar-popup" v-if="showCalendar">
			<view class="calendar-mask" @click="cancelDatePicker"></view>
			<view class="calendar-container">
				<view class="calendar-header custom-calendar-header">
					<text class="calendar-close" @click="cancelDatePicker">×</text>
					<view class="calendar-nav">
						<text class="calendar-title">{{ currentYear }}年{{ currentMonth }}月</text>
					</view>
				</view>
				
				<!-- 使用uni-calendar组件 -->
				<uni-calendar
					class="custom-calendar"
					ref="calendar"
					:insert="true"
					:lunar="false"
					:start-date="startDate"
					:end-date="endDate"
					@change="calendarChange"
					@monthSwitch="monthSwitch"
				/>
				
				<view class="calendar-footer">
					<view class="calendar-confirm" @click="confirmDatePicker">确认</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { ticketApi } from '@/utils/api.js';

export default {
	data() {
		const now = new Date();
		const year = now.getFullYear();
		const month = now.getMonth() + 1;
		const day = now.getDate();
		const nowDateStr = `${year}-${month}-${day}`;
		
		return {
			departureStation: '同济大学',
			arrivalStation: '泰安各县城',
			currentDateIndex: 0,
			dateList: [], // 将在onLoad中填充

			// 地点ID（需要根据实际业务配置）
			upAddressId: 1, // 上车地点ID，默认为同济大学
			downAddressId: 2, // 下车地点ID，默认为泰安各县城

			// 车票数据列表
			ticketList: [],

			// 加载状态
			loading: false,
			loadingText: '加载中...',

			// 日历相关数据
			showCalendar: false,
			currentYear: now.getFullYear(),
			currentMonth: now.getMonth() + 1,
			currentDay: now.getDate(),
			selectedInfo: null,
			startDate: nowDateStr,
			endDate: `${year + 1}-${month}-${day}`,
			selectedDate: nowDateStr,
			
			weekDayNames: ['日', '一', '二', '三', '四', '五', '六'],
			today: now,
			scrollLeft: 0, // 日期滚动条的滚动位置
			screenWidth: 375, // 屏幕宽度，将在onReady中获取实际值
			dateItemWidth: 120 // 日期项宽度（rpx）
		};
	},
	onLoad(options) {
		// 从路由参数获取出发地和目的地
		if (options.departure) {
			this.departureStation = decodeURIComponent(options.departure);
		}
		if (options.arrival) {
			this.arrivalStation = decodeURIComponent(options.arrival);
		}

		// 从路由参数获取地点ID（如果有的话）
		if (options.upAddressId) {
			this.upAddressId = parseInt(options.upAddressId);
		}
		if (options.downAddressId) {
			this.downAddressId = parseInt(options.downAddressId);
		}

		// 动态设置导航栏标题
		uni.setNavigationBarTitle({
			title: this.departureStation + ' — ' + this.arrivalStation
		});

		// 生成未来30天的日期
		this.generateDateList();

		// 处理传递过来的日期参数
		if (options.selectedDate) {
			this.setSelectedDate(options.selectedDate);
		}

		// 加载车票数据
		this.loadTicketList();
	},
	onReady() {
		// 获取系统信息，用于计算滚动位置
		uni.getSystemInfo({
			success: (res) => {
				this.screenWidth = res.screenWidth;
				// 如果已经设置了选中日期，重新计算滚动位置
				if (this.currentDateIndex > 0) {
					this.$nextTick(() => {
						this.scrollToSelectedDate();
					});
				}
			}
		});
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 生成日期列表（未来30天）
		generateDateList() {
			const today = new Date();
			const dateList = [];

			for (let i = 0; i < 30; i++) {
				const date = new Date();
				date.setDate(today.getDate() + i);

				const weekIndex = date.getDay();
				const weekDay = this.weekDayNames[weekIndex];

				dateList.push({
					date: date,
					day: date.getDate(),
					weekDay: weekDay,
					isToday: i === 0
				});
			}

			this.dateList = dateList;
		},

		// 选择日期
		selectDate(index) {
			this.currentDateIndex = index;
			// 重新加载该日期的车票数据
			this.loadTicketList();
		},

		// 设置选中的日期（从首页传递过来的日期）
		setSelectedDate(selectedDateStr) {
			try {
				// 解析传递过来的日期字符串，格式如 "2024-1-15"
				const dateParts = selectedDateStr.split('-');
				if (dateParts.length === 3) {
					const year = parseInt(dateParts[0]);
					const month = parseInt(dateParts[1]);
					const day = parseInt(dateParts[2]);
					const selectedDate = new Date(year, month - 1, day);

					// 计算选中日期与今天的差值
					const today = new Date();
					const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
					const selectedStart = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate());
					const diffTime = selectedStart - todayStart;
					const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

					// 如果选中的日期在30天范围内，设置对应的索引
					if (diffDays >= 0 && diffDays < 30) {
						this.currentDateIndex = diffDays;
					} else if (diffDays >= 0) {
						// 如果超出30天范围，重新生成以选中日期为起点的日期列表
						this.generateDateListFromDate(selectedDate);
						this.currentDateIndex = 0;
					}

					// 滚动到选中的日期位置
					this.$nextTick(() => {
						this.scrollToSelectedDate();
					});
				}
			} catch (error) {
				console.error('解析选中日期失败:', error);
				// 如果解析失败，默认选择今天
				this.currentDateIndex = 0;
			}
		},

		// 从指定日期开始生成日期列表
		generateDateListFromDate(startDate) {
			const dateList = [];

			for (let i = 0; i < 30; i++) {
				const date = new Date(startDate);
				date.setDate(startDate.getDate() + i);

				const weekIndex = date.getDay();
				const weekDay = this.weekDayNames[weekIndex];

				// 判断是否是今天
				const today = new Date();
				const isToday = date.getDate() === today.getDate() &&
					date.getMonth() === today.getMonth() &&
					date.getFullYear() === today.getFullYear();

				dateList.push({
					date: date,
					day: date.getDate(),
					weekDay: weekDay,
					isToday: isToday
				});
			}

			this.dateList = dateList;
		},

		// 滚动到选中的日期位置
		scrollToSelectedDate() {
			// 基于实际屏幕宽度计算滚动位置
			// rpx转px的比例：750rpx = screenWidth px
			const rpxToPx = this.screenWidth / 750;
			const itemWidthPx = this.dateItemWidth * rpxToPx; // 日期项的实际像素宽度

			// 计算可视区域能显示多少个完整的日期项
			const visibleItems = Math.floor(this.screenWidth / itemWidthPx);

			// 滚动策略：让选中的日期项显示在可视区域的合适位置
			if (this.currentDateIndex > 2) {
				// 计算目标位置：让选中项显示在可视区域的左侧1/4位置
				const targetVisiblePosition = Math.min(2, Math.floor(visibleItems / 4));
				const scrollDistance = (this.currentDateIndex - targetVisiblePosition) * itemWidthPx;
				this.scrollLeft = Math.max(0, Math.round(scrollDistance));
			} else {
				this.scrollLeft = 0;
			}

			console.log('滚动计算:', {
				currentDateIndex: this.currentDateIndex,
				screenWidth: this.screenWidth,
				itemWidthPx: itemWidthPx,
				visibleItems: visibleItems,
				scrollLeft: this.scrollLeft
			});
		},

		// 加载车票列表数据
		async loadTicketList() {
			try {
				this.loading = true;
				this.loadingText = '正在查询车票...';

				// 获取当前选择的日期
				const selectedDate = this.dateList[this.currentDateIndex];
				const departureTime = this.formatDate(selectedDate.date);

				// 构建请求参数
				const params = {
					carTime: departureTime,
					upAddress: this.upAddressId,
					downAddress: this.downAddressId
				};

				console.log('查询车票参数:', params);

				// 调用接口获取数据
				const response = await ticketApi.getTicketList(params);

				if (response && response.data) {
					// 处理接口返回的数据
					this.ticketList = this.processTicketData(response.data);
				} else {
					this.ticketList = [];
					uni.showToast({
						title: '暂无车票信息',
						icon: 'none'
					});
				}

			} catch (error) {
				console.error('获取车票列表失败:', error);
				this.ticketList = [];
				uni.showToast({
					title: '获取车票信息失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 处理接口返回的车票数据
		processTicketData(data) {
			if (!Array.isArray(data)) {
				return [];
			}

			return data.map(item => {
				return {
					// 根据后端接口字段映射到前端需要的字段
					time: this.extractTimeFromDateTime(item.departureTime), // 发车时间，提取时分
					type: this.getTicketType(item.vehicleType), // 车辆类型
					typeName: item.carType, // 车辆类型名称
					departureDoor: item.departureDoor , // 上车门
					duration: item.travelTime , // 行程时长
					originalPrice: parseFloat(item.originalPrice ), // 原价
					currentPrice: parseFloat(item.currentPrice || item.price ), // 现价
					discount: item.discountPrice, // 优惠金额
					discountCount: item.discountCount || 0, // 优惠数量
					status: this.getTicketStatus(item.ticketNumber), // 根据票数量显示状态
					isImmediateDeparture: item.isImmediateDeparture || false, // 是否即将发车
					// 保留原始数据以备后用
					originalData: item
				};
			});
		},

		// 根据车辆类型获取对应的样式类型
		getTicketType(vehicleType) {
			// 根据实际业务逻辑映射车辆类型
			switch (vehicleType) {
				case 1:
				case '商务车':
					return 'business';
				case 2:
				case '普通车':
				default:
					return 'normal';
			}
		},

		// 根据票数量获取票务状态
		getTicketStatus(ticketNumber) {
			// 将ticketNumber转换为数字，如果无效则默认为0
			const num = parseInt(ticketNumber) || 0;

			if (num >= 10) {
				return '有票';
			} else if (num > 0) {
				return `${num}张`;
			} else {
				return '无票';
			}
		},

		// 格式化日期为 yyyy-MM-dd 格式
		formatDate(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},

		// 从完整的日期时间字符串中提取时分
		extractTimeFromDateTime(dateTimeString) {
			if (!dateTimeString) {
				return '08:00'; // 默认时间
			}

			try {
				// 处理格式如 "2025-06-10 00:00:00" 或 "2025-06-10T00:00:00"
				let timeStr = '';

				if (dateTimeString.includes(' ')) {
					// 格式: "2025-06-10 00:00:00"
					timeStr = dateTimeString.split(' ')[1];
				} else if (dateTimeString.includes('T')) {
					// 格式: "2025-06-10T00:00:00"
					timeStr = dateTimeString.split('T')[1];
				} else {
					// 如果已经是时间格式，直接返回
					return dateTimeString;
				}

				// 提取时分部分 (HH:MM)
				if (timeStr && timeStr.length >= 5) {
					return timeStr.substring(0, 5); // 取前5位 "HH:MM"
				}

				return '08:00'; // 默认时间
			} catch (error) {
				console.error('解析发车时间失败:', dateTimeString, error);
				return '08:00'; // 默认时间
			}
		},

		// 显示日期选择器
		showDatePicker() {
			this.showCalendar = true;
		},

		// 取消日期选择
		cancelDatePicker() {
			this.showCalendar = false;
		},
		
		// 日历组件日期变化事件
		calendarChange(e) {
			if (e.year && e.month && e.date) {
				// 保存选中的日期信息
				this.selectedInfo = e;
				this.selectedDate = `${e.year}-${e.month}-${e.date}`;
				
				// 更新显示的年月
				this.currentYear = e.year;
				this.currentMonth = e.month;
			}
		},
		
		// 日历组件月份切换事件
		monthSwitch(e) {
			if (e.year && e.month) {
				this.currentYear = e.year;
				this.currentMonth = e.month;
			}
		},

		// 确认日期选择
		confirmDatePicker() {
			if (!this.selectedInfo) return;
			
			const { year, month, date } = this.selectedInfo;
			const selectedDate = new Date(year, month - 1, date);

			// 检查所选日期是否在当前显示的30天内
			const today = new Date();
			const diffTime = selectedDate - today;
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

			if (diffDays >= 0 && diffDays < 30) {
				// 如果在30天内，直接选中对应的日期
				this.currentDateIndex = diffDays;
			} else {
				// 如果不在30天内，需要重新生成日期列表
				const dateList = [];
				for (let i = 0; i < 30; i++) {
					const date = new Date(selectedDate);
					date.setDate(selectedDate.getDate() - diffDays + i);

					const weekIndex = date.getDay();
					const weekDay = this.weekDayNames[weekIndex];

					const isToday = date.getDate() === today.getDate() &&
						date.getMonth() === today.getMonth() &&
						date.getFullYear() === today.getFullYear();

					dateList.push({
						date: date,
						day: date.getDate(),
						weekDay: weekDay,
						isToday: isToday
					});
				}

				this.dateList = dateList;
				this.currentDateIndex = diffDays;
			}

			this.showCalendar = false;

			// 重新加载车票数据
			this.loadTicketList();
		},
		
		// 检查日期是否是今天
		isDateToday(date) {
			const today = new Date();
			return date.getDate() === today.getDate() && 
				   date.getMonth() === today.getMonth() && 
				   date.getFullYear() === today.getFullYear();
		},

		// 跳转到购票确认页面
		goToTicketConfirm(ticket) {
			// 检查票务状态，如果无票则提示并阻止跳转
			if (ticket.status === '无票') {
				uni.showToast({
					title: '该班次暂无余票',
					icon: 'none'
				});
				return;
			}

			// 获取车票ID，优先使用原始数据中的ID
			const ticketId = ticket.originalData?.id || ticket.id;

			if (!ticketId) {
				uni.showToast({
					title: '车票信息异常，请重新选择',
					icon: 'none'
				});
				return;
			}

			// 获取当前选择的日期
			const selectedDate = this.dateList[this.currentDateIndex];
			const month = selectedDate.date.getMonth() + 1;
			const day = selectedDate.date.getDate();
			const weekDay = selectedDate.weekDay;
			const today = new Date();
			const isTomorrow = day === today.getDate() + 1 && month === today.getMonth() + 1;
			const dateDesc = isTomorrow ? '明天' : `${month}月${day}日`;

			// 构建跳转参数，修正日期格式
			const monthStr = month < 10 ? '0' + month : month;
			const dayStr = day < 10 ? '0' + day : day;
			const formattedDate = `${monthStr}月${dayStr}日`;

			// 构建参数对象，主要传递车票ID和基本信息
			const params = {
				ticketId: ticketId, // 车票ID，用于接口查询
				departure: this.departureStation,
				arrival: this.arrivalStation,
				date: formattedDate,
				dateDesc: dateDesc,
				// 以下为备用数据，如果接口调用失败时使用
				departureDoor: ticket.departureDoor,
				time: ticket.time,
				duration: ticket.duration,
				typeName: ticket.typeName,
				type: ticket.type,
				originalPrice: ticket.originalPrice,
				currentPrice: ticket.currentPrice,
				discount: ticket.discount
			};

			// 使用URL参数传递车票ID
			const queryString = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
			const url = `/subpkg-booking/ticket_confirm/ticket_confirm?${queryString}`;

			uni.navigateTo({
				url: url
			});
		}
	}
}
</script>

<style scoped lang="scss">
page {
	background-color: #f5f7fa;
}

.content {
	width: 100%;
	min-height: 100vh;
	position: relative;
}

/* 日期选择器 */
.date-scroll {
	white-space: nowrap;
	background-color: #fff;
	position: relative;
	z-index: 9;
	height: 140rpx;
	border-bottom: 1px solid #eee;
	margin-top: 0;
	/* 调整顶部边距 */
}

.date-list {
	display: flex;
	padding: 0 30rpx;
	height: 100%;
	/* 添加滑动提示效果 */
	position: relative;
}

.date-list::after {
	content: '';
	position: absolute;
	right: 120rpx; /* 预留日历图标的位置 */
	top: 0;
	bottom: 0;
	width: 60rpx;
	background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.8));
	pointer-events: none; /* 确保不影响触摸事件 */
}

.date-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-width: 100rpx;
	height: 100%;
	padding: 0 10rpx;
}

.date-item-active {
	color: #3F8DF9;
}

.week-day {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
	margin-top: 20rpx;
}

.date-item-active .week-day {
	color: #3F8DF9;
}

.day-num {
	font-size: 32rpx;
	color: #333;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	margin-bottom: 20rpx;
}

.date-item-active .day-num {
	background-color: #3F8DF9;
	color: #fff;
}

.today-circle {
	background-color: #e6f0ff;
	color: #3F8DF9;
}

.date-item-active .today-circle {
	background-color: #3F8DF9;
	color: #fff;
}

/* 日历图标 */
.calendar-icon {
	position: absolute;
	right: 20rpx;
	top: 0;
	/* 调整顶部位置 */
	z-index: 11;
	width: 100rpx;
	height: 140rpx;
	background-color: #fff;
	box-shadow: -5rpx 0 10rpx rgba(0, 0, 0, 0.1); /* 只在左边添加阴影 */
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.calendar-img {
	width: 40rpx;
	height: 40rpx;
	display: block;
}

.arrow-img {
	width: 20rpx;
	height: 20rpx;
	margin-top: 6rpx;
}

/* 车票列表 */
.ticket-list {
	padding: 20rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 100rpx 0;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

/* 空状态 */
.empty-container {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 100rpx 0;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.empty-tip {
	font-size: 26rpx;
	color: #999;
}

.ticket-item {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx 20rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.08);
	position: relative;
}

/* 无票状态的禁用样式 */
.ticket-disabled {
	opacity: 0.6;
	background-color: #f5f5f5;
	pointer-events: none;
}

/* 移除商务车类型的左侧竖线 */
.business {
	position: relative;
}

/* 移除商务车的黄色竖线 */
.business::before {
	display: none;
}

/* 即将发车标签 - 左上角绝对定位 */
.departure-tag {
	position: absolute;
	top: 0;
	left: 0;
	font-size: 20rpx;
	color: #ff9500; /* 深黄色字体 */
	background: rgba(246,136,11,0.1); /* 浅黄色背景 */
	padding: 6rpx 12rpx;
	border-radius: 0 0 8rpx 0;
	line-height: 1;
	z-index: 2;
}

/* 主要内容区域 */
.ticket-content {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-top: 0;
}

.ticket-left {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	min-height: 100rpx;
	width: 120rpx;
}

/* 时间区域 */
.time-section {
	display: flex;
	flex-direction: column;
	margin-bottom: 24rpx;
}

.ticket-time {
	font-size: 44rpx;
	font-weight: bold;
	color: #333;
	line-height: 1;
	margin-bottom: 20rpx;
}

.ticket-duration {
	font-size: 25rpx;
	color: #999;
	line-height: 1;
}

.info-icon {
	color: #999; /* 修改为与字体相同颜色 */
	margin-left: 4rpx;
}

/* 站点信息 - 中间区域 */
.ticket-stations {
	display: flex;
	align-items: center;
	margin: 0 24rpx;
	flex: 1;
}

.station-line {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 20rpx;
	padding-top: 0; /* 移除上边距与站点名称对齐 */
}

.station-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	position: relative;
}

.start {
	background-color: #3F8DF9;
}

.end {
	background-color: #ff9500;
}

.station-connector {
	width: 2rpx;
	height: 50rpx; /* 增加连接线高度 */
	background-color: #ddd;
	margin: 6rpx 0;
}

.station-names {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	min-height: 108rpx;
}

.station-name {
	font-size: 30rpx; /* 增大字体 */
	font-weight: bold; /* 加粗 */
	color: #333;
	line-height: 38rpx;
}

/* 车票类型标签 */
.ticket-type-tag {
	align-self: flex-start;
	margin-top: 6rpx;
}

.type-text {
	font-size: 22rpx;
	font-weight: bold;
	color: #000000;
	padding: 6rpx 12rpx;
	border-radius: 6rpx;
	line-height: 1;
}

.ticket-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	justify-content: space-between;
	min-height: 100rpx;
	padding-left: 20rpx;
	width: 140rpx; /* 增加宽度 */
}

.price-box {
	text-align: right;
	margin-bottom: 20rpx;
	display: flex; /* 使价格在同一行 */
	align-items: baseline;
	justify-content: flex-end;
}

.price-original {
	font-size: 24rpx;
	color: #ccc;
	margin-right: 8rpx; /* 添加右侧间距 */
	line-height: 1;
}

.price-through {
	text-decoration: line-through;
}

.price-current {
	color: #ff9500;
	font-weight: bold;
	line-height: 1;
}

.price-symbol {
	font-size: 34rpx;
}

.price-value {
	font-size: 42rpx;
}

.discount-tag {
	font-size: 20rpx;
	color: #ff9500;
	background-color: #fff5e6;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
	margin-bottom: 30rpx;
	border: 1rpx solid #ffe0b3;
	line-height: 1;
}

.ticket-status {
	font-size: 26rpx;
	color: #999;
	line-height: 1;
}

/* 日历弹窗 */
.calendar-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
}

.calendar-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
}

.calendar-container {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	padding: 30rpx;
	animation: slideUp 0.3s ease;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}

	to {
		transform: translateY(0);
	}
}

.calendar-header {
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	margin-bottom: 20rpx;
}

.calendar-close {
	position: absolute;
	left: 0;
	font-size: 48rpx;
	color: #333;
	line-height: 1;
}

.calendar-nav {
	display: flex;
	align-items: center;
}

.calendar-title {
	font-size: 34rpx;
	font-weight: bold;
}

.calendar-footer {
	margin-top: 20rpx;
	padding-bottom: 20rpx;
}

.calendar-confirm {
	height: 90rpx;
	background-color: #333;
	color: #fff;
	border-radius: 45rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 32rpx;
}

/* 自定义uni-calendar样式 */
.custom-calendar {
	--calendar-border-color: #f5f5f5;
	--calendar-text-color: #333;
	--calendar-lunar-color: #999;
	--calendar-background-color: #fff;
	--calendar-selected-background-color: #3F8DF9;
	--calendar-selected-lunar-color: #fff;
	--calendar-selected-text-color: #fff;
}

/* uni-calendar组件样式修改 */
:deep(.uni-calendar) {
	background-color: #ffffff;
}

:deep(.uni-calendar__header) {
	display: none !important;
}

:deep(.uni-calendar__weeks) {
	padding: 10rpx 0;
}

:deep(.uni-calendar__weeks-day) {
	height: 90rpx;
}

:deep(.uni-calendar__weeks-day-text) {
	font-size: 30rpx;
	color: #333;
}

:deep(.uni-calendar__selected) {
	background-color: #3F8DF9;
	color: #fff;
	border-radius: 50%;
	width: 70rpx;
	height: 70rpx;
	line-height: 70rpx;
	text-align: center;
}

:deep(.uni-calendar__disabled) {
	color: #ccc;
	cursor: default;
}

:deep(.uni-calendar-item--disable) {
	color: #ccc;
	cursor: default;
}

:deep(.uni-calendar-item--before-checked), 
:deep(.uni-calendar-item--after-checked) {
	background-color: rgba(63, 141, 249, 0.1);
	color: #333;
}

:deep(.uni-calendar__week-day) {
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	font-size: 28rpx;
	color: #333;
}

:deep(.uni-calendar__week-day-text) {
	color: #333;
}

:deep(.uni-calendar__weeks-day-text) {
	color: #333;
}

:deep(.uni-calendar-item__weeks-box-circle) {
	border: 2rpx solid #3F8DF9;
	color: #3F8DF9;
	border-radius: 50%;
}

/* 字体图标 */
@font-face {
	font-family: "iconfont";
	src: url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAALEAAsAAAAABpAAAAJ3AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCCcAqBDIERATYCJAMICwYABCAFhG0HMhvZBREVjDWyHwluhGgUUkk5s7uXKwRBUP1Y9v28mCBJNkk0iSSaJBqkE6FJhEIlFO+Be+7tBhLIXC9JK8STaGSSbJ7sj/6fY6bL58PyW5sDyg0cUF/QF8QBF+D+gnaDbsRhXCcwaNEszkbW7qNGJCtgXCCe1DZGkhlZaMmQN4RW09Ki2ChCo5de04CN4ffxj4qGJGkKWLBx95KZ0P6r+NlKU/9vXRpRAj2cQYoFCowDMnFUG9ovMRg+MPCCS2UEePXqFfxsvef/H48AlVZVIVv939OMPQRRgJ+trfSQGPyqeAkBwJB5UDh7uysSDIZMoUcxDBgsWkJnSHbqgFfwzdHMnD29vdKxE2bWLJzp7jZn9iYTA7pi9R9Sp/768F7Lw/ttt2/Xj8ObUT4+xm8WjB8/4/zIR6ejp1f3ro8f5rWFz5/xq3qv3v+KNnH63PTJk3cO9Z07j24/ka+Pnz4pNA2g9gv48ysguw0wWVYc+vuPrln794DX1Qs1IKuWD7wII/5Zf4PJh3+vLWQtf5kVgDUoesEXRx9imYJBBvAPh+2+SrJflhy2xE6QdFqBoNcagyxvHKXIcVDrtQrNnLPRg07r8x6jRJAlF0BvzDBIhpyCYsgNZlneolTpPdSGAoNBb9G2sMsUqYyImCEHFj+QVe0RmSInWfkGiXhNSBmpSbl4QXwRI4ZM54TYqIqseYZUxm0kggyxSBWxJi6hNBkRaa7oQukmYkW5PNLKmCOyHsWQAww+QKyU5iFmFHFK+fsGItFaI9TBw6n8BYTXwuGDjNaGmDGqJeLbyVA9MQ+JgBhEhVARzCacBEWjrD/IXCLnkbqJLZ1UvZZ9vdf4W14BB7CXliRp0pEOLM50o44GAAA=');
}
</style>