<template>
	<view class="content">
		<!-- 姓名 -->
		<view class="form-item">
			<view class="form-label">姓名</view>
			<input class="form-input" v-model="name" placeholder="请输入姓名" placeholder-style="color: #cccccc;" />
		</view>

		<!-- 证件类型 -->
		<view class="form-item">
			<view class="form-label">证件类型</view>
			<view class="form-value">身份证</view>
		</view>

		<!-- 证件号 -->
		<view class="form-item last-item">
			<view class="form-label">证件号</view>
			<input class="form-input" v-model="idCard" placeholder="请输入证件号" placeholder-style="color: #cccccc;" type="idcard" maxlength="18" />
		</view>

		<!-- 提示信息 -->
		<view class="tips">
			<text class="tips-text">提示：点击保存表示您已阅读并同意以下内容</text>
			<text class="tips-text">您知悉您录入的乘车人身份证件信息，将用于您的汽车票等所有需要实名制的产品，并在使用时进行验证，请确保此信息真实有效。</text>
		</view>

		<!-- 保存按钮 -->
		<view class="save-button" :class="{'save-button-no-delete': !isEdit}" hover-class="button-hover" @click="savePassenger">保存乘客</view>

		<!-- 删除按钮 - 只在编辑模式下显示 -->
		<view v-if="isEdit" class="delete-button" hover-class="delete-hover" @click="deletePassenger">删除信息</view>
	</view>
</template>

<script>
import request from '../../utils/request.js';

export default {
	data() {
		return {
			index: -1, // 乘客在列表中的索引，-1表示新增
			id: '', // 乘客ID，用于区分新增和编辑
			name: '', // 乘客姓名
			idCard: '', // 证件号码
			isEdit: false // 是否是编辑模式
		};
	},
	onLoad() {
		// 尝试获取需要编辑的乘客数据
		const eventChannel = this.getOpenerEventChannel();
		eventChannel.on('acceptPassengerData', (data) => {
			console.log('接收到乘客数据:', data);
			if (data && data.passenger) {
				this.index = data.index;
				this.name = data.passenger.name;
				
				// 获取乘客ID
				if (data.passenger.id) {
					this.id = data.passenger.id;
				}
				
				// 使用传递过来的完整身份证号
				if (data.passenger.idNumber) {
					this.idCard = data.passenger.idNumber;
				} else if (data.passenger.rawIdNumber) {
					this.idCard = data.passenger.rawIdNumber;
				} else if (data.passenger.idCard) {
					// 兼容旧数据，尝试移除星号
					this.idCard = data.passenger.idCard.replace(/\*/g, '');
				}
				
				this.isEdit = true;
			}
		});
		
		// 设置导航栏标题
		this.setNavigationTitle();
	},
	onShow() {
		// 每次页面显示时设置导航栏标题
		this.setNavigationTitle();
	},
	methods: {
		// 设置导航栏标题
		setNavigationTitle() {
			uni.setNavigationBarTitle({
				title: this.isEdit ? '编辑乘客' : '添加乘客'
			});
		},
		
		// 保存乘客信息
		savePassenger() {
			// 表单验证
			if (!this.name.trim()) {
				uni.showToast({
					title: '请输入姓名',
					icon: 'none'
				});
				return;
			}
			
			if (!this.idCard.trim()) {
				uni.showToast({
					title: '请输入证件号',
					icon: 'none'
				});
				return;
			}
			
			// 身份证号码验证（简单验证，实际应用中应使用更严格的验证）
			const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
			if (!idCardReg.test(this.idCard)) {
				uni.showToast({
					title: '请输入正确的身份证号',
					icon: 'none'
				});
				return;
			}
			
			// 统一保存到服务器
			this.saveToServer();
		},
		
		// 保存乘客信息到服务器（统一处理新增和编辑）
		saveToServer() {
			uni.showLoading({
				title: '保存中...'
			});
			
			// 准备请求数据
			const requestData = {
				name: this.name,
				idNumber: this.idCard
			};
			
			// 如果有ID，则是编辑模式，需要将ID添加到请求数据中
			if (this.id) {
				requestData.id = this.id;
			}
			
			const url = '/app/information';
			const method = this.id ? 'put' : 'post'; // 有ID使用PUT，无ID使用POST
			const actionText = this.id ? '编辑' : '添加';
			
			console.log(`准备${actionText}乘客信息:`, {
				url,
				method,
				data: requestData
			});
			
			// 使用对应的请求方法
			request[method](url, requestData)
				.then(res => {
					uni.hideLoading();
					console.log(`${actionText}乘客成功，响应数据:`, res);
					
					// 兼容不同的后端接口响应格式
					let success = true;
					if (res.code !== undefined && res.code !== 0 && res.code !== 200) {
						success = false;
					}
					
					if (success) {
						// 接口调用成功
						uni.showToast({
							title: `${actionText}成功`,
							icon: 'success'
						});
						
						// 构建乘客数据对象
						const passenger = {
							id: this.id || (res.data && res.data.id), // 保存返回的ID或现有ID
							name: this.name,
							idCard: this.formatIdCard(this.idCard), // 显示格式化身份证号
							rawIdNumber: this.idCard, // 保存完整身份证号
							selected: true
						};
						
						// 返回上一页并传递数据
						const pages = getCurrentPages();
						const prevPage = pages[pages.length - 2]; // 上一页
						
						if (this.isEdit) {
							// 编辑模式：更新已有乘客
							prevPage.$vm.updatePassenger(this.index, passenger);
						} else {
							// 新增模式：添加新乘客
							prevPage.$vm.addNewPassenger(passenger);
						}
						
						setTimeout(() => {
							uni.navigateBack();
						}, 500);
					} else {
						// API返回了业务错误
						let errorMsg = `${actionText}失败，请重试`;
						if (res.msg) {
							errorMsg = res.msg;
						} else if (res.message) {
							errorMsg = res.message;
						}
						
						uni.showModal({
							title: `${actionText}失败`,
							content: errorMsg,
							confirmText: '重试',
							cancelText: '取消',
							success: (result) => {
								if (result.confirm) {
									// 用户点击重试
									this.saveToServer();
								}
							}
						});
					}
				})
				.catch(err => {
					uni.hideLoading();
					
					console.error(`${actionText}乘客失败，详细错误:`, err);
					
					// 显示更具体的错误信息
					let errorMsg = '网络异常，请重试';
					if (err && err.msg) {
						errorMsg = err.msg;
					} else if (err && err.errMsg) {
						errorMsg = err.errMsg;
					}
					
					// 显示错误信息并提供重试选项
					uni.showModal({
						title: `${actionText}失败`,
						content: errorMsg,
						confirmText: '重试',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								// 用户点击重试
								this.saveToServer();
							}
						}
					});
				});
		},
		
		// 删除乘客信息
		deletePassenger() {
			// 显示确认对话框
			uni.showModal({
				title: '提示',
				content: '确定要删除此乘客信息吗？',
				success: (res) => {
					if (res.confirm) {
						if (this.id) {
							// 如果有ID，调用删除接口
							this.deleteFromServer();
						} else {
							// 否则直接从本地删除
							this.deleteFromLocal();
						}
					}
				}
			});
		},
		
		// 从服务器删除乘客
		deleteFromServer() {
			uni.showLoading({
				title: '删除中...'
			});
			
			request.delete(`/app/information/${this.id}`)
				.then(res => {
					uni.hideLoading();
					console.log('删除乘客成功，响应数据:', res);
					
					// 兼容不同的后端接口响应格式
					let success = true;
					if (res.code !== undefined && res.code !== 0 && res.code !== 200) {
						success = false;
					}
					
					if (success) {
						// 接口调用成功
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
						
						// 从本地删除
						this.deleteFromLocal();
					} else {
						// API返回了业务错误
						let errorMsg = '删除失败，请重试';
						if (res.msg) {
							errorMsg = res.msg;
						} else if (res.message) {
							errorMsg = res.message;
						}
						
						uni.showModal({
							title: '删除失败',
							content: errorMsg,
							confirmText: '重试',
							cancelText: '取消',
							success: (result) => {
								if (result.confirm) {
									// 用户点击重试
									this.deleteFromServer();
								}
							}
						});
					}
				})
				.catch(err => {
					uni.hideLoading();
					
					console.error('删除乘客失败，详细错误:', err);
					
					// 显示更具体的错误信息
					let errorMsg = '网络异常，请重试';
					if (err && err.msg) {
						errorMsg = err.msg;
					} else if (err && err.errMsg) {
						errorMsg = err.errMsg;
					}
					
					// 显示错误信息并提供重试选项
					uni.showModal({
						title: '删除失败',
						content: errorMsg,
						confirmText: '重试',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								// 用户点击重试
								this.deleteFromServer();
							}
						}
					});
				});
		},
		
		// 从本地删除乘客
		deleteFromLocal() {
			// 返回上一页并执行删除
			const pages = getCurrentPages();
			const prevPage = pages[pages.length - 2]; // 上一页
			prevPage.$vm.removePassenger(this.index);
			uni.navigateBack();
		},
		
		// 格式化身份证号（中间部分用*号代替）
		formatIdCard(idCard) {
			if (idCard.length >= 18) {
				return idCard.substr(0, 4) + '**********' + idCard.substr(14);
			} else if (idCard.length >= 15) {
				return idCard.substr(0, 4) + '*******' + idCard.substr(11);
			}
			return idCard;
		}
	}
}
</script>

<style lang="scss" scoped>
page {
	background-color: #f5f7fa;
}

.content {
	padding: 20rpx;
	padding-top: 30rpx;
}

.form-item {
	background-color: #fff;
	padding: 30rpx 24rpx;
	margin-bottom: 2rpx;
	display: flex;
	align-items: center;
	position: relative;
}

.form-item::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 24rpx;
	right: 24rpx;
	height: 1rpx;
	background-color: #f0f0f0;
}

.form-item:last-child::after {
	display: none;
}

.form-item:first-child {
	border-radius: 16rpx 16rpx 0 0;
}

.last-item {
	border-radius: 0 0 16rpx 16rpx;
	margin-bottom: 40rpx;
}

.form-label {
	width: 180rpx;
	font-size: 32rpx;
	color: #333;
	font-weight: 400;
}

.form-input {
	flex: 1;
	font-size: 32rpx;
	color: #333;
	height: 60rpx;
}

.form-value {
	flex: 1;
	font-size: 32rpx;
	color: #333;
}

.tips {
	padding: 0 24rpx;
	margin-bottom: 80rpx;
}

.tips-text {
	display: block;
	font-size: 26rpx;
	color: #999;
	line-height: 1.6;
	margin-bottom: 10rpx;
}

.save-button {
	margin: 0 24rpx;
	height: 90rpx;
	background-color: #3F8DF9;
	color: #fff;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 45rpx;
	box-shadow: 0 4rpx 12rpx rgba(63, 141, 249, 0.3);
}

.save-button-no-delete {
	margin-bottom: 80rpx;
}

.delete-button {
	margin: 30rpx 24rpx;
	height: 90rpx;
	background-color: #fff;
	color: #FF3B30;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 45rpx;
	border: 1rpx solid rgba(255, 59, 48, 0.6);
}

/* 按钮点击状态 */
.button-hover {
	opacity: 0.9;
	transform: scale(0.98);
}

.delete-hover {
	background-color: #FFF5F5;
	color: #FF3B30;
}
</style> 