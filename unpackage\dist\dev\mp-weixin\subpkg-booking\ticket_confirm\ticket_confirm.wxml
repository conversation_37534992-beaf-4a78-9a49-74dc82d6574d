<view class="content data-v-466fd826"><block wx:if="{{loading}}"><view class="loading-container data-v-466fd826"><view class="loading-text data-v-466fd826">{{loadingText}}</view></view></block><block wx:else><view class="trip-info data-v-466fd826"><view class="date-time data-v-466fd826">{{date+" "+dateDesc+" "+time+"出发 (约"+duration+"小时)"}}</view><view class="route-info data-v-466fd826"><view class="route-stations data-v-466fd826"><text class="route-from data-v-466fd826">{{departure+"-"+departureDoor}}</text><text class="route-arrow data-v-466fd826">→</text><text class="route-to data-v-466fd826">{{arrival}}</text></view><view class="bus-type data-v-466fd826">{{typeName}}</view></view><view class="trip-details data-v-466fd826"><view data-event-opts="{{[['tap',[['showAddressDetail',['$event']]]]]}}" class="trip-detail-item data-v-466fd826" bindtap="__e"><view class="detail-info data-v-466fd826"><text class="detail-label data-v-466fd826">ⓘ 发车前24小时不可退票、改签</text><text class="detail-label data-v-466fd826" style="text-align:right;padding-right:10rpx;">{{departureLocation}}</text></view></view></view></view></block><view class="passenger-section data-v-466fd826"><view class="section-title data-v-466fd826">选择乘客</view><view class="passenger-list data-v-466fd826"><block wx:for="{{passengerList}}" wx:for-item="passenger" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectPassenger',[index]]]]]}}" class="passenger-item data-v-466fd826" bindtap="__e"><view class="passenger-checkbox data-v-466fd826"><view class="{{['data-v-466fd826','checkbox-circle',[(passenger.selected)?'checkbox-active':'']]}}"><block wx:if="{{passenger.selected}}"><view class="checkbox-inner data-v-466fd826"></view></block></view></view><view class="passenger-info data-v-466fd826"><text class="passenger-name data-v-466fd826">{{passenger.name}}</text><text class="passenger-id data-v-466fd826">{{passenger.idCard}}</text><text class="passenger-type-text data-v-466fd826">{{passenger.type}}</text></view><view data-event-opts="{{[['tap',[['editPassenger',[index]]]]]}}" class="passenger-arrow data-v-466fd826" catchtap="__e"><image class="arrow-icon data-v-466fd826" src="/static/icons/arrow_right.png"></image></view></view></block><view data-event-opts="{{[['tap',[['goToPassengerManagement',['$event']]]]]}}" class="add-passenger data-v-466fd826" bindtap="__e"><image class="add-icon data-v-466fd826" src="/static/icons/add_circle.png"></image><text class="add-text data-v-466fd826">选择乘客</text></view></view><view class="passenger-phone data-v-466fd826"><view class="phone-label data-v-466fd826">乘客电话<text class="required data-v-466fd826"></text></view><input class="phone-input data-v-466fd826" type="number" placeholder="请输入联系电话" maxlength="11" data-event-opts="{{[['input',[['__set_model',['','contactPhone','$event',[]]]]]]}}" value="{{contactPhone}}" bindinput="__e"/></view><view class="remark-section data-v-466fd826"><view class="remark-label data-v-466fd826">备注</view><input class="remark-input data-v-466fd826" placeholder="有其它特定要求请备注（选填）" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e"/></view></view><view class="price-section data-v-466fd826"><view class="price-item data-v-466fd826"><text class="price-label data-v-466fd826">车票总价</text><text class="price-value data-v-466fd826">{{"¥"+$root.g0}}</text></view><view class="price-item data-v-466fd826"><text class="price-label data-v-466fd826">厂商优惠</text><text class="price-discount data-v-466fd826">{{"减¥"+$root.g1}}</text></view><view data-event-opts="{{[['tap',[['selectCoupon',['$event']]]]]}}" class="price-item coupon-item data-v-466fd826" bindtap="__e"><text class="price-label data-v-466fd826">优惠券</text><view class="coupon-select data-v-466fd826"><block wx:if="{{!selectedCoupon}}"><text class="coupon-text data-v-466fd826">选择优惠券</text></block><block wx:else><text class="coupon-selected data-v-466fd826">{{"-¥"+$root.g2}}</text></block><image class="arrow-icon data-v-466fd826" src="/static/icons/arrow_right.png"></image></view></view><view class="total-price data-v-466fd826"><text class="total-label data-v-466fd826">合计</text><text class="total-value data-v-466fd826">{{"¥"+$root.g3}}</text></view></view><view class="payment-bar data-v-466fd826"><view class="terms-section data-v-466fd826"><view data-event-opts="{{[['tap',[['toggleTerms',['$event']]]]]}}" class="terms-checkbox data-v-466fd826" bindtap="__e"><view class="{{['data-v-466fd826','checkbox-circle',[(termsAgreed)?'checkbox-active':'']]}}"><block wx:if="{{termsAgreed}}"><view class="checkbox-inner data-v-466fd826"></view></block></view></view><text class="terms-text data-v-466fd826">我已阅读并同意</text><text data-event-opts="{{[['tap',[['viewTerms',['$event']]]]]}}" class="terms-link data-v-466fd826" catchtap="__e">《购票须知》</text></view><view class="payment-total data-v-466fd826"><text class="payment-label data-v-466fd826">共计</text><text class="payment-currency data-v-466fd826">¥</text><text class="payment-value data-v-466fd826">{{$root.g4}}</text></view><view data-event-opts="{{[['tap',[['confirmPayment',['$event']]]]]}}" class="payment-button data-v-466fd826" bindtap="__e">立即支付</view></view><block wx:if="{{showCouponPopup}}"><view class="coupon-popup data-v-466fd826"><view data-event-opts="{{[['tap',[['closeCouponPopup',['$event']]]]]}}" class="coupon-mask data-v-466fd826" bindtap="__e"></view><view class="coupon-container data-v-466fd826"><view class="coupon-header data-v-466fd826"><text data-event-opts="{{[['tap',[['closeCouponPopup',['$event']]]]]}}" class="coupon-close data-v-466fd826" bindtap="__e">×</text><text class="coupon-title data-v-466fd826">使用优惠券</text></view><view class="coupon-list data-v-466fd826"><block wx:for="{{couponList}}" wx:for-item="coupon" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['chooseCoupon',[index]]]]]}}" class="coupon-item data-v-466fd826" bindtap="__e"><view class="coupon-content data-v-466fd826"><view class="coupon-amount data-v-466fd826"><text class="coupon-currency data-v-466fd826">¥</text><text class="coupon-value data-v-466fd826">{{coupon.amount}}</text><block wx:if="{{coupon.condition}}"><text class="coupon-condition data-v-466fd826">{{coupon.condition}}</text></block></view><view class="coupon-info data-v-466fd826"><text class="coupon-type data-v-466fd826">{{coupon.type}}</text><text class="coupon-expire data-v-466fd826">{{"有效期至："+coupon.expireDate}}</text></view><view class="{{['coupon-select-icon','data-v-466fd826',(coupon.selected)?'selected':'']}}"><block wx:if="{{coupon.selected}}"><view class="select-inner data-v-466fd826"></view></block></view></view><view class="coupon-edge-left data-v-466fd826"></view><view class="coupon-edge-right data-v-466fd826"></view></view></block></view><view class="coupon-footer data-v-466fd826"><view data-event-opts="{{[['tap',[['confirmCoupon',['$event']]]]]}}" class="coupon-confirm-btn data-v-466fd826" bindtap="__e">确认</view></view></view></view></block></view>