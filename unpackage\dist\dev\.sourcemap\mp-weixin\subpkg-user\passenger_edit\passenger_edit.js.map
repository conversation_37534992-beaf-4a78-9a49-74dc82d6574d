{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/subpkg-user/passenger_edit/passenger_edit.vue?bc3f", "webpack:///E:/购票系统/购票系统/subpkg-user/passenger_edit/passenger_edit.vue?b62f", "webpack:///E:/购票系统/购票系统/subpkg-user/passenger_edit/passenger_edit.vue?690a", "webpack:///E:/购票系统/购票系统/subpkg-user/passenger_edit/passenger_edit.vue?cc73", "uni-app:///subpkg-user/passenger_edit/passenger_edit.vue", "webpack:///E:/购票系统/购票系统/subpkg-user/passenger_edit/passenger_edit.vue?6830"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "data", "index", "id", "name", "idCard", "isEdit", "onLoad", "eventChannel", "console", "onShow", "methods", "setNavigationTitle", "uni", "title", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon", "saveToServer", "idNumber", "requestData", "url", "method", "request", "then", "success", "rawIdNumber", "selected", "prevPage", "setTimeout", "errorMsg", "content", "confirmText", "cancelText", "catch", "delete<PERSON><PERSON><PERSON><PERSON>", "deleteFromServer", "deleteFromLocal", "formatIdCard"], "mappings": "oKAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,gDACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCRvB,yHAA+qB,eAAG,G,2HCmClrB,e,EAEA,CACAC,gBACA,OACAC,SACAC,MACAC,QACAC,UACAC,YAGAC,kBAAA,WAEA,+BACAC,wCACAC,0BACA,iBACA,gBACA,wBAGA,iBACA,qBAIA,qBACA,8BACA,wBACA,iCACA,qBAEA,+CAGA,gBAKA,2BAEAC,kBAEA,2BAEAC,SAEAC,8BACAC,yBACAC,mCAKAC,yBAEA,oBAQA,uBASA,iDACA,oBASA,oBARAF,aACAC,mBACAE,mBAZAH,aACAC,eACAE,mBAVAH,aACAC,cACAE,eA4BAC,wBAAA,WACAJ,eACAC,iBAIA,OACAV,eACAc,sBAIA,UACAC,cAGA,yBACA,uBACA,oBAEAV,oCACAW,MACAC,SACApB,SAIAqB,kBACAC,kBACAV,gBACAJ,yCAGA,SAKA,QAJA,uCACAe,MAGA,GAEAX,aACAC,wBACAE,iBAIA,OACAb,2BACAC,YACAC,gCACAoB,qBACAC,aAIA,oBACA,gBAEA,SAEAC,iCAGAA,yBAGAC,uBACAf,mBACA,SACA,CAEA,4BACA,MACAgB,QACA,YACAA,aAGAhB,aACAC,wBACAgB,UACAC,iBACAC,gBACAR,oBACA,WAEA,wBAMAS,mBACApB,gBAEAJ,2CAGA,iBACA,SACAoB,QACA,cACAA,YAIAhB,aACAC,wBACAgB,UACAC,iBACAC,gBACAR,oBACA,WAEA,wBAQAU,2BAAA,WAEArB,aACAC,WACAgB,uBACAN,oBACA,YACA,KAEA,qBAGA,yBAQAW,4BAAA,WACAtB,eACAC,iBAGAQ,sDACAC,kBACAV,gBACAJ,8BAGA,SAKA,QAJA,uCACAe,MAGA,EAEAX,aACAC,aACAE,iBAIA,wBACA,CAEA,iBACA,MACAa,QACA,YACAA,aAGAhB,aACAC,aACAgB,UACAC,iBACAC,gBACAR,oBACA,WAEA,4BAMAS,mBACApB,gBAEAJ,gCAGA,iBACA,SACAoB,QACA,cACAA,YAIAhB,aACAC,aACAgB,UACAC,iBACAC,gBACAR,oBACA,WAEA,4BAQAY,2BAEA,wBACA,gBACAT,kCACAd,kBAIAwB,yBACA,oBACA,wCACA,aACA,qCAEA,KAGA,c,6DC1WA,yHAA8xC,eAAG,G", "file": "subpkg-user/passenger_edit/passenger_edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg-user/passenger_edit/passenger_edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./passenger_edit.vue?vue&type=template&id=37c11de4&scoped=true&\"\nvar renderjs\nimport script from \"./passenger_edit.vue?vue&type=script&lang=js&\"\nexport * from \"./passenger_edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./passenger_edit.vue?vue&type=style&index=0&id=37c11de4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"37c11de4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg-user/passenger_edit/passenger_edit.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./passenger_edit.vue?vue&type=template&id=37c11de4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./passenger_edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./passenger_edit.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<!-- 姓名 -->\r\n\t\t<view class=\"form-item\">\r\n\t\t\t<view class=\"form-label\">姓名</view>\r\n\t\t\t<input class=\"form-input\" v-model=\"name\" placeholder=\"请输入姓名\" placeholder-style=\"color: #cccccc;\" />\r\n\t\t</view>\r\n\r\n\t\t<!-- 证件类型 -->\r\n\t\t<view class=\"form-item\">\r\n\t\t\t<view class=\"form-label\">证件类型</view>\r\n\t\t\t<view class=\"form-value\">身份证</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 证件号 -->\r\n\t\t<view class=\"form-item last-item\">\r\n\t\t\t<view class=\"form-label\">证件号</view>\r\n\t\t\t<input class=\"form-input\" v-model=\"idCard\" placeholder=\"请输入证件号\" placeholder-style=\"color: #cccccc;\" type=\"idcard\" maxlength=\"18\" />\r\n\t\t</view>\r\n\r\n\t\t<!-- 提示信息 -->\r\n\t\t<view class=\"tips\">\r\n\t\t\t<text class=\"tips-text\">提示：点击保存表示您已阅读并同意以下内容</text>\r\n\t\t\t<text class=\"tips-text\">您知悉您录入的乘车人身份证件信息，将用于您的汽车票等所有需要实名制的产品，并在使用时进行验证，请确保此信息真实有效。</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 保存按钮 -->\r\n\t\t<view class=\"save-button\" :class=\"{'save-button-no-delete': !isEdit}\" hover-class=\"button-hover\" @click=\"savePassenger\">保存乘客</view>\r\n\r\n\t\t<!-- 删除按钮 - 只在编辑模式下显示 -->\r\n\t\t<view v-if=\"isEdit\" class=\"delete-button\" hover-class=\"delete-hover\" @click=\"deletePassenger\">删除信息</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport request from '../../utils/request.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tindex: -1, // 乘客在列表中的索引，-1表示新增\r\n\t\t\tid: '', // 乘客ID，用于区分新增和编辑\r\n\t\t\tname: '', // 乘客姓名\r\n\t\t\tidCard: '', // 证件号码\r\n\t\t\tisEdit: false // 是否是编辑模式\r\n\t\t};\r\n\t},\r\n\tonLoad() {\r\n\t\t// 尝试获取需要编辑的乘客数据\r\n\t\tconst eventChannel = this.getOpenerEventChannel();\r\n\t\teventChannel.on('acceptPassengerData', (data) => {\r\n\t\t\tconsole.log('接收到乘客数据:', data);\r\n\t\t\tif (data && data.passenger) {\r\n\t\t\t\tthis.index = data.index;\r\n\t\t\t\tthis.name = data.passenger.name;\r\n\t\t\t\t\r\n\t\t\t\t// 获取乘客ID\r\n\t\t\t\tif (data.passenger.id) {\r\n\t\t\t\t\tthis.id = data.passenger.id;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 使用传递过来的完整身份证号\r\n\t\t\t\tif (data.passenger.idNumber) {\r\n\t\t\t\t\tthis.idCard = data.passenger.idNumber;\r\n\t\t\t\t} else if (data.passenger.rawIdNumber) {\r\n\t\t\t\t\tthis.idCard = data.passenger.rawIdNumber;\r\n\t\t\t\t} else if (data.passenger.idCard) {\r\n\t\t\t\t\t// 兼容旧数据，尝试移除星号\r\n\t\t\t\t\tthis.idCard = data.passenger.idCard.replace(/\\*/g, '');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.isEdit = true;\r\n\t\t\t}\r\n\t\t});\r\n\t\t\r\n\t\t// 设置导航栏标题\r\n\t\tthis.setNavigationTitle();\r\n\t},\r\n\tonShow() {\r\n\t\t// 每次页面显示时设置导航栏标题\r\n\t\tthis.setNavigationTitle();\r\n\t},\r\n\tmethods: {\r\n\t\t// 设置导航栏标题\r\n\t\tsetNavigationTitle() {\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: this.isEdit ? '编辑乘客' : '添加乘客'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 保存乘客信息\r\n\t\tsavePassenger() {\r\n\t\t\t// 表单验证\r\n\t\t\tif (!this.name.trim()) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入姓名',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (!this.idCard.trim()) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入证件号',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 身份证号码验证（简单验证，实际应用中应使用更严格的验证）\r\n\t\t\tconst idCardReg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n\t\t\tif (!idCardReg.test(this.idCard)) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入正确的身份证号',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 统一保存到服务器\r\n\t\t\tthis.saveToServer();\r\n\t\t},\r\n\t\t\r\n\t\t// 保存乘客信息到服务器（统一处理新增和编辑）\r\n\t\tsaveToServer() {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '保存中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 准备请求数据\r\n\t\t\tconst requestData = {\r\n\t\t\t\tname: this.name,\r\n\t\t\t\tidNumber: this.idCard\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 如果有ID，则是编辑模式，需要将ID添加到请求数据中\r\n\t\t\tif (this.id) {\r\n\t\t\t\trequestData.id = this.id;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst url = '/app/information';\r\n\t\t\tconst method = this.id ? 'put' : 'post'; // 有ID使用PUT，无ID使用POST\r\n\t\t\tconst actionText = this.id ? '编辑' : '添加';\r\n\t\t\t\r\n\t\t\tconsole.log(`准备${actionText}乘客信息:`, {\r\n\t\t\t\turl,\r\n\t\t\t\tmethod,\r\n\t\t\t\tdata: requestData\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 使用对应的请求方法\r\n\t\t\trequest[method](url, requestData)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.log(`${actionText}乘客成功，响应数据:`, res);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 兼容不同的后端接口响应格式\r\n\t\t\t\t\tlet success = true;\r\n\t\t\t\t\tif (res.code !== undefined && res.code !== 0 && res.code !== 200) {\r\n\t\t\t\t\t\tsuccess = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (success) {\r\n\t\t\t\t\t\t// 接口调用成功\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: `${actionText}成功`,\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 构建乘客数据对象\r\n\t\t\t\t\t\tconst passenger = {\r\n\t\t\t\t\t\t\tid: this.id || (res.data && res.data.id), // 保存返回的ID或现有ID\r\n\t\t\t\t\t\t\tname: this.name,\r\n\t\t\t\t\t\t\tidCard: this.formatIdCard(this.idCard), // 显示格式化身份证号\r\n\t\t\t\t\t\t\trawIdNumber: this.idCard, // 保存完整身份证号\r\n\t\t\t\t\t\t\tselected: true\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 返回上一页并传递数据\r\n\t\t\t\t\t\tconst pages = getCurrentPages();\r\n\t\t\t\t\t\tconst prevPage = pages[pages.length - 2]; // 上一页\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (this.isEdit) {\r\n\t\t\t\t\t\t\t// 编辑模式：更新已有乘客\r\n\t\t\t\t\t\t\tprevPage.$vm.updatePassenger(this.index, passenger);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 新增模式：添加新乘客\r\n\t\t\t\t\t\t\tprevPage.$vm.addNewPassenger(passenger);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// API返回了业务错误\r\n\t\t\t\t\t\tlet errorMsg = `${actionText}失败，请重试`;\r\n\t\t\t\t\t\tif (res.msg) {\r\n\t\t\t\t\t\t\terrorMsg = res.msg;\r\n\t\t\t\t\t\t} else if (res.message) {\r\n\t\t\t\t\t\t\terrorMsg = res.message;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: `${actionText}失败`,\r\n\t\t\t\t\t\t\tcontent: errorMsg,\r\n\t\t\t\t\t\t\tconfirmText: '重试',\r\n\t\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\t\tsuccess: (result) => {\r\n\t\t\t\t\t\t\t\tif (result.confirm) {\r\n\t\t\t\t\t\t\t\t\t// 用户点击重试\r\n\t\t\t\t\t\t\t\t\tthis.saveToServer();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.error(`${actionText}乘客失败，详细错误:`, err);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示更具体的错误信息\r\n\t\t\t\t\tlet errorMsg = '网络异常，请重试';\r\n\t\t\t\t\tif (err && err.msg) {\r\n\t\t\t\t\t\terrorMsg = err.msg;\r\n\t\t\t\t\t} else if (err && err.errMsg) {\r\n\t\t\t\t\t\terrorMsg = err.errMsg;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示错误信息并提供重试选项\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: `${actionText}失败`,\r\n\t\t\t\t\t\tcontent: errorMsg,\r\n\t\t\t\t\t\tconfirmText: '重试',\r\n\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t// 用户点击重试\r\n\t\t\t\t\t\t\t\tthis.saveToServer();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 删除乘客信息\r\n\t\tdeletePassenger() {\r\n\t\t\t// 显示确认对话框\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确定要删除此乘客信息吗？',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tif (this.id) {\r\n\t\t\t\t\t\t\t// 如果有ID，调用删除接口\r\n\t\t\t\t\t\t\tthis.deleteFromServer();\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 否则直接从本地删除\r\n\t\t\t\t\t\t\tthis.deleteFromLocal();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 从服务器删除乘客\r\n\t\tdeleteFromServer() {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '删除中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\trequest.delete(`/app/information/${this.id}`)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.log('删除乘客成功，响应数据:', res);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 兼容不同的后端接口响应格式\r\n\t\t\t\t\tlet success = true;\r\n\t\t\t\t\tif (res.code !== undefined && res.code !== 0 && res.code !== 200) {\r\n\t\t\t\t\t\tsuccess = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (success) {\r\n\t\t\t\t\t\t// 接口调用成功\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 从本地删除\r\n\t\t\t\t\t\tthis.deleteFromLocal();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// API返回了业务错误\r\n\t\t\t\t\t\tlet errorMsg = '删除失败，请重试';\r\n\t\t\t\t\t\tif (res.msg) {\r\n\t\t\t\t\t\t\terrorMsg = res.msg;\r\n\t\t\t\t\t\t} else if (res.message) {\r\n\t\t\t\t\t\t\terrorMsg = res.message;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '删除失败',\r\n\t\t\t\t\t\t\tcontent: errorMsg,\r\n\t\t\t\t\t\t\tconfirmText: '重试',\r\n\t\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\t\tsuccess: (result) => {\r\n\t\t\t\t\t\t\t\tif (result.confirm) {\r\n\t\t\t\t\t\t\t\t\t// 用户点击重试\r\n\t\t\t\t\t\t\t\t\tthis.deleteFromServer();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.error('删除乘客失败，详细错误:', err);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示更具体的错误信息\r\n\t\t\t\t\tlet errorMsg = '网络异常，请重试';\r\n\t\t\t\t\tif (err && err.msg) {\r\n\t\t\t\t\t\terrorMsg = err.msg;\r\n\t\t\t\t\t} else if (err && err.errMsg) {\r\n\t\t\t\t\t\terrorMsg = err.errMsg;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示错误信息并提供重试选项\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '删除失败',\r\n\t\t\t\t\t\tcontent: errorMsg,\r\n\t\t\t\t\t\tconfirmText: '重试',\r\n\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t// 用户点击重试\r\n\t\t\t\t\t\t\t\tthis.deleteFromServer();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 从本地删除乘客\r\n\t\tdeleteFromLocal() {\r\n\t\t\t// 返回上一页并执行删除\r\n\t\t\tconst pages = getCurrentPages();\r\n\t\t\tconst prevPage = pages[pages.length - 2]; // 上一页\r\n\t\t\tprevPage.$vm.removePassenger(this.index);\r\n\t\t\tuni.navigateBack();\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化身份证号（中间部分用*号代替）\r\n\t\tformatIdCard(idCard) {\r\n\t\t\tif (idCard.length >= 18) {\r\n\t\t\t\treturn idCard.substr(0, 4) + '**********' + idCard.substr(14);\r\n\t\t\t} else if (idCard.length >= 15) {\r\n\t\t\t\treturn idCard.substr(0, 4) + '*******' + idCard.substr(11);\r\n\t\t\t}\r\n\t\t\treturn idCard;\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\npage {\r\n\tbackground-color: #f5f7fa;\r\n}\r\n\r\n.content {\r\n\tpadding: 20rpx;\r\n\tpadding-top: 30rpx;\r\n}\r\n\r\n.form-item {\r\n\tbackground-color: #fff;\r\n\tpadding: 30rpx 24rpx;\r\n\tmargin-bottom: 2rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tposition: relative;\r\n}\r\n\r\n.form-item::after {\r\n\tcontent: \"\";\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\tleft: 24rpx;\r\n\tright: 24rpx;\r\n\theight: 1rpx;\r\n\tbackground-color: #f0f0f0;\r\n}\r\n\r\n.form-item:last-child::after {\r\n\tdisplay: none;\r\n}\r\n\r\n.form-item:first-child {\r\n\tborder-radius: 16rpx 16rpx 0 0;\r\n}\r\n\r\n.last-item {\r\n\tborder-radius: 0 0 16rpx 16rpx;\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.form-label {\r\n\twidth: 180rpx;\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 400;\r\n}\r\n\r\n.form-input {\r\n\tflex: 1;\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\theight: 60rpx;\r\n}\r\n\r\n.form-value {\r\n\tflex: 1;\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.tips {\r\n\tpadding: 0 24rpx;\r\n\tmargin-bottom: 80rpx;\r\n}\r\n\r\n.tips-text {\r\n\tdisplay: block;\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n\tline-height: 1.6;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.save-button {\r\n\tmargin: 0 24rpx;\r\n\theight: 90rpx;\r\n\tbackground-color: #3F8DF9;\r\n\tcolor: #fff;\r\n\tfont-size: 32rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tborder-radius: 45rpx;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(63, 141, 249, 0.3);\r\n}\r\n\r\n.save-button-no-delete {\r\n\tmargin-bottom: 80rpx;\r\n}\r\n\r\n.delete-button {\r\n\tmargin: 30rpx 24rpx;\r\n\theight: 90rpx;\r\n\tbackground-color: #fff;\r\n\tcolor: #FF3B30;\r\n\tfont-size: 32rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tborder-radius: 45rpx;\r\n\tborder: 1rpx solid rgba(255, 59, 48, 0.6);\r\n}\r\n\r\n/* 按钮点击状态 */\r\n.button-hover {\r\n\topacity: 0.9;\r\n\ttransform: scale(0.98);\r\n}\r\n\r\n.delete-hover {\r\n\tbackground-color: #FFF5F5;\r\n\tcolor: #FF3B30;\r\n}\r\n</style> ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./passenger_edit.vue?vue&type=style&index=0&id=37c11de4&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./passenger_edit.vue?vue&type=style&index=0&id=37c11de4&lang=scss&scoped=true&\""], "sourceRoot": ""}