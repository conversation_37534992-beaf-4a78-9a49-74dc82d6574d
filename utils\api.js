/**
 * API接口封装
 * 统一管理所有接口调用
 */

import request from './request.js';

// 车票相关接口
export const ticketApi = {
  /**
   * 获取车票列表
   * @param {Object} params 查询参数
   * @param {string} params.departureTime 发车时间 格式：yyyy-MM-dd
   * @param {number} params.upAddress 上车地点ID
   * @param {number} params.downAddress 下车地点ID
   * @returns {Promise} 返回车票列表数据
   */
  getTicketList(params) {
    return request.get('/app/ticket/list', params);
  },

  /**
   * 根据ID获取车票详细信息
   * @param {number|string} ticketId 车票ID
   * @returns {Promise} 返回车票详细信息
   */
  getTicketById(ticketId) {
    return request.get(`/app/ticket/${ticketId}`);
  }
};

// 用户相关接口
export const userApi = {
  /**
   * 更新用户信息
   * @param {Object} userInfo 用户信息
   * @param {string} userInfo.avatar 头像URL
   * @param {string} userInfo.nickName 用户昵称
   * @returns {Promise} 返回更新结果
   */
  updateUserInfo(userInfo) {
    return request.put('/app/user', userInfo);
  }
};

// 订单相关接口
export const orderApi = {
  /**
   * 获取订单列表
   * @returns {Promise} 返回订单列表数据
   */
  getOrderList() {
    return request.get('/app/order/list');
  },

  /**
   * 申请售后
   * @param {number|string} orderId 订单ID
   * @returns {Promise} 返回申请结果
   */
  applyRefund(orderId) {
    return request.put('/app/order', { id: orderId });
  }
};

// 配置相关接口
export const configApi = {
  /**
   * 获取配置信息
   * @param {string} type 配置类型
   * @returns {Promise} 返回配置内容
   */
  getConfig(type) {
    return request.get(`/app/configure/${type}`);
  }
};

// 默认导出所有API
export default {
  ticketApi,
  userApi,
  orderApi,
  configApi
};
