@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  min-height: 100vh;
  background-color: #F6F7F9;
}
.share-earn-container {
  min-height: 100vh;
  position: relative;
}
/* 背景图片样式 */
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 490rpx;
}
/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
  display: flex;
  flex-direction: column;
}
.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 30rpx;
}
.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
}
.navbar-right {
  display: flex;
  align-items: center;
  width: 120rpx;
  justify-content: space-between;
}
.more-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
}
.dot {
  width: 6rpx;
  height: 6rpx;
  background-color: #ffffff;
  border-radius: 50%;
  margin: 2rpx 0;
}
.target-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 浮动规则按钮 */
.floating-rule-btn {
  position: fixed;
  right: 0;
  top: 300rpx;
  width: 80rpx;
  height: 60rpx;
  background-color: white;
  border-radius: 20rpx 0 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 998;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.rule-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
/* 内容区域 */
.content-area {
  width: 100%;
  box-sizing: border-box;
  padding: 0 30rpx;
  padding-top: 465rpx;
  /* 为背景图片留出空间 */
  background-color: #1476F9;
}
/* 邀请注册和操作步骤合并模块 */
.invite-steps-section {
  background: linear-gradient(180deg, #FFFFFF 0%, #D2EAFF 100%);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  margin-top: -50rpx;
  /* 覆盖背景图片底部一小部分 */
  position: relative;
  z-index: 1;
}
/* 带左侧蓝色柱状条的标题 */
.section-title-with-bar {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.title-bar {
  width: 6rpx;
  height: 32rpx;
  background-color: #3B99FC;
  border-radius: 3rpx;
  margin-right: 16rpx;
}
.section-title {
  font-size: 36rpx;
  color: #000000;
  font-weight: bold;
}
/* 邀请注册模块 */
.invite-module {
  margin-bottom: 40rpx;
}
.invite-reward {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  background-color: #D4EBFF;
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
}
.reward-label {
  font-size: 28rpx;
  color: #000000;
}
.reward-info {
  display: flex;
  align-items: baseline;
}
.reward-amount {
  font-size: 48rpx;
  color: #FF4757;
  font-weight: bold;
}
.reward-unit {
  font-size: 24rpx;
  color: #909399;
  font-weight: normal;
  margin-left: 4rpx;
}
/* 操作步骤模块 */
.steps-module {
  /* 操作步骤模块样式 */
}
.steps-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.33%;
}
.step-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
}
.step-text-container {
  display: flex;
  align-items: center;
  flex-direction: row;
}
.step-number {
  font-size: 24rpx;
  color: #999999;
  margin-right: 8rpx;
}
.step-text {
  font-size: 24rpx;
  color: #606266;
  text-align: center;
  line-height: 1.4;
  width: 100rpx;
}
.generate-poster-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.poster-btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
  letter-spacing: 8rpx;
  /* 字符间距 */
}
/* 当前收益模块 */
.earnings-section {
  background: linear-gradient(180deg, #FFFFFF 0%, #D2EAFF 100%);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}
.earnings-content {
  display: flex;
  justify-content: space-around;
}
.earnings-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.earnings-amount {
  font-size: 48rpx;
  color: #FF4757;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 16rpx;
}
.earnings-unit {
  font-size: 24rpx;
  color: #FF4757;
  font-weight: normal;
}
.earnings-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 16rpx;
}
.withdraw-btn {
  font-size: 24rpx;
  color: #4A90E2;
  padding: 8rpx 16rpx;
  border: 1rpx solid #4A90E2;
  border-radius: 20rpx;
}
/* 邀请记录和奖励记录 */
.records-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 410rpx;
}
.tab-header {
  display: flex;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}
.tab-item {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.tab-item.active .tab-text {
  color: #000000;
  font-weight: bold;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #4A90E2;
  border-radius: 3rpx;
}
.tab-text {
  font-size: 32rpx;
  color: #666666;
}
.tab-content {
  min-height: 400rpx;
}
.record-list {
  padding: 0 30rpx;
}
.record-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  background: #D4EBFF;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  margin-bottom: 20rpx;
}
.record-item:last-child {
  border-bottom: none;
}
.record-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}
.record-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.record-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.record-time {
  font-size: 24rpx;
  color: #999999;
}
/* 奖励金额样式 */
.record-reward {
  display: flex;
  align-items: center;
  text-align: right;
}
.reward-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 4rpx;
}
.reward-amount {
  font-size: 28rpx;
  color: #FF4757;
  font-weight: bold;
}
/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}
.empty-text {
  font-size: 28rpx;
  color: #999999;
}
/* 海报弹窗样式 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 移除不需要的弹窗内容样式 */
.poster-canvas-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 300rpx;
  padding: 40rpx 20rpx;
  min-height: 60vh;
}
.poster-canvas {
  border-radius: 20rpx;
  overflow: hidden;
}
.poster-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666666;
}
/* 移除活动规则相关样式 */
/* 底部操作按钮样式 */
.poster-bottom-actions {
  position: fixed;
  bottom: 100rpx;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  padding: 20rpx 0;
  display: flex;
  justify-content: space-around;
  z-index: 10000;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  transition: all 0.3s ease;
  flex: 1;
}
.action-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
}
.action-btn-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}
.action-btn-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}
/* 取消按钮样式 */
.poster-cancel-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  padding: 40rpx 0;
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
}
.cancel-text {
  font-size: 36rpx;
  color: #333333;
  font-weight: 500;
}
