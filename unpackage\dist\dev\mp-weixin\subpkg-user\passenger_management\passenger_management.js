(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["subpkg-user/passenger_management/passenger_management"],{104:function(e,s,n){"use strict";(function(e,s){var t=n(4);n(26);t(n(25));var r=t(n(105));e.__webpack_require_UNI_MP_PLUGIN__=n,s(r.default)}).call(this,n(1)["default"],n(2)["createPage"])},105:function(e,s,n){"use strict";n.r(s);var t=n(106),r=n(108);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(s,e,(function(){return r[e]}))}(a);n(110);var i,o=n(33),c=Object(o["default"])(r["default"],t["render"],t["staticRenderFns"],!1,null,null,null,!1,t["components"],i);c.options.__file="subpkg-user/passenger_management/passenger_management.vue",s["default"]=c.exports},106:function(e,s,n){"use strict";n.r(s);var t=n(107);n.d(s,"render",(function(){return t["render"]})),n.d(s,"staticRenderFns",(function(){return t["staticRenderFns"]})),n.d(s,"recyclableRender",(function(){return t["recyclableRender"]})),n.d(s,"components",(function(){return t["components"]}))},107:function(e,s,n){"use strict";var t;n.r(s),n.d(s,"render",(function(){return r})),n.d(s,"staticRenderFns",(function(){return i})),n.d(s,"recyclableRender",(function(){return a})),n.d(s,"components",(function(){return t}));var r=function(){var e=this,s=e.$createElement,n=(e._self._c,e.passengers.length),t=e.isSelectMode?e.selectedPassengers.length:null;e._isMounted||(e.e0=function(s,n){var t=arguments[arguments.length-1].currentTarget.dataset,r=t.eventParams||t["event-params"];n=r.index;e.isSelectMode?e.selectPassenger(n):e.selectPassengerForReturn(n)}),e.$mp.data=Object.assign({},{$root:{g0:n,g1:t}})},a=!1,i=[];r._withStripped=!0},108:function(e,s,n){"use strict";n.r(s);var t=n(109),r=n.n(t);for(var a in t)["default"].indexOf(a)<0&&function(e){n.d(s,e,(function(){return t[e]}))}(a);s["default"]=r.a},109:function(e,s,n){"use strict";(function(e){var t=n(4);Object.defineProperty(s,"__esModule",{value:!0}),s.default=void 0;var r=t(n(11)),a=t(n(18)),i=t(n(41));function o(e,s){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);s&&(t=t.filter((function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable}))),n.push.apply(n,t)}return n}function c(e){for(var s=1;s<arguments.length;s++){var n=null!=arguments[s]?arguments[s]:{};s%2?o(Object(n),!0).forEach((function(s){(0,r.default)(e,s,n[s])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(n,s))}))}return e}var u={data:function(){return{passengers:[],originalPassengers:[],isLoading:!1,isRefreshing:!1,isSelectMode:!1,selectedPassengers:[]}},onLoad:function(s){s&&"select"===s.mode&&(this.isSelectMode=!0,e.setNavigationBarTitle({title:"选择乘客"}))},onShow:function(){this.loadPassengers()},onPullDownRefresh:function(){this.loadPassengers(!0)},methods:{loadPassengers:function(){var s=this,n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];n||(this.isLoading=!0,e.showLoading({title:"加载中..."})),i.default.get("/app/information/list").then((function(t){console.log("获取乘客列表成功:",t),s.isLoading=!1,s.isRefreshing=!1,n||e.hideLoading(),t&&t.data?(s.originalPassengers=(0,a.default)(t.data),s.passengers=s.formatPassengerData(t.data),s.isSelectMode&&(s.passengers.forEach((function(e){e.selected=!1})),s.updateSelectedPassengers()),s.savePassengers(),n&&(e.showToast({title:"刷新成功",icon:"success",duration:1500}),e.stopPullDownRefresh())):s.handleLoadError()})).catch((function(t){console.error("获取乘客列表失败:",t),s.isLoading=!1,s.isRefreshing=!1,n?e.stopPullDownRefresh():e.hideLoading(),s.handleLoadError()}))},handleLoadError:function(){e.showToast({title:"获取乘客列表失败",icon:"none"});try{var s=e.getStorageSync("passengers");this.passengers=s?JSON.parse(s):[]}catch(n){console.error("获取本地乘客列表失败",n),this.passengers=[]}},formatPassengerData:function(e){var s=this;return e.map((function(e){return{id:e.id,name:e.name||"",idCard:s.formatIdCard(e.idNumber||""),rawIdNumber:e.idNumber||"",selected:!s.isSelectMode}}))},formatIdCard:function(e){return e.length>=18?e.substr(0,4)+"**********"+e.substr(14):e.length>=15?e.substr(0,4)+"*******"+e.substr(11):e},addPassenger:function(){e.navigateTo({url:"/subpkg-user/passenger_edit/passenger_edit"})},editPassenger:function(s){var n=this;e.navigateTo({url:"/subpkg-user/passenger_edit/passenger_edit",success:function(e){var t=c(c({},n.passengers[s]),{},{idNumber:n.passengers[s].rawIdNumber});e.eventChannel.emit("acceptPassengerData",{passenger:t,index:s})}})},addNewPassenger:function(e){this.passengers.push(e),this.savePassengers(),this.loadPassengers()},updatePassenger:function(e,s){e>=0&&e<this.passengers.length&&(this.passengers[e]=s,this.savePassengers())},removePassenger:function(e){e>=0&&e<this.passengers.length&&(this.passengers.splice(e,1),this.savePassengers())},savePassengers:function(){try{e.setStorageSync("passengers",JSON.stringify(this.passengers))}catch(s){console.error("保存乘客列表失败",s)}},selectPassenger:function(e){this.isSelectMode&&(this.passengers[e].selected=!this.passengers[e].selected,this.updateSelectedPassengers())},updateSelectedPassengers:function(){this.selectedPassengers=this.passengers.filter((function(e){return e.selected}))},confirmSelection:function(){if(0!==this.selectedPassengers.length){var s=getCurrentPages(),n=s[s.length-2];n&&"subpkg-booking/ticket_confirm/ticket_confirm"===n.route&&n.$vm.receiveSelectedPassengers(this.selectedPassengers),e.navigateBack()}else e.showToast({title:"请至少选择一位乘客",icon:"none"})},selectPassengerForReturn:function(s){var n=this.passengers[s],t=getCurrentPages(),r=t[t.length-2];r&&"subpkg-booking/ticket_confirm/ticket_confirm"===r.route&&r.$vm.receiveSelectedPassengers([n]),e.navigateBack()}}};s.default=u}).call(this,n(2)["default"])},110:function(e,s,n){"use strict";n.r(s);var t=n(111),r=n.n(t);for(var a in t)["default"].indexOf(a)<0&&function(e){n.d(s,e,(function(){return t[e]}))}(a);s["default"]=r.a},111:function(e,s,n){}},[[104,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/subpkg-user/passenger_management/passenger_management.js.map