<template>
	<view class="mine-container">
		<!-- 背景图片 -->
		<image class="background-image" src="/static/images/minebackground.png" mode="aspectFill"></image>
		
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="navbarStyle">
			<!-- 状态栏占位 -->
			<view :style="{ height: statusBarHeight + 'px' }"></view>
			<!-- 导航栏内容区 -->
			<view class="navbar-content">
				<text class="page-title">我的</text>
			</view>
		</view>
		
		<!-- 内容区域 - 添加上外边距，确保不被导航栏遮挡 -->
		<view class="content-area" :style="{ paddingTop: navbarHeight + 'px' }">
			<!-- 用户信息区域 -->
			<view class="user-info-section">
				<view class="user-info-left" @click="goToUserProfile">
					<image class="user-avatar" :src="userInfo.avatar"></image>
					<view class="user-details">
						<text class="username">{{userInfo.nickName}}</text>
						<text class="user-phone">{{userInfo.phone}}</text>
					</view>
				</view>
				<view class="user-info-right" @click="goToCoupon">
					<text class="coupon-text">优惠券</text>
				</view>
			</view>
			
			<!-- 常用工具区域 -->
			<view class="tools-section">
				<text class="section-title">常用工具</text>
				<view class="tools-grid">
					<!-- 联系客服 -->
					<button class="tool-item" open-type="contact">
						<view class="tool-icon-container">
							<image class="tool-icon" src="/static/icons/customer_service.png"></image>
						</view>
						<text class="tool-name">联系客服</text>
					</button>
					
					<!-- 分享赚钱 -->
					<view class="tool-item" @click="shareToEarn">
						<view class="tool-icon-container">
							<image class="tool-icon" src="/static/icons/share_money.png"></image>
						</view>
						<text class="tool-name">分享赚钱</text>
					</view>
					
					<!-- 乘车人管理 -->
					<view class="tool-item" @click="managePassengers">
						<view class="tool-icon-container">
							<image class="tool-icon" src="/static/icons/passenger_manage.png"></image>
						</view>
						<text class="tool-name">乘车人管理</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import auth from '../../utils/auth.js';
	
	export default {
		data() {
			return {
				userInfo: {
					nickName: '未知用户',
					phone: '153****1696',
					avatar: '/static/images/user.png'
				},
				couponCount: 5,
				statusBarHeight: 20, // 状态栏高度
				navbarHeight: 80,    // 导航栏总高度
				menuButtonInfo: {},  // 胶囊按钮信息
			}
		},
		computed: {
			// 计算导航栏的样式
			navbarStyle() {
				return {
					height: this.navbarHeight + 'px',
					paddingTop: this.statusBarHeight + 'px'
				}
			}
		},
		onLoad() {
			// 获取系统信息
			this.setNavbarInfo();
		},
		onShow() {
			// 每次页面显示时检查是否有更新的用户信息
			const userInfo = auth.getUserInfo();
			if (userInfo) {
				this.userInfo = userInfo;
			} else {
				// 如果缓存中没有用户信息，跳转到登录页
				auth.navigateToLogin('/pages/mine/mine');
			}
		},
		methods: {
			// 设置导航栏信息
			setNavbarInfo() {
				try {
					// 获取系统信息
					const systemInfo = uni.getSystemInfoSync();
					// 获取状态栏高度
					this.statusBarHeight = systemInfo.statusBarHeight;
					
					// 获取胶囊按钮位置信息
					this.menuButtonInfo = uni.getMenuButtonBoundingClientRect();
					
					// 计算导航栏高度
					const menuBottom = this.menuButtonInfo.bottom;
					const menuTop = this.menuButtonInfo.top;
					const menuHeight = this.menuButtonInfo.height;
					
					// 计算导航栏内容区高度
					const contentHeight = menuHeight + (menuTop - this.statusBarHeight) * 2;
					
					// 设置导航栏总高度
					this.navbarHeight = this.statusBarHeight + contentHeight;
					
					console.log('导航栏高度:', this.navbarHeight);
				} catch (e) {
					console.error('获取系统信息失败:', e);
					// 设置默认高度
					this.statusBarHeight = 20;
					this.navbarHeight = 60;
				}
			},
			
			// 跳转到用户资料页
			goToUserProfile() {
				uni.navigateTo({
					url: '/subpkg-user/user_profile/user_profile'
				});
			},



			// 分享赚钱
			shareToEarn() {
				uni.navigateTo({
					url: '/subpkg-activity/share_earn/share_earn'
				});
			},

			// 乘车人管理
			managePassengers() {
				uni.navigateTo({
					url: '/subpkg-user/passenger_management/passenger_management'
				});
			},

			// 跳转到优惠券页面
			goToCoupon() {
				uni.navigateTo({
					url: '/subpkg-booking/coupon/coupon'
				});
			}
		}
	}
</script>

<style lang="scss">
page {
	min-height: 100vh;
	background-color: #F6F7F9;
}

.mine-container {
	min-height: 100vh;
	position: relative;
}

/* 背景图片样式 */
.background-image {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: -1;
}

/* 自定义导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 999;
	display: flex;
	flex-direction: column;
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 44px; /* 默认高度，会被JS动态修改 */
}

.page-title {
	color: #000000;
	font-size: 36rpx;
	font-weight: bold;
}

/* 内容区域 */
.content-area {
	width: 100%;
	box-sizing: border-box;
}

/* 用户信息区域样式 */
.user-info-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 30rpx;
	border-radius: 12rpx;
	position: relative;
	z-index: 1;
	margin: 20rpx;
}

.user-info-left {
	display: flex;
	align-items: center;
}

.user-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.user-details {
	display: flex;
	flex-direction: column;
}

.username {
	font-size: 36rpx;
	color: #333333;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.user-phone {
	font-size: 28rpx;
	color: #999999;
}

.user-info-right {
	display: flex;
	flex-direction: column;
	align-items: center;
	cursor: pointer;
}

.coupon-count {
	font-size: 48rpx;
	color: #333333;
	font-weight: bold;
	line-height: 1;
}

.coupon-text {
	font-size: 28rpx;
	color: #999999;
	margin-top: 8rpx;
}

/* 常用工具区域样式 */
.tools-section {
	margin: 20rpx;
	background-color: #FFFFFF;
	padding: 30rpx;
	border-radius: 12rpx;
	position: relative;
	z-index: 1;
}

.section-title {
	font-size: 33rpx;
	color: #333333;
	font-weight: bold;
}

.tools-grid {
	display: flex;
	justify-content: space-between;
	margin-top: 30rpx;
}

.tool-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 33.33%;
	background-color: transparent !important;
	border: none !important;
	outline: none !important;
	padding: 0 !important;
	margin: 0 !important;
	font-size: inherit;
	line-height: inherit;
	box-shadow: none !important;
}

.tool-item::after {
	border: none !important;
}

/* 专门针对button组件的样式 */
button.tool-item {
	background: transparent !important;
	border: 0 !important;
	border-radius: 0 !important;
	outline: 0 !important;
	-webkit-appearance: none !important;
	appearance: none !important;
}

button.tool-item::after {
	border: none !important;
	content: none !important;
}

.tool-icon-container {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background-color: #F6F7F9;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 16rpx;
}

.tool-icon {
	width: 60rpx;
	height: 60rpx;
}

.tool-name {
	font-size: 28rpx;
	color: #333333;
}
</style> 