<template>
	<view class="container">
		<!-- 乘客列表 -->
		<view class="passenger-list">
			<view v-if="passengers.length > 0">
				<view class="passenger-item" v-for="(passenger, index) in passengers" :key="index"
					@click="isSelectMode ? selectPassenger(index) : selectPassengerForReturn(index)">
					<!-- 选择模式下显示选择框 -->
					<view v-if="isSelectMode" class="passenger-checkbox">
						<view :class="['checkbox-circle', { 'checkbox-active': passenger.selected }]">
							<view v-if="passenger.selected" class="checkbox-inner"></view>
						</view>
					</view>

					<view class="passenger-info">
						<view class="info-row">
							<text class="passenger-name">{{passenger.name}}</text>
							<text class="id-type">身份证</text>
							<text class="passenger-id">{{passenger.idCard}}</text>
						</view>
					</view>

					<!-- 非选择模式下显示编辑按钮 -->
					<view v-if="!isSelectMode" class="edit-button" @click.stop="editPassenger(index)">
						<image class="edit-icon" src="/static/icons/idet.png"></image>
						<text class="edit-text">编辑</text>
					</view>
				</view>
			</view>
			<view v-else class="empty-tip">
				<text>暂无乘客信息</text>
				<text class="add-tip">点击下方按钮添加新乘客</text>
			</view>
		</view>
		
		<!-- 选择模式下显示确认按钮，否则显示添加乘客按钮 -->
		<view v-if="isSelectMode" class="confirm-button" @click="confirmSelection">
			确认选择 ({{ selectedPassengers.length }})
		</view>
		<view v-else class="add-button" @click="addPassenger">添加乘客</view>
	</view>
</template>

<script>
	import request from '../../utils/request.js';

	export default {
		data() {
			return {
				passengers: [],
				originalPassengers: [], // 存储原始数据（包含完整身份证号）
				isLoading: false,
				isRefreshing: false,
				isSelectMode: false, // 是否为选择模式
				selectedPassengers: [] // 已选择的乘客
			};
		},
		onLoad(options) {
			// 检查是否为选择模式
			if (options && options.mode === 'select') {
				this.isSelectMode = true;
				// 设置导航栏标题
				uni.setNavigationBarTitle({
					title: '选择乘客'
				});
			}
		},

		onShow() {
			// 每次页面显示时获取乘客列表
			this.loadPassengers();
		},

		// 下拉刷新
		onPullDownRefresh() {
			this.loadPassengers(true);
		},
		methods: {
			
			// 加载乘客列表
			loadPassengers(isRefresh = false) {
				if (!isRefresh) {
					this.isLoading = true;
					// 显示加载提示
					uni.showLoading({
						title: '加载中...'
					});
				}
				
				// 调用接口获取乘客列表
				request.get('/app/information/list')
					.then(res => {
						console.log('获取乘客列表成功:', res);
						this.isLoading = false;
						this.isRefreshing = false;
						
						if (!isRefresh) {
							uni.hideLoading();
						}
						
						// 判断返回数据是否正确
						if (res && res.data) {
							// 保存原始数据
							this.originalPassengers = [...res.data];
							// 转换成页面所需的数据格式
							this.passengers = this.formatPassengerData(res.data);

							// 如果是选择模式，确保所有乘客都不选中，并更新选中列表
							if (this.isSelectMode) {
								this.passengers.forEach(passenger => {
									passenger.selected = false;
								});
								this.updateSelectedPassengers();
							}

							// 将获取到的数据保存到本地存储中
							this.savePassengers();

							if (isRefresh) {
								uni.showToast({
									title: '刷新成功',
									icon: 'success',
									duration: 1500
								});
								// 停止下拉刷新
								uni.stopPullDownRefresh();
							}
						} else {
							this.handleLoadError();
						}
					})
					.catch(err => {
						console.error('获取乘客列表失败:', err);
						this.isLoading = false;
						this.isRefreshing = false;

						if (!isRefresh) {
							uni.hideLoading();
						} else {
							// 停止下拉刷新
							uni.stopPullDownRefresh();
						}

						this.handleLoadError();
					});
			},
			
			// 处理加载错误
			handleLoadError() {
				uni.showToast({
					title: '获取乘客列表失败',
					icon: 'none'
				});
				
				// 尝试从本地存储获取
				try {
					const passengers = uni.getStorageSync('passengers');
					if (passengers) {
						this.passengers = JSON.parse(passengers);
					} else {
						this.passengers = [];
					}
				} catch (e) {
					console.error('获取本地乘客列表失败', e);
					this.passengers = [];
				}
			},
			
			// 格式化接口返回的数据
			formatPassengerData(apiData) {
				// 将接口返回的数据格式化为页面所需格式
				return apiData.map(item => ({
					id: item.id, // 保存乘客ID
					name: item.name || '',
					idCard: this.formatIdCard(item.idNumber || ''),
					rawIdNumber: item.idNumber || '', // 保存原始身份证号
					selected: this.isSelectMode ? false : true // 选择模式下默认不选中，管理模式下默认选中
				}));
			},
			
			// 格式化身份证号（中间部分用*号代替）
			formatIdCard(idCard) {
				if (idCard.length >= 18) {
					return idCard.substr(0, 4) + '**********' + idCard.substr(14);
				} else if (idCard.length >= 15) {
					return idCard.substr(0, 4) + '*******' + idCard.substr(11);
				}
				return idCard;
			},
			
			// 添加乘客
			addPassenger() {
				uni.navigateTo({
					url: '/subpkg-user/passenger_edit/passenger_edit'
				});
			},

			// 编辑乘客
			editPassenger(index) {
				uni.navigateTo({
					url: '/subpkg-user/passenger_edit/passenger_edit',
					success: (res) => {
						// 传递乘客数据到编辑页面（包含完整身份证号）
						const passengerData = {
							...this.passengers[index],
							idNumber: this.passengers[index].rawIdNumber // 传递完整的身份证号
						};

						res.eventChannel.emit('acceptPassengerData', {
							passenger: passengerData,
							index: index
						});
					}
				});
			},
			
			// 添加新乘客（供编辑页面调用）
			addNewPassenger(passenger) {
				this.passengers.push(passenger);
				this.savePassengers();
				// 刷新数据
				this.loadPassengers();
			},
			
			// 更新乘客信息（供编辑页面调用）
			updatePassenger(index, passenger) {
				if (index >= 0 && index < this.passengers.length) {
					this.passengers[index] = passenger;
					this.savePassengers();
				}
			},
			
			// 删除乘客（供编辑页面调用）
			removePassenger(index) {
				if (index >= 0 && index < this.passengers.length) {
					this.passengers.splice(index, 1);
					this.savePassengers();
				}
			},
			
			// 保存乘客列表到本地存储
			savePassengers() {
				try {
					uni.setStorageSync('passengers', JSON.stringify(this.passengers));
				} catch (e) {
					console.error('保存乘客列表失败', e);
				}
			},

			// 选择乘客（选择模式下使用）
			selectPassenger(index) {
				if (!this.isSelectMode) return;

				// 切换选中状态
				this.passengers[index].selected = !this.passengers[index].selected;

				// 更新已选择的乘客列表
				this.updateSelectedPassengers();
			},

			// 更新已选择的乘客列表
			updateSelectedPassengers() {
				this.selectedPassengers = this.passengers.filter(passenger => passenger.selected);
			},

			// 确认选择
			confirmSelection() {
				if (this.selectedPassengers.length === 0) {
					uni.showToast({
						title: '请至少选择一位乘客',
						icon: 'none'
					});
					return;
				}

				// 将选中的乘客数据传递回购票确认页面
				const pages = getCurrentPages();
				const prevPage = pages[pages.length - 2]; // 上一个页面

				if (prevPage && prevPage.route === 'subpkg-booking/ticket_confirm/ticket_confirm') {
					// 调用购票确认页面的方法，传递选中的乘客
					prevPage.$vm.receiveSelectedPassengers(this.selectedPassengers);
				}

				// 返回上一页
				uni.navigateBack();
			},

			// 非选择模式下点击乘客卡片直接返回
			selectPassengerForReturn(index) {
				// 获取选中的乘客数据
				const selectedPassenger = this.passengers[index];

				// 将乘客数据传递回购票确认页面
				const pages = getCurrentPages();
				const prevPage = pages[pages.length - 2]; // 上一个页面

				if (prevPage && prevPage.route === 'subpkg-booking/ticket_confirm/ticket_confirm') {
					// 调用购票确认页面的方法，传递单个乘客（包装成数组）
					prevPage.$vm.receiveSelectedPassengers([selectedPassenger]);
				}

				// 返回上一页
				uni.navigateBack();
			}
		}
	};
</script>

<style lang="scss">
/* 移除不再需要的iconfont样式 */
page {
	background-color: #f5f7fa;
	min-height: 100vh;
}

.container {
	padding: 20rpx;
	min-height: 97vh;
	display: flex;
	flex-direction: column;
}

.passenger-list {
	flex: 1;
	/* 移除固定高度，让内容自然撑开 */
}

.passenger-item {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 30rpx 24rpx;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.passenger-info {
	flex: 1;
	margin-right: 20rpx;
}

.info-row {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 10rpx; /* 使用gap属性提供更一致的间距 */
}

.passenger-name {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

.id-type {
	font-size: 26rpx;
	color: #999999;
	background-color: #F6F7F9;
	padding: 2rpx 12rpx;
	border-radius: 12rpx;
}

.passenger-id {
	font-size: 28rpx;
	color: #666666;
}

.edit-button {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 80rpx;
	padding: 10rpx;
	border-left: 1rpx solid #f0f0f0;
}

.edit-icon {
	width: 42rpx;
	height: 42rpx;
	margin-bottom: 8rpx;
}

.edit-text {
	font-size: 24rpx;
	color: #999999;
}

.add-button {
	height: 90rpx;
	background-color: #3F8DF9;
	color: #FFFFFF;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 45rpx;
	margin: 40rpx 0;
	box-shadow: 0 4rpx 12rpx rgba(63, 141, 249, 0.3);
	transition: all 0.2s;
}

.add-button:active {
	transform: scale(0.98);
	opacity: 0.9;
}

.empty-tip {
	padding: 100rpx 0;
	text-align: center;
	color: #999;
	font-size: 32rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.add-tip {
	font-size: 28rpx;
	color: #bbb;
	margin-top: 20rpx;
}

/* 选择框样式 */
.passenger-checkbox {
	margin-right: 20rpx;
	display: flex;
	align-items: center;
}

.checkbox-circle {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #ddd;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s;
}

.checkbox-active {
	border-color: #3F8DF9;
	background-color: #3F8DF9;
}

.checkbox-inner {
	width: 20rpx;
	height: 20rpx;
	background-color: white;
	border-radius: 50%;
}

/* 确认按钮样式 */
.confirm-button {
	height: 90rpx;
	background-color: #3F8DF9;
	color: #FFFFFF;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 45rpx;
	margin: 40rpx 0;
	box-shadow: 0 4rpx 12rpx rgba(63, 141, 249, 0.3);
	transition: all 0.2s;
}

.confirm-button:active {
	transform: scale(0.98);
	opacity: 0.9;
}
</style> 