(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["subpkg-booking/order_detail/order_detail"],{88:function(e,t,n){"use strict";(function(e,t){var r=n(4);n(26);r(n(25));var a=r(n(89));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n(1)["default"],n(2)["createPage"])},89:function(e,t,n){"use strict";n.r(t);var r=n(90),a=n(92);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n(94);var i,s=n(33),c=Object(s["default"])(a["default"],r["render"],r["staticRenderFns"],!1,null,null,null,!1,r["components"],i);c.options.__file="subpkg-booking/order_detail/order_detail.vue",t["default"]=c.exports},90:function(e,t,n){"use strict";n.r(t);var r=n(91);n.d(t,"render",(function(){return r["render"]})),n.d(t,"staticRenderFns",(function(){return r["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return r["recyclableRender"]})),n.d(t,"components",(function(){return r["components"]}))},91:function(e,t,n){"use strict";var r;n.r(t),n.d(t,"render",(function(){return a})),n.d(t,"staticRenderFns",(function(){return i})),n.d(t,"recyclableRender",(function(){return o})),n.d(t,"components",(function(){return r}));try{r={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,165))}}}catch(s){if(-1===s.message.indexOf("Cannot find module")||-1===s.message.indexOf(".vue"))throw s;console.error(s.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var a=function(){var e=this,t=e.$createElement,n=(e._self._c,"待出行"!==e.orderStatus?e.passengers.length:null);e.$mp.data=Object.assign({},{$root:{g0:n}})},o=!1,i=[];a._withStripped=!0},92:function(e,t,n){"use strict";n.r(t);var r=n(93),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},93:function(e,t,n){"use strict";(function(e){var r=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n(58)),o=r(n(60)),i=n(61),s={data:function(){return{orderStatus:"待出行",isPassengerCollapsed:!1,orderInfo:null,passengers:[],paymentInfo:{actualPayment:"0.00",orderId:"",orderTime:"",payTime:""},orderData:null,tripInfo:{date:"",weekday:"",time:"",departure:"",arrival:""},showQrPopup:!1,groupQrCodeUrl:""}},onLoad:function(t){var n=t.id;if(n){console.log("Loaded order id:",n);var r=e.getStorageSync("orderDetailData");r&&r.id==n?(this.orderData=r,this.processOrderData(r)):(console.warn("订单数据不存在，需要从接口获取"),e.showToast({title:"订单数据加载失败",icon:"none"}))}},onUnload:function(){e.removeStorageSync("orderDetailData")},methods:{processOrderData:function(e){this.orderStatus=e.status,this.tripInfo={date:this.formatDateForDisplay(e.originalData.upTicketTime),weekday:this.getWeekday(e.originalData.upTicketTime),time:this.formatTimeForDisplay(e.originalData.upTicketTime),departure:e.originalData.upAddress||"未知",arrival:e.originalData.downAddress||"未知"},this.passengers=[{name:e.originalData.name,idCard:this.maskIdCard(e.originalData.idNumber),type:"成人票"}],this.paymentInfo={actualPayment:parseFloat(e.originalData.payAmout||0).toFixed(2),orderId:e.originalData.orderNo,orderTime:this.formatDateTime(e.originalData.upTicketTime),payTime:this.formatDateTime(e.originalData.upTicketTime)}},formatDateForDisplay:function(e){if(!e)return"待定";try{var t=new Date(e),n=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0");return"".concat(n,"-").concat(r)}catch(a){return"待定"}},formatTimeForDisplay:function(e){if(!e)return"待定 出发";try{var t=new Date(e),n=String(t.getHours()).padStart(2,"0"),r=String(t.getMinutes()).padStart(2,"0");return"".concat(n,":").concat(r," 出发")}catch(a){return"待定 出发"}},getWeekday:function(e){if(!e)return"";try{var t=new Date(e),n=["周日","周一","周二","周三","周四","周五","周六"];return n[t.getDay()]}catch(r){return""}},formatDateTime:function(e){if(!e)return"待定";try{var t=new Date(e),n=t.getFullYear(),r=String(t.getMonth()+1).padStart(2,"0"),a=String(t.getDate()).padStart(2,"0"),o=String(t.getHours()).padStart(2,"0"),i=String(t.getMinutes()).padStart(2,"0"),s=String(t.getSeconds()).padStart(2,"0");return"".concat(n,"-").concat(r,"-").concat(a," ").concat(o,":").concat(i,":").concat(s)}catch(c){return"待定"}},maskIdCard:function(e){return!e||e.length<8?e:e.substring(0,4)+"**********"+e.substring(e.length-4)},togglePassengerList:function(){this.isPassengerCollapsed=!this.isPassengerCollapsed},openNavigation:function(){e.showToast({title:"打开导航功能开发中",icon:"none"})},copyOrderId:function(){e.setClipboardData({data:this.paymentInfo.orderId,success:function(){e.showToast({title:"订单号已复制",icon:"success"})}})},openAfterSale:function(){var t=this;return(0,o.default)(a.default.mark((function n(){var r,o;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,t.orderData&&t.orderData.id){n.next=4;break}return e.showToast({title:"订单信息不存在",icon:"none"}),n.abrupt("return");case 4:return n.next=6,new Promise((function(t){e.showModal({title:"申请售后",content:"确定要申请售后吗？",success:function(e){t(e.confirm)}})}));case 6:if(r=n.sent,r){n.next=9;break}return n.abrupt("return");case 9:return e.showLoading({title:"申请中..."}),n.next=12,i.orderApi.applyRefund(t.orderData.id);case 12:o=n.sent,e.hideLoading(),o&&200===o.code?(e.showToast({title:"申请售后成功",icon:"success"}),setTimeout((function(){e.navigateBack()}),1500)):e.showToast({title:o.msg||"申请售后失败",icon:"none"}),n.next=22;break;case 17:n.prev=17,n.t0=n["catch"](0),e.hideLoading(),console.error("申请售后失败:",n.t0),e.showToast({title:"申请售后失败，请重试",icon:"none"});case 22:case"end":return n.stop()}}),n,null,[[0,17]])})))()},fetchOrderDetail:function(e){},showGroupQrCode:function(){var t=this;return(0,o.default)(a.default.mark((function n(){var r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,t.showQrPopup=!0,!t.groupQrCodeUrl){n.next=4;break}return n.abrupt("return");case 4:return n.next=6,i.configApi.getConfig("group_qr_code");case 6:r=n.sent,r&&200===r.code?t.groupQrCodeUrl=r.msg||"":e.showToast({title:"获取群二维码失败",icon:"none"}),n.next=14;break;case 10:n.prev=10,n.t0=n["catch"](0),console.error("获取群二维码失败:",n.t0),e.showToast({title:"获取群二维码失败",icon:"none"});case 14:case"end":return n.stop()}}),n,null,[[0,10]])})))()},hideGroupQrCode:function(){this.showQrPopup=!1},saveQrCode:function(){var t=this;return(0,o.default)(a.default.mark((function n(){var r,o;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.groupQrCodeUrl){n.next=3;break}return e.showToast({title:"二维码加载中，请稍后",icon:"none"}),n.abrupt("return");case 3:return n.prev=3,e.showLoading({title:"正在保存..."}),n.next=7,new Promise((function(n,r){e.downloadFile({url:t.groupQrCodeUrl,success:function(e){200===e.statusCode?n(e.tempFilePath):r(new Error("下载失败"))},fail:r})}));case 7:return r=n.sent,n.next=10,new Promise((function(t,n){e.saveImageToPhotosAlbum({filePath:r,success:t,fail:n})}));case 10:e.hideLoading(),e.showToast({title:"保存成功",icon:"success"}),n.next=21;break;case 14:n.prev=14,n.t0=n["catch"](3),e.hideLoading(),console.error("保存失败:",n.t0),o="保存失败，请重试",n.t0.errMsg&&n.t0.errMsg.includes("auth")?o="请授权访问相册":n.t0.errMsg&&n.t0.errMsg.includes("download")&&(o="图片下载失败"),e.showToast({title:o,icon:"none"});case 21:case"end":return n.stop()}}),n,null,[[3,14]])})))()}}};t.default=s}).call(this,n(2)["default"])},94:function(e,t,n){"use strict";n.r(t);var r=n(95),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},95:function(e,t,n){}},[[88,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/subpkg-booking/order_detail/order_detail.js.map