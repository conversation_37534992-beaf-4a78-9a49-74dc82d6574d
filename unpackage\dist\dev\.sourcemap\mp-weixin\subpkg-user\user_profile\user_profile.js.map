{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/subpkg-user/user_profile/user_profile.vue?b484", "webpack:///E:/购票系统/购票系统/subpkg-user/user_profile/user_profile.vue?d8ce", "webpack:///E:/购票系统/购票系统/subpkg-user/user_profile/user_profile.vue?386d", "webpack:///E:/购票系统/购票系统/subpkg-user/user_profile/user_profile.vue?837f", "uni-app:///subpkg-user/user_profile/user_profile.vue", "webpack:///E:/购票系统/购票系统/subpkg-user/user_profile/user_profile.vue?e824"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "data", "userInfo", "nick<PERSON><PERSON>", "phone", "avatar", "onLoad", "methods", "onChooseAvatar", "uni", "title", "icon", "onNicknameChange", "updateUserInfo", "userApi", "console", "auth", "logout", "content", "success", "url"], "mappings": "gKAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,4CACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCRvB,yHAA6qB,eAAG,G,2HC0ChrB,eACA,Q,EAEA,CACAC,gBACA,OACAC,UACAC,YACAC,SACAC,sCAIAC,kBAEA,8BACA,IACA,kBAGAC,SAEAC,2BACA,yBACA,IACA,uBAGA,sBAEAC,aACAC,eACAC,mBAMAC,6BACA,qBACA,gCACA,yBAGA,sBAEAH,aACAC,eACAC,mBAMAE,0BAAA,WAEAC,0BACAT,4BACAF,kCACA,kBACAY,0BAGAC,qCACA,mBACAD,gCAKAE,kBACAR,aACAC,WACAQ,oBACAC,oBACA,YAEAH,2BAGAP,aACAW,kCAOA,c,6DClIA,yHAAowC,eAAG,G", "file": "subpkg-user/user_profile/user_profile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg-user/user_profile/user_profile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./user_profile.vue?vue&type=template&id=5c2dfa4e&\"\nvar renderjs\nimport script from \"./user_profile.vue?vue&type=script&lang=js&\"\nexport * from \"./user_profile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user_profile.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg-user/user_profile/user_profile.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_profile.vue?vue&type=template&id=5c2dfa4e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_profile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_profile.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"profile-container\">\n\t\t<!-- 头像部分 -->\n\t\t<view class=\"avatar-section\">\n\t\t\t<text class=\"section-label\">头像</text>\n\t\t\t<view class=\"avatar-wrapper\">\n\t\t\t\t<button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" class=\"avatar-button\">\n\t\t\t\t\t<image class=\"avatar\" :src=\"userInfo.avatar\"></image>\n\t\t\t\t</button>\n\t\t\t\t<view class=\"avatar-edit-icon\">\n\t\t\t\t\t<image class=\"edit-icon\" src=\"/static/icons/arrow_right.png\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 昵称部分 -->\n\t\t<view class=\"info-section\">\n\t\t\t<text class=\"section-label\">昵称</text>\n\t\t\t<view class=\"info-input-wrapper\">\n\t\t\t\t<input class=\"info-value\" type=\"nickname\" :value=\"userInfo.nickName\" @blur=\"onNicknameChange\" placeholder=\"请输入昵称\" />\n\t\t\t\t<view class=\"avatar-edit-icon\">\n\t\t\t\t\t<image class=\"edit-icon\" src=\"/static/icons/arrow_right.png\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 手机号部分 -->\n\t\t<view class=\"info-section\">\n\t\t\t<text class=\"section-label\">手机号</text>\n\t\t\t<view class=\"info-input-wrapper\">\n\t\t\t\t<text class=\"info-value\">{{userInfo.phone}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 退出登录按钮 -->\n\t\t<view class=\"logout-button-wrapper\">\n\t\t\t<button class=\"logout-button\" @click=\"logout\">退出登录</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport auth from '../../utils/auth.js';\n\timport { userApi } from '../../utils/api.js';\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tuserInfo: {\n\t\t\t\t\tnickName: '',\n\t\t\t\t\tphone: '',\n\t\t\t\t\tavatar: '/static/images/avatar.png'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// 从缓存或API获取用户信息\n\t\t\tconst userInfo = auth.getUserInfo();\n\t\t\tif (userInfo) {\n\t\t\t\tthis.userInfo = userInfo;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 微信小程序获取头像回调\n\t\t\tonChooseAvatar(e) {\n\t\t\t\tconst { avatarUrl } = e.detail;\n\t\t\t\tif (avatarUrl) {\n\t\t\t\t\tthis.userInfo.avatar = avatarUrl;\n\t\t\t\t\t\n\t\t\t\t\t// 更新用户信息到服务器\n\t\t\t\t\tthis.updateUserInfo();\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '头像更新成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 处理昵称变更\n\t\t\tonNicknameChange(e) {\n\t\t\t\tconst nickName = e.detail.value;\n\t\t\t\tif (nickName && nickName !== this.userInfo.nickName) {\n\t\t\t\t\tthis.userInfo.nickName = nickName;\n\t\t\t\t\t\n\t\t\t\t\t// 更新用户信息到服务器\n\t\t\t\t\tthis.updateUserInfo();\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '昵称更新成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 更新用户信息到服务器\n\t\t\tupdateUserInfo() {\n\t\t\t\t// 调用API更新用户信息\n\t\t\t\tuserApi.updateUserInfo({\n\t\t\t\t\tavatar: this.userInfo.avatar,\n\t\t\t\t\tnickName: this.userInfo.nickName\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log('用户信息更新成功', res);\n\t\t\t\t\t\n\t\t\t\t\t// 保存到缓存\n\t\t\t\t\tauth.setUserInfo(this.userInfo);\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('用户信息更新失败', err);\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 退出登录\n\t\t\tlogout() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '确定要退出登录吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 清除登录信息\n\t\t\t\t\t\t\tauth.clearLoginInfo();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 返回首页\n\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\npage {\n\tbackground-color: #F5F5F5;\n}\n\n.profile-container {\n\tpadding: 20rpx 0;\n}\n\n/* 通用部分样式 */\n.section-label {\n\tfont-size: 32rpx;\n\tcolor: #333;\n}\n\n/* 头像部分样式 */\n.avatar-section {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tbackground-color: #FFFFFF;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.avatar-wrapper {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.avatar-button {\n\tbackground: none;\n\tpadding: 0;\n\tmargin: 0;\n\tline-height: 1;\n\tborder: none;\n\toutline: none;\n\twidth: auto;\n\toverflow: visible;\n}\n\n.avatar-button::after {\n\tborder: none;\n}\n\n.avatar {\n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder-radius: 50%;\n\tmargin-right: 20rpx;\n}\n\n.avatar-edit-icon {\n\twidth: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.edit-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n}\n\n/* 信息部分样式 */\n.info-section {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tbackground-color: #FFFFFF;\n\tpadding: 30rpx;\n\tmargin-bottom: 2rpx; /* 很小的间距让分割线效果 */\n}\n\n.info-input-wrapper {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.info-value {\n\tfont-size: 32rpx;\n\tcolor: #666;\n\tmargin-right: 20rpx;\n\theight: 40rpx;\n\tborder: none;\n\tbackground-color: transparent;\n\ttext-align: right;\n}\n\n/* 退出登录按钮样式 */\n.logout-button-wrapper {\n\tpadding: 60rpx;\n\tmargin-top: 60rpx;\n}\n\n.logout-button {\n\twidth: 100%;\n\theight: 90rpx;\n\tline-height: 90rpx;\n\tbackground-color: #FFFFFF;\n\tcolor: #333;\n\tfont-size: 34rpx;\n\tborder-radius: 45rpx;\n\ttext-align: center;\n}\n</style> ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_profile.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_profile.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}