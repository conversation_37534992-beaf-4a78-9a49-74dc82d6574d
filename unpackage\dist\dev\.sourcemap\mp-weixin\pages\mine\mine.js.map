{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/pages/mine/mine.vue?0892", "webpack:///E:/购票系统/购票系统/pages/mine/mine.vue?91b5", "webpack:///E:/购票系统/购票系统/pages/mine/mine.vue?dc48", "webpack:///E:/购票系统/购票系统/pages/mine/mine.vue?4792", "uni-app:///pages/mine/mine.vue", "webpack:///E:/购票系统/购票系统/pages/mine/mine.vue?ee70"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "data", "userInfo", "nick<PERSON><PERSON>", "phone", "avatar", "couponCount", "statusBarHeight", "navbarHeight", "menuButtonInfo", "computed", "navbarStyle", "height", "paddingTop", "onLoad", "onShow", "auth", "methods", "setNavbarInfo", "console", "goToUserProfile", "uni", "url", "shareToEarn", "managePassengers", "goToCoupon"], "mappings": "yIAAA,MAGA,aACA,WAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,iIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,sBACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,gCCRvB,wHAAqqB,eAAG,G,0HCiExqB,e,EAEA,CACAC,gBACA,OACAC,UACAC,gBACAC,oBACAC,kCAEAC,cACAC,mBACAC,gBACAC,oBAGAC,UAEAC,uBACA,OACAC,8BACAC,wCAIAC,kBAEA,sBAEAC,kBAEA,8BACA,EACA,gBAGAC,+CAGAC,SAEAC,yBACA,IAEA,4BAEA,uCAGA,wDAGA,+BACA,0BACA,6BAGA,+BAGA,yCAEAC,wCACA,SACAA,6BAEA,wBACA,uBAKAC,2BACAC,cACAC,gDAOAC,uBACAF,cACAC,gDAKAE,4BACAH,cACAC,gEAKAG,sBACAJ,cACAC,yCAIA,c,4DCtKA,wHAA4vC,eAAG,G", "file": "pages/mine/mine.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/mine.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mine.vue?vue&type=template&id=dcbcfe34&\"\nvar renderjs\nimport script from \"./mine.vue?vue&type=script&lang=js&\"\nexport * from \"./mine.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mine.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/mine.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=template&id=dcbcfe34&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"mine-container\">\n\t\t<!-- 背景图片 -->\n\t\t<image class=\"background-image\" src=\"/static/images/minebackground.png\" mode=\"aspectFill\"></image>\n\t\t\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"custom-navbar\" :style=\"navbarStyle\">\n\t\t\t<!-- 状态栏占位 -->\n\t\t\t<view :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\t<!-- 导航栏内容区 -->\n\t\t\t<view class=\"navbar-content\">\n\t\t\t\t<text class=\"page-title\">我的</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 内容区域 - 添加上外边距，确保不被导航栏遮挡 -->\n\t\t<view class=\"content-area\" :style=\"{ paddingTop: navbarHeight + 'px' }\">\n\t\t\t<!-- 用户信息区域 -->\n\t\t\t<view class=\"user-info-section\">\n\t\t\t\t<view class=\"user-info-left\" @click=\"goToUserProfile\">\n\t\t\t\t\t<image class=\"user-avatar\" :src=\"userInfo.avatar\"></image>\n\t\t\t\t\t<view class=\"user-details\">\n\t\t\t\t\t\t<text class=\"username\">{{userInfo.nickName}}</text>\n\t\t\t\t\t\t<text class=\"user-phone\">{{userInfo.phone}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"user-info-right\" @click=\"goToCoupon\">\n\t\t\t\t\t<text class=\"coupon-text\">优惠券</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 常用工具区域 -->\n\t\t\t<view class=\"tools-section\">\n\t\t\t\t<text class=\"section-title\">常用工具</text>\n\t\t\t\t<view class=\"tools-grid\">\n\t\t\t\t\t<!-- 联系客服 -->\n\t\t\t\t\t<button class=\"tool-item\" open-type=\"contact\">\n\t\t\t\t\t\t<view class=\"tool-icon-container\">\n\t\t\t\t\t\t\t<image class=\"tool-icon\" src=\"/static/icons/customer_service.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"tool-name\">联系客服</text>\n\t\t\t\t\t</button>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 分享赚钱 -->\n\t\t\t\t\t<view class=\"tool-item\" @click=\"shareToEarn\">\n\t\t\t\t\t\t<view class=\"tool-icon-container\">\n\t\t\t\t\t\t\t<image class=\"tool-icon\" src=\"/static/icons/share_money.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"tool-name\">分享赚钱</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 乘车人管理 -->\n\t\t\t\t\t<view class=\"tool-item\" @click=\"managePassengers\">\n\t\t\t\t\t\t<view class=\"tool-icon-container\">\n\t\t\t\t\t\t\t<image class=\"tool-icon\" src=\"/static/icons/passenger_manage.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"tool-name\">乘车人管理</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport auth from '../../utils/auth.js';\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tuserInfo: {\n\t\t\t\t\tnickName: '未知用户',\n\t\t\t\t\tphone: '153****1696',\n\t\t\t\t\tavatar: '/static/images/user.png'\n\t\t\t\t},\n\t\t\t\tcouponCount: 5,\n\t\t\t\tstatusBarHeight: 20, // 状态栏高度\n\t\t\t\tnavbarHeight: 80,    // 导航栏总高度\n\t\t\t\tmenuButtonInfo: {},  // 胶囊按钮信息\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 计算导航栏的样式\n\t\t\tnavbarStyle() {\n\t\t\t\treturn {\n\t\t\t\t\theight: this.navbarHeight + 'px',\n\t\t\t\t\tpaddingTop: this.statusBarHeight + 'px'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// 获取系统信息\n\t\t\tthis.setNavbarInfo();\n\t\t},\n\t\tonShow() {\n\t\t\t// 每次页面显示时检查是否有更新的用户信息\n\t\t\tconst userInfo = auth.getUserInfo();\n\t\t\tif (userInfo) {\n\t\t\t\tthis.userInfo = userInfo;\n\t\t\t} else {\n\t\t\t\t// 如果缓存中没有用户信息，跳转到登录页\n\t\t\t\tauth.navigateToLogin('/pages/mine/mine');\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 设置导航栏信息\n\t\t\tsetNavbarInfo() {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取系统信息\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\t// 获取状态栏高度\n\t\t\t\t\tthis.statusBarHeight = systemInfo.statusBarHeight;\n\t\t\t\t\t\n\t\t\t\t\t// 获取胶囊按钮位置信息\n\t\t\t\t\tthis.menuButtonInfo = uni.getMenuButtonBoundingClientRect();\n\t\t\t\t\t\n\t\t\t\t\t// 计算导航栏高度\n\t\t\t\t\tconst menuBottom = this.menuButtonInfo.bottom;\n\t\t\t\t\tconst menuTop = this.menuButtonInfo.top;\n\t\t\t\t\tconst menuHeight = this.menuButtonInfo.height;\n\t\t\t\t\t\n\t\t\t\t\t// 计算导航栏内容区高度\n\t\t\t\t\tconst contentHeight = menuHeight + (menuTop - this.statusBarHeight) * 2;\n\t\t\t\t\t\n\t\t\t\t\t// 设置导航栏总高度\n\t\t\t\t\tthis.navbarHeight = this.statusBarHeight + contentHeight;\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('导航栏高度:', this.navbarHeight);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取系统信息失败:', e);\n\t\t\t\t\t// 设置默认高度\n\t\t\t\t\tthis.statusBarHeight = 20;\n\t\t\t\t\tthis.navbarHeight = 60;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 跳转到用户资料页\n\t\t\tgoToUserProfile() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/subpkg-user/user_profile/user_profile'\n\t\t\t\t});\n\t\t\t},\n\n\n\n\t\t\t// 分享赚钱\n\t\t\tshareToEarn() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/subpkg-activity/share_earn/share_earn'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 乘车人管理\n\t\t\tmanagePassengers() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/subpkg-user/passenger_management/passenger_management'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 跳转到优惠券页面\n\t\t\tgoToCoupon() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/subpkg-booking/coupon/coupon'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\npage {\n\tmin-height: 100vh;\n\tbackground-color: #F6F7F9;\n}\n\n.mine-container {\n\tmin-height: 100vh;\n\tposition: relative;\n}\n\n/* 背景图片样式 */\n.background-image {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tz-index: -1;\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\tz-index: 999;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.navbar-content {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\theight: 44px; /* 默认高度，会被JS动态修改 */\n}\n\n.page-title {\n\tcolor: #000000;\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n}\n\n/* 内容区域 */\n.content-area {\n\twidth: 100%;\n\tbox-sizing: border-box;\n}\n\n/* 用户信息区域样式 */\n.user-info-section {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 0 30rpx;\n\tborder-radius: 12rpx;\n\tposition: relative;\n\tz-index: 1;\n\tmargin: 20rpx;\n}\n\n.user-info-left {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.user-avatar {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\tmargin-right: 20rpx;\n}\n\n.user-details {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.username {\n\tfont-size: 36rpx;\n\tcolor: #333333;\n\tfont-weight: bold;\n\tmargin-bottom: 8rpx;\n}\n\n.user-phone {\n\tfont-size: 28rpx;\n\tcolor: #999999;\n}\n\n.user-info-right {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tcursor: pointer;\n}\n\n.coupon-count {\n\tfont-size: 48rpx;\n\tcolor: #333333;\n\tfont-weight: bold;\n\tline-height: 1;\n}\n\n.coupon-text {\n\tfont-size: 28rpx;\n\tcolor: #999999;\n\tmargin-top: 8rpx;\n}\n\n/* 常用工具区域样式 */\n.tools-section {\n\tmargin: 20rpx;\n\tbackground-color: #FFFFFF;\n\tpadding: 30rpx;\n\tborder-radius: 12rpx;\n\tposition: relative;\n\tz-index: 1;\n}\n\n.section-title {\n\tfont-size: 33rpx;\n\tcolor: #333333;\n\tfont-weight: bold;\n}\n\n.tools-grid {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tmargin-top: 30rpx;\n}\n\n.tool-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\twidth: 33.33%;\n\tbackground-color: transparent !important;\n\tborder: none !important;\n\toutline: none !important;\n\tpadding: 0 !important;\n\tmargin: 0 !important;\n\tfont-size: inherit;\n\tline-height: inherit;\n\tbox-shadow: none !important;\n}\n\n.tool-item::after {\n\tborder: none !important;\n}\n\n/* 专门针对button组件的样式 */\nbutton.tool-item {\n\tbackground: transparent !important;\n\tborder: 0 !important;\n\tborder-radius: 0 !important;\n\toutline: 0 !important;\n\t-webkit-appearance: none !important;\n\tappearance: none !important;\n}\n\nbutton.tool-item::after {\n\tborder: none !important;\n\tcontent: none !important;\n}\n\n.tool-icon-container {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tbackground-color: #F6F7F9;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tmargin-bottom: 16rpx;\n}\n\n.tool-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n}\n\n.tool-name {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n}\n</style> ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}