<view class="content data-v-37c11de4"><view class="form-item data-v-37c11de4"><view class="form-label data-v-37c11de4">姓名</view><input class="form-input data-v-37c11de4" placeholder="请输入姓名" placeholder-style="color: #cccccc;" data-event-opts="{{[['input',[['__set_model',['','name','$event',[]]]]]]}}" value="{{name}}" bindinput="__e"/></view><view class="form-item data-v-37c11de4"><view class="form-label data-v-37c11de4">证件类型</view><view class="form-value data-v-37c11de4">身份证</view></view><view class="form-item last-item data-v-37c11de4"><view class="form-label data-v-37c11de4">证件号</view><input class="form-input data-v-37c11de4" placeholder="请输入证件号" placeholder-style="color: #cccccc;" type="idcard" maxlength="18" data-event-opts="{{[['input',[['__set_model',['','idCard','$event',[]]]]]]}}" value="{{idCard}}" bindinput="__e"/></view><view class="tips data-v-37c11de4"><text class="tips-text data-v-37c11de4">提示：点击保存表示您已阅读并同意以下内容</text><text class="tips-text data-v-37c11de4">您知悉您录入的乘车人身份证件信息，将用于您的汽车票等所有需要实名制的产品，并在使用时进行验证，请确保此信息真实有效。</text></view><view class="{{['save-button','data-v-37c11de4',(!isEdit)?'save-button-no-delete':'']}}" hover-class="button-hover" data-event-opts="{{[['tap',[['savePassenger',['$event']]]]]}}" bindtap="__e">保存乘客</view><block wx:if="{{isEdit}}"><view class="delete-button data-v-37c11de4" hover-class="delete-hover" data-event-opts="{{[['tap',[['deletePassenger',['$event']]]]]}}" bindtap="__e">删除信息</view></block></view>