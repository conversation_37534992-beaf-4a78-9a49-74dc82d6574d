<view class="content data-v-d11afc2c"><scroll-view class="date-scroll data-v-d11afc2c" scroll-x="true" show-scrollbar="false" scroll-left="{{scrollLeft}}"><view class="date-list data-v-d11afc2c"><block wx:for="{{dateList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectDate',[index]]]]]}}" class="{{['data-v-d11afc2c','date-item',[(currentDateIndex===index)?'date-item-active':'']]}}" bindtap="__e"><text class="week-day data-v-d11afc2c">{{item.weekDay}}</text><view class="{{['data-v-d11afc2c','day-num',[(item.isToday)?'today-circle':'']]}}"><block wx:if="{{item.isToday}}"><text style="font-size:24rpx;" class="data-v-d11afc2c">今天</text></block><block wx:else><text style="font-size:28rpx;" class="data-v-d11afc2c">{{item.day}}</text></block></view></view></block></view></scroll-view><view data-event-opts="{{[['tap',[['showDatePicker',['$event']]]]]}}" class="calendar-icon data-v-d11afc2c" bindtap="__e"><image class="calendar-img data-v-d11afc2c" src="/static/icons/calendar.png"></image></view><view class="ticket-list data-v-d11afc2c"><block wx:if="{{loading}}"><view class="loading-container data-v-d11afc2c"><view class="loading-text data-v-d11afc2c">{{loadingText}}</view></view></block><block wx:else><block wx:if="{{$root.g0}}"><view class="empty-container data-v-d11afc2c"><view class="empty-text data-v-d11afc2c">暂无车票信息</view><view class="empty-tip data-v-d11afc2c">请尝试选择其他日期</view></view></block><block wx:else><block wx:for="{{$root.l0}}" wx:for-item="ticket" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goToTicketConfirm',['$0'],[[['ticketList','',index]]]]]]]}}" class="{{['data-v-d11afc2c','ticket-item',ticket.$orig.type,[(ticket.$orig.status==='无票')?'ticket-disabled':'']]}}" bindtap="__e"><block wx:if="{{ticket.$orig.isImmediateDeparture}}"><text class="departure-tag data-v-d11afc2c">即将发车</text></block><view class="ticket-content data-v-d11afc2c"><view class="ticket-left data-v-d11afc2c"><view class="time-section data-v-d11afc2c"><text class="ticket-time data-v-d11afc2c">{{ticket.$orig.time}}</text><text class="ticket-duration data-v-d11afc2c">{{"约"+ticket.$orig.duration+'小时'}}<text class="info-icon data-v-d11afc2c">ⓘ</text></text></view><view class="ticket-type-tag data-v-d11afc2c"><text class="type-text data-v-d11afc2c">{{ticket.$orig.typeName}}</text></view></view><view class="ticket-stations data-v-d11afc2c"><view class="station-line data-v-d11afc2c"><view class="station-dot start data-v-d11afc2c"></view><view class="station-connector data-v-d11afc2c"></view><view class="station-dot end data-v-d11afc2c"></view></view><view class="station-names data-v-d11afc2c"><text class="station-name data-v-d11afc2c">{{departureStation}}</text><text class="station-name data-v-d11afc2c">{{arrivalStation}}</text></view></view><view class="ticket-right data-v-d11afc2c"><view class="price-box data-v-d11afc2c"><view class="price-original data-v-d11afc2c"><text class="price-through data-v-d11afc2c">{{"¥"+ticket.g1}}</text></view><view class="price-current data-v-d11afc2c"><text class="price-symbol data-v-d11afc2c">¥</text><text class="price-value data-v-d11afc2c">{{ticket.$orig.currentPrice}}</text></view></view><view class="discount-tag data-v-d11afc2c"><text class="data-v-d11afc2c">{{"优惠¥"+ticket.$orig.discount}}</text></view><block wx:if="{{ticket.g2}}"><text class="ticket-status data-v-d11afc2c" style="color:red;">{{ticket.$orig.status}}</text></block><block wx:else><text class="ticket-status data-v-d11afc2c">{{ticket.$orig.status}}</text></block></view></view></view></block></block></block></view><block wx:if="{{showCalendar}}"><view class="calendar-popup data-v-d11afc2c"><view data-event-opts="{{[['tap',[['cancelDatePicker',['$event']]]]]}}" class="calendar-mask data-v-d11afc2c" bindtap="__e"></view><view class="calendar-container data-v-d11afc2c"><view class="calendar-header custom-calendar-header data-v-d11afc2c"><text data-event-opts="{{[['tap',[['cancelDatePicker',['$event']]]]]}}" class="calendar-close data-v-d11afc2c" bindtap="__e">×</text><view class="calendar-nav data-v-d11afc2c"><text class="calendar-title data-v-d11afc2c">{{currentYear+"年"+currentMonth+"月"}}</text></view></view><uni-calendar class="custom-calendar data-v-d11afc2c vue-ref" vue-id="56de3c5c-1" insert="{{true}}" lunar="{{false}}" start-date="{{startDate}}" end-date="{{endDate}}" data-ref="calendar" data-event-opts="{{[['^change',[['calendarChange']]],['^monthSwitch',[['monthSwitch']]]]}}" bind:change="__e" bind:monthSwitch="__e" bind:__l="__l"></uni-calendar><view class="calendar-footer data-v-d11afc2c"><view data-event-opts="{{[['tap',[['confirmDatePicker',['$event']]]]]}}" class="calendar-confirm data-v-d11afc2c" bindtap="__e">确认</view></view></view></view></block></view>