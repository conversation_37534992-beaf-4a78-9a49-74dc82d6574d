<view class="content data-v-57280228"><view class="header data-v-57280228"><image class="banner-image data-v-57280228" src="/static/images/banner.png" mode="aspectFill"></image></view><view class="station-selector-container data-v-57280228"><view class="station-selector data-v-57280228"><view data-event-opts="{{[['tap',[['selectDeparture',['$event']]]]]}}" class="station-item data-v-57280228" bindtap="__e"><text class="station-name data-v-57280228">{{departureStation}}</text></view><view data-event-opts="{{[['tap',[['switchStations',['$event']]]]]}}" class="switch-btn data-v-57280228" bindtap="__e"><image class="{{['data-v-57280228','switch-icon',[(isRotating)?'rotating':'']]}}" src="/static/images/switch.png" mode="aspectFit"></image></view><view data-event-opts="{{[['tap',[['selectArrival',['$event']]]]]}}" class="station-item data-v-57280228" bindtap="__e"><text class="station-name data-v-57280228">{{arrivalStation}}</text></view></view></view><view data-event-opts="{{[['tap',[['showDatePicker',['$event']]]]]}}" class="date-selector data-v-57280228" bindtap="__e"><text class="date data-v-57280228">{{date}}</text><text class="week data-v-57280228">{{week}}</text><block wx:if="{{isToday}}"><text class="today data-v-57280228">今天</text></block></view><view data-event-opts="{{[['tap',[['queryTickets',['$event']]]]]}}" class="query-btn data-v-57280228" bindtap="__e">查询车票</view><view class="history data-v-57280228"><scroll-view class="history-scroll data-v-57280228" scroll-x="true" show-scrollbar="false"><view class="history-scroll-content data-v-57280228"><block wx:for="{{historyList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['useHistory',['$0'],[[['historyList','',index]]]]]]]}}" class="history-item data-v-57280228" bindtap="__e"><text class="history-text data-v-57280228">{{item}}</text></view></block></view></scroll-view><view class="history-header data-v-57280228"><text data-event-opts="{{[['tap',[['clearHistory',['$event']]]]]}}" class="history-clear data-v-57280228" bindtap="__e">清除历史</text></view></view><block wx:if="{{showCalendar}}"><view class="calendar-popup data-v-57280228"><view data-event-opts="{{[['tap',[['cancelDatePicker',['$event']]]]]}}" class="calendar-mask data-v-57280228" bindtap="__e"></view><view class="calendar-container data-v-57280228"><view class="calendar-header custom-calendar-header data-v-57280228"><text data-event-opts="{{[['tap',[['cancelDatePicker',['$event']]]]]}}" class="calendar-close data-v-57280228" bindtap="__e">×</text><text class="calendar-title data-v-57280228">请选择日期</text></view><uni-calendar class="custom-calendar data-v-57280228 vue-ref" vue-id="8dd740cc-1" insert="{{true}}" lunar="{{false}}" start-date="{{startDate}}" end-date="{{endDate}}" data-ref="calendar" data-event-opts="{{[['^change',[['calendarChange']]],['^monthSwitch',[['monthSwitch']]]]}}" bind:change="__e" bind:monthSwitch="__e" bind:__l="__l"></uni-calendar><view class="calendar-footer data-v-57280228"><view data-event-opts="{{[['tap',[['confirmDatePicker',['$event']]]]]}}" class="calendar-confirm data-v-57280228" bindtap="__e">确认</view></view></view></view></block><block wx:if="{{showStationPicker}}"><view class="station-popup data-v-57280228"><view data-event-opts="{{[['tap',[['cancelStationPicker',['$event']]]]]}}" class="station-mask data-v-57280228" bindtap="__e"></view><view class="{{['data-v-57280228','station-container',[(stationAnimateShow)?'station-container-show':'']]}}"><view class="station-header data-v-57280228"><text data-event-opts="{{[['tap',[['cancelStationPicker',['$event']]]]]}}" class="station-close data-v-57280228" bindtap="__e">×</text><text class="station-title data-v-57280228">{{isSelectingDeparture?'出发地':'到达地'}}</text></view><view class="station-tip data-v-57280228"><text class="tip-icon data-v-57280228">⚠</text><text class="tip-text data-v-57280228">提示：如有疑问，可在微信群中联系客服咨询～</text></view><view class="station-content data-v-57280228"><scroll-view class="city-list data-v-57280228" scroll-y="true"><block wx:for="{{cities}}" wx:for-item="city" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectCity',[index]]]]]}}" class="{{['data-v-57280228','city-item',[(currentCityIndex===index)?'city-item-active':'']]}}" bindtap="__e"><text class="city-name data-v-57280228">{{city.addressName}}</text></view></block></scroll-view><scroll-view class="place-list data-v-57280228" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="place" wx:for-index="index" wx:key="index"><block wx:if="{{place.g0}}"><view data-event-opts="{{[['tap',[['selectPlace',['$0'],[[['currentCity.sysAddressDto','',index]]]]]]]}}" class="{{['data-v-57280228','place-item',[(place.m0)?'place-item-selected':'']]}}" bindtap="__e"><text class="place-name data-v-57280228">{{place.$orig.addressName}}</text><block wx:if="{{place.m1}}"><text class="place-check data-v-57280228">✓</text></block></view></block></block><block wx:if="{{$root.g1}}"><view class="no-place data-v-57280228"><text class="no-place-text data-v-57280228">暂无站点</text></view></block></scroll-view></view><view class="station-footer data-v-57280228"><view data-event-opts="{{[['tap',[['confirmStationPicker',['$event']]]]]}}" class="station-confirm data-v-57280228" bindtap="__e">确认</view></view></view></view></block></view>