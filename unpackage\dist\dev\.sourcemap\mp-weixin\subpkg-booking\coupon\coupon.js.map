{"version": 3, "sources": ["webpack:///E:/购票系统/购票系统/subpkg-booking/coupon/coupon.vue?be09", "uni-app:///subpkg-booking/coupon/coupon.vue", "webpack:///E:/购票系统/购票系统/subpkg-booking/coupon/coupon.vue?30e6", "uni-app:///main.js", "webpack:///E:/购票系统/购票系统/subpkg-booking/coupon/coupon.vue?0fea", "webpack:///E:/购票系统/购票系统/subpkg-booking/coupon/coupon.vue?c32d", "webpack:///E:/购票系统/购票系统/subpkg-booking/coupon/coupon.vue?ec70"], "names": ["data", "activeTab", "coupons", "loading", "onLoad", "onShow", "computed", "currentCoupons", "methods", "loadCoupons", "request", "response", "uni", "title", "icon", "console", "processCouponsData", "status", "timeText", "id", "amount", "minAmount", "expireTime", "startTime", "endTime", "type", "formatDate", "switchTab", "useCoupon", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "length", "$mp", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped"], "mappings": "6HAAA,yHAAuqB,eAAG,G,qJC8E1qB,W,GACA,SAEA,CACAA,gBACA,OACAC,sBACAC,WACAC,aAGAC,kBACA,oBAGAC,kBAEA,oBAGAC,UAEAC,0BAAA,WACA,wCACA,gCACA,uBACA,yBACA,0BAMAC,SAEAC,uBAAA,0IAKA,OALA,SAEA,aAGA,SACAC,uCAAA,OAAAC,SAGA,qBACA,uCAEAC,aACAC,uBACAC,cAEA,mDAEAC,iCACAH,aACAC,gBACAC,cACA,QAEA,OAFA,UAEA,wFAxBA,IA6BAE,+BAAA,WACA,0BAEA,gCACA,0BAGA,cACA,WACAC,cACA,WACAA,SACA,aACAA,aAIA,SASA,OANAC,EAFA,cAEAA,2BAGAA,EAGA,CACAC,uBACAC,wBACAC,YACAR,0BACAS,aACAL,SACAM,YACAC,UACAC,iBAMAC,uBACA,eAEA,IACA,kBACA,kBACA,yCACA,sCACA,+CACA,SAEA,OADAX,4BACA,IAKAY,sBACA,kBAIAC,sBACA,sBAIAhB,aACAC,gBACAC,kBASA,c,6DC5NA,yHAA8vC,eAAG,G,gFCAjwC,MAGA,aACA,WAFAe,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,mIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,mCACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIjC,eAAewC,QAC5BP,EAAIQ,IAAIhD,KAAOiD,OAAOC,OACpB,GACA,CACEC,MAAO,CACLP,GAAIA,MAKRQ,GAAmB,EACnBC,EAAkB,GACtBd,EAAOe,eAAgB,I", "file": "subpkg-booking/coupon/coupon.js", "sourcesContent": ["import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupon.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"coupon-container\">\r\n\t\t<!-- 标签页导航 -->\r\n\t\t<view class=\"tab-nav\">\r\n\t\t\t<view \r\n\t\t\t\tclass=\"tab-item\" \r\n\t\t\t\t:class=\"{ active: activeTab === 'available' }\"\r\n\t\t\t\t@click=\"switchTab('available')\"\r\n\t\t\t>\r\n\t\t\t\t<text class=\"tab-text\">待使用</text>\r\n\t\t\t\t<view class=\"tab-indicator\" v-if=\"activeTab === 'available'\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view \r\n\t\t\t\tclass=\"tab-item\" \r\n\t\t\t\t:class=\"{ active: activeTab === 'expired' }\"\r\n\t\t\t\t@click=\"switchTab('expired')\"\r\n\t\t\t>\r\n\t\t\t\t<text class=\"tab-text\">已过期</text>\r\n\t\t\t\t<view class=\"tab-indicator\" v-if=\"activeTab === 'expired'\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 加载状态 -->\r\n\t\t<view class=\"loading-container\" v-if=\"loading\">\r\n\t\t\t<text class=\"loading-text\">加载中...</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 优惠券列表 -->\r\n\t\t<view class=\"coupon-list\" v-else>\r\n\t\t\t<view \r\n\t\t\t\tclass=\"coupon-item\" \r\n\t\t\t\tv-for=\"(coupon, index) in currentCoupons\" \r\n\t\t\t\t:key=\"index\"\r\n\t\t\t>\r\n\t\t\t\t<image\r\n\t\t\t\t\tclass=\"coupon-bg\"\r\n\t\t\t\t\t:src=\"coupon.status === 'available' ? '/subpkg-booking/static/icons/coupon_yes.png' : '/static/icons/coupon_no.png'\"\r\n\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t></image>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"coupon-content\">\r\n\t\t\t\t\t<!-- 左侧金额区域 -->\r\n\t\t\t\t\t<view class=\"coupon-left\">\r\n\t\t\t\t\t\t<view class=\"amount-container\">\r\n\t\t\t\t\t\t\t<text class=\"currency\" :class=\"{ expired: coupon.status === 'expired' }\">¥</text>\r\n\t\t\t\t\t\t\t<text class=\"amount\" :class=\"{ expired: coupon.status === 'expired' }\">{{ coupon.amount }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"condition\" :class=\"{ expired: coupon.status === 'expired' }\">无门槛</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 右侧信息区域 -->\r\n\t\t\t\t\t<view class=\"coupon-right\">\r\n\t\t\t\t\t\t<view class=\"coupon-info\">\r\n\t\t\t\t\t\t\t<text class=\"coupon-title\" :class=\"{ expired: coupon.status === 'expired' }\">{{ coupon.title }}</text>\r\n\t\t\t\t\t\t\t<text class=\"coupon-expire\" :class=\"{ expired: coupon.status === 'expired' }\">\r\n\t\t\t\t\t\t\t\t{{ coupon.status === 'expired' ? coupon.expireTime : '有效期至：' + coupon.expireTime }}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tclass=\"use-btn\" \r\n\t\t\t\t\t\t\t:class=\"{ disabled: coupon.status === 'expired' }\"\r\n\t\t\t\t\t\t\t@click=\"useCoupon(coupon)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"use-btn-text\">{{ coupon.status === 'available' ? '去使用' : '已过期' }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 空状态 -->\r\n\t\t<view class=\"empty-state\" v-if=\"currentCoupons.length === 0\">\r\n\t\t\t<text class=\"empty-text\">暂无{{ activeTab === 'available' ? '待使用' : '已过期' }}的优惠券</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport request from '../../utils/request.js';\r\n\timport auth from '../../utils/auth.js';\r\n\t\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tactiveTab: 'available', // available: 待使用, expired: 已过期\r\n\t\t\t\tcoupons: [],\r\n\t\t\t\tloading: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.loadCoupons();\r\n\t\t},\r\n\t\t\r\n\t\tonShow() {\r\n\t\t\t// 页面显示时刷新数据\r\n\t\t\tthis.loadCoupons();\r\n\t\t},\r\n\t\t\r\n\t\tcomputed: {\r\n\t\t\t// 当前显示的优惠券列表\r\n\t\t\tcurrentCoupons() {\r\n\t\t\t\treturn this.coupons.filter(coupon => {\r\n\t\t\t\t\tif (this.activeTab === 'available') {\r\n\t\t\t\t\t\treturn coupon.status === 'available';\r\n\t\t\t\t\t} else if (this.activeTab === 'expired') {\r\n\t\t\t\t\t\treturn coupon.status === 'expired';\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 加载优惠券列表\r\n\t\t\tasync loadCoupons() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.loading = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 调用接口获取优惠券列表\r\n\t\t\t\t\tconst response = await request.get('/app/coupon/user/list');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理接口返回的数据\r\n\t\t\t\t\tif (response.code === 200 && response.data) {\r\n\t\t\t\t\t\tthis.coupons = this.processCouponsData(response.data);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: response.msg || '获取优惠券失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取优惠券列表失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取优惠券失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理接口返回的优惠券数据\r\n\t\t\tprocessCouponsData(data) {\r\n\t\t\t\treturn data.map(item => {\r\n\t\t\t\t\t// 格式化时间\r\n\t\t\t\t\tconst startTime = this.formatDate(item.startTime);\r\n\t\t\t\t\tconst endTime = this.formatDate(item.endTime);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 判断优惠券状态\r\n\t\t\t\t\tlet status = 'available';\r\n\t\t\t\t\tif (item.type === 0) {\r\n\t\t\t\t\t\tstatus = 'available'; // 未使用\r\n\t\t\t\t\t} else if (item.type === 1) {\r\n\t\t\t\t\t\tstatus = 'used'; // 已使用\r\n\t\t\t\t\t} else if (item.type === 2) {\r\n\t\t\t\t\t\tstatus = 'expired'; // 已过期\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 生成时间显示文本\r\n\t\t\t\t\tlet timeText = '';\r\n\t\t\t\t\tif (status === 'expired') {\r\n\t\t\t\t\t\t// 已过期显示起止日期\r\n\t\t\t\t\t\ttimeText = `${startTime}-${endTime}`;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 待使用显示有效期至\r\n\t\t\t\t\t\ttimeText = endTime;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tid: item.id || Math.random(),\r\n\t\t\t\t\t\tamount: item.balancePice || 0,\r\n\t\t\t\t\t\tminAmount: 0, // 无门槛\r\n\t\t\t\t\t\ttitle: item.couponName || '优惠券',\r\n\t\t\t\t\t\texpireTime: timeText,\r\n\t\t\t\t\t\tstatus: status,\r\n\t\t\t\t\t\tstartTime: startTime,\r\n\t\t\t\t\t\tendTime: endTime,\r\n\t\t\t\t\t\ttype: item.type\r\n\t\t\t\t\t};\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化日期\r\n\t\t\tformatDate(dateStr) {\r\n\t\t\t\tif (!dateStr) return '';\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst date = new Date(dateStr);\r\n\t\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\t\t\treturn `${year}.${month}.${day}`;\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('日期格式化失败:', error);\r\n\t\t\t\t\treturn dateStr;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 切换标签页\r\n\t\t\tswitchTab(tab) {\r\n\t\t\t\tthis.activeTab = tab;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 使用优惠券\r\n\t\t\tuseCoupon(coupon) {\r\n\t\t\t\tif (coupon.status === 'expired') {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '跳转到购票页面',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 这里可以跳转到购票页面并带上优惠券信息\r\n\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t// \turl: `/pages/index/index?couponId=${coupon.id}`\r\n\t\t\t\t// });\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\npage {\r\n\tbackground-color: #F6F7F9;\r\n}\r\n\r\n.coupon-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #F6F7F9;\r\n}\r\n\r\n/* 标签页导航 */\r\n.tab-nav {\r\n\tdisplay: flex;\r\n\tbackground-color: #FFFFFF;\r\n\tpadding: 0 40rpx;\r\n}\r\n\r\n.tab-item {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tpadding: 30rpx 0;\r\n\tposition: relative;\r\n}\r\n\r\n.tab-text {\r\n\tfont-size: 32rpx;\r\n\tcolor: #999999;\r\n\ttransition: color 0.3s;\r\n}\r\n\r\n.tab-item.active .tab-text {\r\n\tcolor: #303133;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.tab-indicator {\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\twidth: 60rpx;\r\n\theight: 6rpx;\r\n\tbackground-color: #3F8DF9;\r\n\tborder-radius: 3rpx;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\theight: 400rpx;\r\n}\r\n\r\n.loading-text {\r\n\tfont-size: 32rpx;\r\n\tcolor: #999999;\r\n}\r\n\r\n/* 优惠券列表 */\r\n.coupon-list {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.coupon-item {\r\n\tposition: relative;\r\n\tmargin-bottom: 30rpx;\r\n\theight: 200rpx;\r\n\tborder-radius: 16rpx;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.coupon-bg {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tz-index: 0;\r\n}\r\n\r\n.coupon-content {\r\n\tposition: relative;\r\n\tz-index: 1;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\theight: 100%;\r\n\tpadding: 0 40rpx;\r\n}\r\n\r\n/* 左侧金额区域 */\r\n.coupon-left {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: flex-start;\r\n\twidth: 160rpx;\r\n\tmargin-right: 60rpx;\r\n}\r\n\r\n.amount-container {\r\n\tdisplay: flex;\r\n\talign-items: baseline;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.currency {\r\n\tfont-size: 40rpx;\r\n\tcolor: #FFFFFF;\r\n\tfont-weight: bold;\r\n\tmargin-right: 8rpx;\r\n}\r\n\r\n.currency.expired {\r\n\tcolor: #303133;\r\n}\r\n\r\n.amount {\r\n\tfont-size: 54rpx;\r\n\tcolor: #FFFFFF;\r\n\tfont-weight: bold;\r\n\tline-height: 1;\r\n}\r\n\r\n.amount.expired {\r\n\tcolor: #303133;\r\n}\r\n\r\n.condition {\r\n\tfont-size: 28rpx;\r\n\tcolor: #FFFFFF;\r\n\topacity: 0.9;\r\n\tmargin-left: 25rpx;\r\n}\r\n\r\n.condition.expired {\r\n\tcolor: #909399;\r\n\topacity: 1;\r\n}\r\n\r\n/* 分割线 */\r\n.coupon-divider {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\theight: 140rpx;\r\n\tmargin: 0 30rpx;\r\n}\r\n\r\n.dot {\r\n\twidth: 16rpx;\r\n\theight: 16rpx;\r\n\tbackground-color: #F6F7F9;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.dot-top {\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.dot-bottom {\r\n\tmargin-top: 8rpx;\r\n}\r\n\r\n.dash-line {\r\n\tflex: 1;\r\n\twidth: 2rpx;\r\n\tbackground-image: linear-gradient(to bottom, #FFFFFF 50%, transparent 50%);\r\n\tbackground-size: 100% 16rpx;\r\n\tbackground-repeat: repeat-y;\r\n\topacity: 0.6;\r\n}\r\n\r\n/* 右侧信息区域 */\r\n.coupon-right {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\theight: 100%;\r\n}\r\n\r\n.coupon-info {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tjustify-content: center;\r\n}\r\n\r\n.coupon-title {\r\n\tfont-size: 36rpx;\r\n\tcolor: #FFFFFF;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 12rpx;\r\n}\r\n\r\n.coupon-title.expired {\r\n\tcolor: #303133;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.coupon-expire {\r\n\tfont-size: 22rpx;\r\n\tcolor: #FFFFFF;\r\n\topacity: 0.9;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.coupon-expire.expired {\r\n\tcolor: #909399;\r\n\topacity: 1;\r\n}\r\n\r\n/* 使用按钮 */\r\n.use-btn {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 8rpx 16rpx;\r\n\tmin-width: 80rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tmargin-left: 20rpx;\r\n}\r\n\r\n.use-btn.disabled {\r\n\tbackground-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.use-btn-text {\r\n\tfont-size: 22rpx;\r\n\tcolor: #FF6B35;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.use-btn.disabled .use-btn-text {\r\n\tcolor: #FFFFFF;\r\n\topacity: 0.7;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\theight: 400rpx;\r\n}\r\n\r\n.empty-text {\r\n\tfont-size: 32rpx;\r\n\tcolor: #999999;\r\n}\r\n</style> ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupon.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupon.vue?vue&type=style&index=0&lang=scss&\"", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg-booking/coupon/coupon.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coupon.vue?vue&type=template&id=9baacbf4&\"\nvar renderjs\nimport script from \"./coupon.vue?vue&type=script&lang=js&\"\nexport * from \"./coupon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coupon.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg-booking/coupon/coupon.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupon.vue?vue&type=template&id=9baacbf4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.currentCoupons.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }"], "sourceRoot": ""}