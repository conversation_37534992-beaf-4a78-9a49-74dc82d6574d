@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-37c11de4 {
  background-color: #f5f7fa;
}
.content.data-v-37c11de4 {
  padding: 20rpx;
  padding-top: 30rpx;
}
.form-item.data-v-37c11de4 {
  background-color: #fff;
  padding: 30rpx 24rpx;
  margin-bottom: 2rpx;
  display: flex;
  align-items: center;
  position: relative;
}
.form-item.data-v-37c11de4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  background-color: #f0f0f0;
}
.form-item.data-v-37c11de4:last-child::after {
  display: none;
}
.form-item.data-v-37c11de4:first-child {
  border-radius: 16rpx 16rpx 0 0;
}
.last-item.data-v-37c11de4 {
  border-radius: 0 0 16rpx 16rpx;
  margin-bottom: 40rpx;
}
.form-label.data-v-37c11de4 {
  width: 180rpx;
  font-size: 32rpx;
  color: #333;
  font-weight: 400;
}
.form-input.data-v-37c11de4 {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  height: 60rpx;
}
.form-value.data-v-37c11de4 {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}
.tips.data-v-37c11de4 {
  padding: 0 24rpx;
  margin-bottom: 80rpx;
}
.tips-text.data-v-37c11de4 {
  display: block;
  font-size: 26rpx;
  color: #999;
  line-height: 1.6;
  margin-bottom: 10rpx;
}
.save-button.data-v-37c11de4 {
  margin: 0 24rpx;
  height: 90rpx;
  background-color: #3F8DF9;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(63, 141, 249, 0.3);
}
.save-button-no-delete.data-v-37c11de4 {
  margin-bottom: 80rpx;
}
.delete-button.data-v-37c11de4 {
  margin: 30rpx 24rpx;
  height: 90rpx;
  background-color: #fff;
  color: #FF3B30;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 45rpx;
  border: 1rpx solid rgba(255, 59, 48, 0.6);
}
/* 按钮点击状态 */
.button-hover.data-v-37c11de4 {
  opacity: 0.9;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.delete-hover.data-v-37c11de4 {
  background-color: #FFF5F5;
  color: #FF3B30;
}
