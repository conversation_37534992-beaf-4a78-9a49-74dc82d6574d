<view class="record-container"><view class="tab-container"><view data-event-opts="{{[['tap',[['switchTab',[0]]]]]}}" class="{{['tab-item',(activeTab===0)?'active':'']}}" bindtap="__e"><text class="tab-text">全部订单</text><block wx:if="{{activeTab===0}}"><view class="active-line"></view></block></view><view data-event-opts="{{[['tap',[['switchTab',[1]]]]]}}" class="{{['tab-item',(activeTab===1)?'active':'']}}" bindtap="__e"><text class="tab-text">待出行<block wx:if="{{pendingCount>0}}"><text class="tab-count">{{"("+pendingCount+")"}}</text></block></text><block wx:if="{{activeTab===1}}"><view class="active-line"></view></block></view><view data-event-opts="{{[['tap',[['switchTab',[2]]]]]}}" class="{{['tab-item',(activeTab===2)?'active':'']}}" bindtap="__e"><text class="tab-text">已出行<block wx:if="{{completedCount>0}}"><text class="tab-count">{{"("+completedCount+")"}}</text></block></text><block wx:if="{{activeTab===2}}"><view class="active-line"></view></block></view></view><view class="order-list"><block wx:if="{{loading}}"><view class="loading-tip"><text>{{loadingText}}</text></view></block><block wx:else><block wx:if="{{$root.g0===0}}"><view class="empty-tip"><text>暂无订单记录</text></view></block></block><block wx:for="{{$root.l0}}" wx:for-item="order" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['goToOrderDetail',['$0'],[[['filteredOrders','id',order.$orig.id,'id']]]]]]]}}" class="order-item" bindtap="__e"><view class="order-header"><view class="order-type"><uni-icons class="ticket-icon" vue-id="{{'3267e520-1-'+index}}" type="{{order.m0}}" size="22" color="{{order.m1}}" bind:__l="__l"></uni-icons><text class="ticket-type">{{order.$orig.type}}</text></view><text class="{{['order-status',order.m2]}}">{{order.$orig.status}}</text></view><view class="order-route"><text class="route-text">{{order.$orig.route}}</text><text class="route-price">{{"¥"+order.g1}}</text></view><view class="order-time"><text class="time-text">{{order.$orig.departureTime+" 出发"}}</text></view><view class="order-divider"></view><view class="order-payment-actions"><view class="payment-part"><text class="payment-text">已付:<text style="color:#EE0A24;">{{"¥"+order.g2}}</text></text></view><view class="actions-part"><block wx:if="{{order.$orig.canRefund}}"><button data-event-opts="{{[['tap',[['applyRefund',['$0'],[[['filteredOrders','id',order.$orig.id,'id']]]]]]]}}" class="action-btn refund-btn" catchtap="__e">申请售后</button></block><button data-event-opts="{{[['tap',[['showGroupQrCode',['$event']]]]]}}" class="action-btn qr-btn" catchtap="__e">群二维码</button></view></view></view></block></view><block wx:if="{{showQrPopup}}"><view data-event-opts="{{[['tap',[['hideGroupQrCode',['$event']]]]]}}" class="qr-popup" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="qr-popup-content" catchtap="__e"><view class="qr-popup-header"><text class="qr-popup-title">购票成功</text><view data-event-opts="{{[['tap',[['hideGroupQrCode',['$event']]]]]}}" class="qr-popup-close" bindtap="__e"><text class="close-icon">×</text></view></view><view class="qr-popup-body"><block wx:if="{{groupQrCodeUrl}}"><image class="qr-code-image" src="{{groupQrCodeUrl}}" mode="aspectFit"></image></block><block wx:else><view class="qr-loading"><text>加载中...</text></view></block><text class="qr-popup-desc">扫码进群，专业售后服务和时效通知</text></view><view class="qr-popup-footer"><button data-event-opts="{{[['tap',[['saveQrCode',['$event']]]]]}}" class="qr-save-btn" bindtap="__e">保存二维码</button></view></view></view></block></view>