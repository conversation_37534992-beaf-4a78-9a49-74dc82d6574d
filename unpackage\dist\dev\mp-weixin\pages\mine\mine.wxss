@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  min-height: 100vh;
  background-color: #F6F7F9;
}
.mine-container {
  min-height: 100vh;
  position: relative;
}
/* 背景图片样式 */
.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
  display: flex;
  flex-direction: column;
}
.navbar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  /* 默认高度，会被JS动态修改 */
}
.page-title {
  color: #000000;
  font-size: 36rpx;
  font-weight: bold;
}
/* 内容区域 */
.content-area {
  width: 100%;
  box-sizing: border-box;
}
/* 用户信息区域样式 */
.user-info-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  border-radius: 12rpx;
  position: relative;
  z-index: 1;
  margin: 20rpx;
}
.user-info-left {
  display: flex;
  align-items: center;
}
.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.user-details {
  display: flex;
  flex-direction: column;
}
.username {
  font-size: 36rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.user-phone {
  font-size: 28rpx;
  color: #999999;
}
.user-info-right {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}
.coupon-count {
  font-size: 48rpx;
  color: #333333;
  font-weight: bold;
  line-height: 1;
}
.coupon-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 8rpx;
}
/* 常用工具区域样式 */
.tools-section {
  margin: 20rpx;
  background-color: #FFFFFF;
  padding: 30rpx;
  border-radius: 12rpx;
  position: relative;
  z-index: 1;
}
.section-title {
  font-size: 33rpx;
  color: #333333;
  font-weight: bold;
}
.tools-grid {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}
.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.33%;
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: inherit;
  line-height: inherit;
  box-shadow: none !important;
}
.tool-item::after {
  border: none !important;
}
/* 专门针对button组件的样式 */
button.tool-item {
  background: transparent !important;
  border: 0 !important;
  border-radius: 0 !important;
  outline: 0 !important;
  -webkit-appearance: none !important;
  appearance: none !important;
}
button.tool-item::after {
  border: none !important;
  content: none !important;
}
.tool-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #F6F7F9;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
}
.tool-icon {
  width: 60rpx;
  height: 60rpx;
}
.tool-name {
  font-size: 28rpx;
  color: #333333;
}
