(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["subpkg-user/passenger_edit/passenger_edit"],{112:function(e,t,n){"use strict";(function(e,t){var r=n(4);n(26);r(n(25));var i=r(n(113));e.__webpack_require_UNI_MP_PLUGIN__=n,t(i.default)}).call(this,n(1)["default"],n(2)["createPage"])},113:function(e,t,n){"use strict";n.r(t);var r=n(114),i=n(116);for(var s in i)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(s);n(118);var a,o=n(33),c=Object(o["default"])(i["default"],r["render"],r["staticRenderFns"],!1,null,"37c11de4",null,!1,r["components"],a);c.options.__file="subpkg-user/passenger_edit/passenger_edit.vue",t["default"]=c.exports},114:function(e,t,n){"use strict";n.r(t);var r=n(115);n.d(t,"render",(function(){return r["render"]})),n.d(t,"staticRenderFns",(function(){return r["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return r["recyclableRender"]})),n.d(t,"components",(function(){return r["components"]}))},115:function(e,t,n){"use strict";var r;n.r(t),n.d(t,"render",(function(){return i})),n.d(t,"staticRenderFns",(function(){return a})),n.d(t,"recyclableRender",(function(){return s})),n.d(t,"components",(function(){return r}));var i=function(){var e=this,t=e.$createElement;e._self._c},s=!1,a=[];i._withStripped=!0},116:function(e,t,n){"use strict";n.r(t);var r=n(117),i=n.n(r);for(var s in r)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(s);t["default"]=i.a},117:function(e,t,n){"use strict";(function(e){var r=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n(41)),s={data:function(){return{index:-1,id:"",name:"",idCard:"",isEdit:!1}},onLoad:function(){var e=this,t=this.getOpenerEventChannel();t.on("acceptPassengerData",(function(t){console.log("接收到乘客数据:",t),t&&t.passenger&&(e.index=t.index,e.name=t.passenger.name,t.passenger.id&&(e.id=t.passenger.id),t.passenger.idNumber?e.idCard=t.passenger.idNumber:t.passenger.rawIdNumber?e.idCard=t.passenger.rawIdNumber:t.passenger.idCard&&(e.idCard=t.passenger.idCard.replace(/\*/g,"")),e.isEdit=!0)})),this.setNavigationTitle()},onShow:function(){this.setNavigationTitle()},methods:{setNavigationTitle:function(){e.setNavigationBarTitle({title:this.isEdit?"编辑乘客":"添加乘客"})},savePassenger:function(){if(this.name.trim())if(this.idCard.trim()){var t=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;t.test(this.idCard)?this.saveToServer():e.showToast({title:"请输入正确的身份证号",icon:"none"})}else e.showToast({title:"请输入证件号",icon:"none"});else e.showToast({title:"请输入姓名",icon:"none"})},saveToServer:function(){var t=this;e.showLoading({title:"保存中..."});var n={name:this.name,idNumber:this.idCard};this.id&&(n.id=this.id);var r="/app/information",s=this.id?"put":"post",a=this.id?"编辑":"添加";console.log("准备".concat(a,"乘客信息:"),{url:r,method:s,data:n}),i.default[s](r,n).then((function(n){e.hideLoading(),console.log("".concat(a,"乘客成功，响应数据:"),n);var r=!0;if(void 0!==n.code&&0!==n.code&&200!==n.code&&(r=!1),r){e.showToast({title:"".concat(a,"成功"),icon:"success"});var i={id:t.id||n.data&&n.data.id,name:t.name,idCard:t.formatIdCard(t.idCard),rawIdNumber:t.idCard,selected:!0},s=getCurrentPages(),o=s[s.length-2];t.isEdit?o.$vm.updatePassenger(t.index,i):o.$vm.addNewPassenger(i),setTimeout((function(){e.navigateBack()}),500)}else{var c="".concat(a,"失败，请重试");n.msg?c=n.msg:n.message&&(c=n.message),e.showModal({title:"".concat(a,"失败"),content:c,confirmText:"重试",cancelText:"取消",success:function(e){e.confirm&&t.saveToServer()}})}})).catch((function(n){e.hideLoading(),console.error("".concat(a,"乘客失败，详细错误:"),n);var r="网络异常，请重试";n&&n.msg?r=n.msg:n&&n.errMsg&&(r=n.errMsg),e.showModal({title:"".concat(a,"失败"),content:r,confirmText:"重试",cancelText:"取消",success:function(e){e.confirm&&t.saveToServer()}})}))},deletePassenger:function(){var t=this;e.showModal({title:"提示",content:"确定要删除此乘客信息吗？",success:function(e){e.confirm&&(t.id?t.deleteFromServer():t.deleteFromLocal())}})},deleteFromServer:function(){var t=this;e.showLoading({title:"删除中..."}),i.default.delete("/app/information/".concat(this.id)).then((function(n){e.hideLoading(),console.log("删除乘客成功，响应数据:",n);var r=!0;if(void 0!==n.code&&0!==n.code&&200!==n.code&&(r=!1),r)e.showToast({title:"删除成功",icon:"success"}),t.deleteFromLocal();else{var i="删除失败，请重试";n.msg?i=n.msg:n.message&&(i=n.message),e.showModal({title:"删除失败",content:i,confirmText:"重试",cancelText:"取消",success:function(e){e.confirm&&t.deleteFromServer()}})}})).catch((function(n){e.hideLoading(),console.error("删除乘客失败，详细错误:",n);var r="网络异常，请重试";n&&n.msg?r=n.msg:n&&n.errMsg&&(r=n.errMsg),e.showModal({title:"删除失败",content:r,confirmText:"重试",cancelText:"取消",success:function(e){e.confirm&&t.deleteFromServer()}})}))},deleteFromLocal:function(){var t=getCurrentPages(),n=t[t.length-2];n.$vm.removePassenger(this.index),e.navigateBack()},formatIdCard:function(e){return e.length>=18?e.substr(0,4)+"**********"+e.substr(14):e.length>=15?e.substr(0,4)+"*******"+e.substr(11):e}}};t.default=s}).call(this,n(2)["default"])},118:function(e,t,n){"use strict";n.r(t);var r=n(119),i=n.n(r);for(var s in r)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(s);t["default"]=i.a},119:function(e,t,n){}},[[112,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/subpkg-user/passenger_edit/passenger_edit.js.map