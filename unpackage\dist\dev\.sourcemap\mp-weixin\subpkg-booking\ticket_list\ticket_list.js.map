{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/subpkg-booking/ticket_list/ticket_list.vue?be32", "webpack:///E:/购票系统/购票系统/subpkg-booking/ticket_list/ticket_list.vue?30bb", "webpack:///E:/购票系统/购票系统/subpkg-booking/ticket_list/ticket_list.vue?ae8f", "webpack:///E:/购票系统/购票系统/subpkg-booking/ticket_list/ticket_list.vue?cbc6", "uni-app:///subpkg-booking/ticket_list/ticket_list.vue", "webpack:///E:/购票系统/购票系统/subpkg-booking/ticket_list/ticket_list.vue?aadb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniCalendar", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "loading", "ticketList", "length", "l0", "__map", "ticket", "index", "$orig", "__get_orig", "g1", "originalPrice", "toFixed", "g2", "status", "includes", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "departureStation", "arrivalStation", "currentDateIndex", "dateList", "upAddressId", "downAddressId", "loadingText", "showCalendar", "currentYear", "currentMonth", "currentDay", "selectedInfo", "startDate", "endDate", "selectedDate", "weekDayNames", "today", "scrollLeft", "screenWidth", "dateItemWidth", "onLoad", "uni", "title", "onReady", "success", "methods", "goBack", "generateDateList", "date", "day", "weekDay", "isToday", "selectDate", "setSelectedDate", "generateDateListFromDate", "scrollToSelectedDate", "itemWidthPx", "visibleItems", "loadTicketList", "departureTime", "params", "carTime", "upAddress", "downAddress", "ticketApi", "response", "icon", "processTicketData", "time", "type", "typeName", "departureDoor", "duration", "currentPrice", "discount", "discountCount", "isImmediateDeparture", "originalData", "getTicketType", "getTicketStatus", "formatDate", "extractTimeFromDateTime", "timeStr", "showDatePicker", "cancelDatePicker", "calendarChange", "monthSwitch", "confirmDatePicker", "year", "month", "isDateToday", "goToTicketConfirm", "ticketId", "departure", "arrival", "dateDesc", "url"], "mappings": "gKAAA,MAGA,aACA,WAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,iIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,6CACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,YAAa,WACX,OAAO,kIAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACTN,EAAIO,QAAwD,MAA7CP,EAAIO,SAAqC,IAA1BP,EAAIQ,WAAWC,QACnDC,EACDV,EAAIO,SAAYH,EAWb,KAVAJ,EAAIW,MAAMX,EAAIQ,YAAY,SAAUI,EAAQC,GAC1C,IAAIC,EAAQd,EAAIe,WAAWH,GACvBI,EAAKJ,EAAOK,cAAcC,QAAQ,GAClCC,EAAKP,EAAOQ,OAAOC,SAAS,KAChC,MAAO,CACLP,MAAOA,EACPE,GAAIA,EACJG,GAAIA,MAIdnB,EAAIsB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLtB,GAAIA,EACJM,GAAIA,MAKRiB,GAAmB,EACnBC,EAAkB,GACtB7B,EAAO8B,eAAgB,G,gCCxDvB,wHAA4qB,eAAG,G,oJC2H/qB,Q,EAEA,CACAN,gBACA,eACA,kBACA,iBACA,cACA,2CAEA,OACAO,wBACAC,uBACAC,mBACAC,YAGAC,cACAC,gBAGA3B,cAGAD,WACA6B,qBAGAC,gBACAC,4BACAC,4BACAC,uBACAC,kBACAC,YACAC,mDACAC,eAEAC,2CACAC,QACAC,aACAC,gBACAC,oBAGAC,mBAEA,cACA,uDAEA,YACA,mDAIA,gBACA,0CAEA,kBACA,8CAIAC,yBACAC,wDAIA,wBAGA,gBACA,qCAIA,uBAEAC,mBAAA,WAEAF,iBACAG,oBACA,4BAEA,sBACA,wBACA,gCAMAC,SAEAC,kBACAL,kBAIAM,4BAIA,IAHA,eACA,KAEA,cACA,eACAC,yBAEA,iBACA,uBAEAzB,QACAyB,OACAC,gBACAC,UACAC,gBAIA,iBAIAC,uBACA,wBAEA,uBAIAC,4BAAA,WACA,IAEA,mBACA,iBACA,qBACA,iBACA,iBACA,oBAGA,WACA,qDACA,qDACA,MACA,qBAGA,WACA,wBACA,OAEA,iCACA,yBAIA,2BACA,6BAGA,SACAlE,6BAEA,0BAKAmE,qCAGA,IAFA,SAEA,cACA,kBACAN,yBAEA,iBACA,uBAGA,WACA,6BACAA,6BACAA,kCAEAzB,QACAyB,OACAC,gBACAC,UACAC,YAIA,iBAIAI,gCAGA,2BACA,uBAGA,iCAGA,4BAEA,kCACA,8BACA,+CAEA,kBAGApE,qBACAmC,uCACAgB,6BACAkB,cACAC,eACApB,8BAKAqB,0BAAA,gJAkBA,OAlBA,SAEA,aACA,0BAGAxB,iCACAyB,uBAGAC,GACAC,UACAC,wBACAC,6BAGA5E,yBAEA,SACA6E,6BAAA,OAAAC,SAEA,UAEA,0CAEA,gBACAxB,aACAC,eACAwB,eAEA,qDAGA/E,gCACA,gBACAsD,aACAC,iBACAwB,cACA,QAEA,OAFA,UAEA,yFAxCA,IA6CAC,8BAAA,WACA,wBAIA,mBACA,OAEAC,gDACAC,oCACAC,mBACAC,8BACAC,sBACAjE,0CACAkE,iDACAC,yBACAC,iCACAjE,yCACAkE,gDAEAC,mBAlBA,IAwBAC,0BAEA,UACA,OACA,UACA,iBACA,OACA,UACA,QACA,iBAKAC,4BAEA,qBAEA,aACA,KACA,IACA,iBAEA,MAKAC,uBACA,sBACA,yCACA,sCACA,gDAIAC,oCACA,MACA,cAGA,IAEA,SAEA,mBAEAC,sBACA,qBAKA,SAHAA,kBAOA,sBACA,iBAGA,QACA,SAEA,OADA/F,+BACA,UAKAgG,0BACA,sBAIAC,4BACA,sBAIAC,2BACA,0BAEA,oBACA,2EAGA,wBACA,4BAKAC,wBACA,kBACA,wBACA,4BAKAC,6BACA,sBAEA,wBAAAC,SAAAC,UAAAzC,SACA,oBAGA,WACA,MACA,qBAEA,cAEA,4BACA,CAGA,IADA,SACA,cACA,kBACAA,2BAEA,iBACA,uBAEA,6BACAA,6BACAA,kCAEAzB,QACAyB,OACAC,gBACAC,UACAC,YAIA,gBACA,wBAGA,qBAGA,wBAIAuC,wBACA,eACA,kCACA1C,6BACAA,mCAIA2C,8BAAA,MAEA,oBASA,gEAEA,MASA,2CACA,sBACA,mBAEA,GADA,UACA,UACA,wCACA,wCAGA,eACA,eACA,iCAGA,GACAC,WACAC,gCACAC,4BACA9C,OACA+C,WAEAxB,8BACAH,YACAI,oBACAF,oBACAD,YACA9D,8BACAkE,4BACAC,qBAIA,uGACA,6DAEAjC,cACAuD,aA5CAvD,aACAC,qBACAwB,mBAbAzB,aACAC,gBACAwB,iBAyDA,c,4DC3nBA,wHAA2xC,eAAG,G", "file": "subpkg-booking/ticket_list/ticket_list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg-booking/ticket_list/ticket_list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./ticket_list.vue?vue&type=template&id=d11afc2c&scoped=true&\"\nvar renderjs\nimport script from \"./ticket_list.vue?vue&type=script&lang=js&\"\nexport * from \"./ticket_list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ticket_list.vue?vue&type=style&index=0&id=d11afc2c&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d11afc2c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg-booking/ticket_list/ticket_list.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ticket_list.vue?vue&type=template&id=d11afc2c&scoped=true&\"", "var components\ntry {\n  components = {\n    uniCalendar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-calendar/components/uni-calendar/uni-calendar\" */ \"@/uni_modules/uni-calendar/components/uni-calendar/uni-calendar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading ? !_vm.loading && _vm.ticketList.length === 0 : null\n  var l0 =\n    !_vm.loading && !g0\n      ? _vm.__map(_vm.ticketList, function (ticket, index) {\n          var $orig = _vm.__get_orig(ticket)\n          var g1 = ticket.originalPrice.toFixed(2)\n          var g2 = ticket.status.includes(\"张\")\n          return {\n            $orig: $orig,\n            g1: g1,\n            g2: g2,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ticket_list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ticket_list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<!-- 日期选择器 -->\n\t\t<scroll-view class=\"date-scroll\" scroll-x=\"true\" show-scrollbar=\"false\" :scroll-left=\"scrollLeft\">\n\t\t\t<view class=\"date-list\">\n\t\t\t\t<view v-for=\"(item, index) in dateList\" :key=\"index\"\n\t\t\t\t\t:class=\"['date-item', { 'date-item-active': currentDateIndex === index }]\"\n\t\t\t\t\t@click=\"selectDate(index)\">\n\t\t\t\t\t<text class=\"week-day\">{{ item.weekDay }}</text>\n\t\t\t\t\t<view :class=\"['day-num', { 'today-circle': item.isToday }]\">\n\t\t\t\t\t\t<text v-if=\"item.isToday\" style=\"font-size: 24rpx;\">今天</text>\n\t\t\t\t\t\t<text v-else style=\"font-size: 28rpx;\">{{ item.day }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t\t<!-- 日历图标 -->\n\t\t<view class=\"calendar-icon\" @click=\"showDatePicker\">\n\t\t\t<image src=\"/static/icons/calendar.png\" class=\"calendar-img\"></image>\n\t\t\t<!-- // <image src=\"/static/icons/back.png\" class=\"arrow-img\"></image> -->\n\t\t</view>\n\n\n\t\t<!-- 车票列表 -->\n\t\t<view class=\"ticket-list\">\n\t\t\t<!-- 加载状态 -->\n\t\t\t<view v-if=\"loading\" class=\"loading-container\">\n\t\t\t\t<view class=\"loading-text\">{{ loadingText }}</view>\n\t\t\t</view>\n\n\t\t\t<!-- 无数据状态 -->\n\t\t\t<view v-else-if=\"!loading && ticketList.length === 0\" class=\"empty-container\">\n\t\t\t\t<view class=\"empty-text\">暂无车票信息</view>\n\t\t\t\t<view class=\"empty-tip\">请尝试选择其他日期</view>\n\t\t\t</view>\n\n\t\t\t<!-- 车票列表 -->\n\t\t\t<view v-else v-for=\"(ticket, index) in ticketList\" :key=\"index\"\n\t\t\t\t:class=\"['ticket-item', ticket.type, { 'ticket-disabled': ticket.status === '无票' }]\"\n\t\t\t\t@click=\"goToTicketConfirm(ticket)\">\n\t\t\t\t<!-- 即将发车标签 - 左上角 -->\n\t\t\t\t<text v-if=\"ticket.isImmediateDeparture\" class=\"departure-tag\">即将发车</text>\n\n\t\t\t\t<view class=\"ticket-content\">\n\t\t\t\t\t<view class=\"ticket-left\">\n\t\t\t\t\t\t<!-- 时间和时长 -->\n\t\t\t\t\t\t<view class=\"time-section\">\n\t\t\t\t\t\t\t<text class=\"ticket-time\">{{ ticket.time }}</text>\n\t\t\t\t\t\t\t<text class=\"ticket-duration\">约{{ ticket.duration }}小时 <text class=\"info-icon\">ⓘ</text></text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 车票类型 -->\n\t\t\t\t\t\t<view class=\"ticket-type-tag\">\n\t\t\t\t\t\t\t<text class=\"type-text\">{{ ticket.typeName }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 站点信息 - 中间 -->\n\t\t\t\t\t<view class=\"ticket-stations\">\n\t\t\t\t\t\t<view class=\"station-line\">\n\t\t\t\t\t\t\t<view class=\"station-dot start\"></view>\n\t\t\t\t\t\t\t<view class=\"station-connector\"></view>\n\t\t\t\t\t\t\t<view class=\"station-dot end\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"station-names\">\n\t\t\t\t\t\t\t<text class=\"station-name\">{{ departureStation }}</text>\n\t\t\t\t\t\t\t<text class=\"station-name\">{{ arrivalStation }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 价格信息 - 右侧 -->\n\t\t\t\t\t<view class=\"ticket-right\">\n\t\t\t\t\t\t<view class=\"price-box\">\n\t\t\t\t\t\t\t<view class=\"price-original\">\n\t\t\t\t\t\t\t\t<text class=\"price-through\">¥{{ ticket.originalPrice.toFixed(2) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"price-current\">\n\t\t\t\t\t\t\t\t<text class=\"price-symbol\">¥</text>\n\t\t\t\t\t\t\t\t<text class=\"price-value\">{{ ticket.currentPrice }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"discount-tag\">\n\t\t\t\t\t\t\t<text>优惠¥{{ ticket.discount }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"ticket-status\" v-if=\"ticket.status.includes('张')\" style=\"color: red;\">{{ ticket.status }}</text>\n\t\t\t\t\t\t<text class=\"ticket-status\" v-else>{{ ticket.status }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 日期选择器弹窗 -->\n\t\t<view class=\"calendar-popup\" v-if=\"showCalendar\">\n\t\t\t<view class=\"calendar-mask\" @click=\"cancelDatePicker\"></view>\n\t\t\t<view class=\"calendar-container\">\n\t\t\t\t<view class=\"calendar-header custom-calendar-header\">\n\t\t\t\t\t<text class=\"calendar-close\" @click=\"cancelDatePicker\">×</text>\n\t\t\t\t\t<view class=\"calendar-nav\">\n\t\t\t\t\t\t<text class=\"calendar-title\">{{ currentYear }}年{{ currentMonth }}月</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 使用uni-calendar组件 -->\n\t\t\t\t<uni-calendar\n\t\t\t\t\tclass=\"custom-calendar\"\n\t\t\t\t\tref=\"calendar\"\n\t\t\t\t\t:insert=\"true\"\n\t\t\t\t\t:lunar=\"false\"\n\t\t\t\t\t:start-date=\"startDate\"\n\t\t\t\t\t:end-date=\"endDate\"\n\t\t\t\t\t@change=\"calendarChange\"\n\t\t\t\t\t@monthSwitch=\"monthSwitch\"\n\t\t\t\t/>\n\t\t\t\t\n\t\t\t\t<view class=\"calendar-footer\">\n\t\t\t\t\t<view class=\"calendar-confirm\" @click=\"confirmDatePicker\">确认</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { ticketApi } from '@/utils/api.js';\n\nexport default {\n\tdata() {\n\t\tconst now = new Date();\n\t\tconst year = now.getFullYear();\n\t\tconst month = now.getMonth() + 1;\n\t\tconst day = now.getDate();\n\t\tconst nowDateStr = `${year}-${month}-${day}`;\n\t\t\n\t\treturn {\n\t\t\tdepartureStation: '同济大学',\n\t\t\tarrivalStation: '泰安各县城',\n\t\t\tcurrentDateIndex: 0,\n\t\t\tdateList: [], // 将在onLoad中填充\n\n\t\t\t// 地点ID（需要根据实际业务配置）\n\t\t\tupAddressId: 1, // 上车地点ID，默认为同济大学\n\t\t\tdownAddressId: 2, // 下车地点ID，默认为泰安各县城\n\n\t\t\t// 车票数据列表\n\t\t\tticketList: [],\n\n\t\t\t// 加载状态\n\t\t\tloading: false,\n\t\t\tloadingText: '加载中...',\n\n\t\t\t// 日历相关数据\n\t\t\tshowCalendar: false,\n\t\t\tcurrentYear: now.getFullYear(),\n\t\t\tcurrentMonth: now.getMonth() + 1,\n\t\t\tcurrentDay: now.getDate(),\n\t\t\tselectedInfo: null,\n\t\t\tstartDate: nowDateStr,\n\t\t\tendDate: `${year + 1}-${month}-${day}`,\n\t\t\tselectedDate: nowDateStr,\n\t\t\t\n\t\t\tweekDayNames: ['日', '一', '二', '三', '四', '五', '六'],\n\t\t\ttoday: now,\n\t\t\tscrollLeft: 0, // 日期滚动条的滚动位置\n\t\t\tscreenWidth: 375, // 屏幕宽度，将在onReady中获取实际值\n\t\t\tdateItemWidth: 120 // 日期项宽度（rpx）\n\t\t};\n\t},\n\tonLoad(options) {\n\t\t// 从路由参数获取出发地和目的地\n\t\tif (options.departure) {\n\t\t\tthis.departureStation = decodeURIComponent(options.departure);\n\t\t}\n\t\tif (options.arrival) {\n\t\t\tthis.arrivalStation = decodeURIComponent(options.arrival);\n\t\t}\n\n\t\t// 从路由参数获取地点ID（如果有的话）\n\t\tif (options.upAddressId) {\n\t\t\tthis.upAddressId = parseInt(options.upAddressId);\n\t\t}\n\t\tif (options.downAddressId) {\n\t\t\tthis.downAddressId = parseInt(options.downAddressId);\n\t\t}\n\n\t\t// 动态设置导航栏标题\n\t\tuni.setNavigationBarTitle({\n\t\t\ttitle: this.departureStation + ' — ' + this.arrivalStation\n\t\t});\n\n\t\t// 生成未来30天的日期\n\t\tthis.generateDateList();\n\n\t\t// 处理传递过来的日期参数\n\t\tif (options.selectedDate) {\n\t\t\tthis.setSelectedDate(options.selectedDate);\n\t\t}\n\n\t\t// 加载车票数据\n\t\tthis.loadTicketList();\n\t},\n\tonReady() {\n\t\t// 获取系统信息，用于计算滚动位置\n\t\tuni.getSystemInfo({\n\t\t\tsuccess: (res) => {\n\t\t\t\tthis.screenWidth = res.screenWidth;\n\t\t\t\t// 如果已经设置了选中日期，重新计算滚动位置\n\t\t\t\tif (this.currentDateIndex > 0) {\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.scrollToSelectedDate();\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t},\n\tmethods: {\n\t\t// 返回上一页\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\n\t\t// 生成日期列表（未来30天）\n\t\tgenerateDateList() {\n\t\t\tconst today = new Date();\n\t\t\tconst dateList = [];\n\n\t\t\tfor (let i = 0; i < 30; i++) {\n\t\t\t\tconst date = new Date();\n\t\t\t\tdate.setDate(today.getDate() + i);\n\n\t\t\t\tconst weekIndex = date.getDay();\n\t\t\t\tconst weekDay = this.weekDayNames[weekIndex];\n\n\t\t\t\tdateList.push({\n\t\t\t\t\tdate: date,\n\t\t\t\t\tday: date.getDate(),\n\t\t\t\t\tweekDay: weekDay,\n\t\t\t\t\tisToday: i === 0\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tthis.dateList = dateList;\n\t\t},\n\n\t\t// 选择日期\n\t\tselectDate(index) {\n\t\t\tthis.currentDateIndex = index;\n\t\t\t// 重新加载该日期的车票数据\n\t\t\tthis.loadTicketList();\n\t\t},\n\n\t\t// 设置选中的日期（从首页传递过来的日期）\n\t\tsetSelectedDate(selectedDateStr) {\n\t\t\ttry {\n\t\t\t\t// 解析传递过来的日期字符串，格式如 \"2024-1-15\"\n\t\t\t\tconst dateParts = selectedDateStr.split('-');\n\t\t\t\tif (dateParts.length === 3) {\n\t\t\t\t\tconst year = parseInt(dateParts[0]);\n\t\t\t\t\tconst month = parseInt(dateParts[1]);\n\t\t\t\t\tconst day = parseInt(dateParts[2]);\n\t\t\t\t\tconst selectedDate = new Date(year, month - 1, day);\n\n\t\t\t\t\t// 计算选中日期与今天的差值\n\t\t\t\t\tconst today = new Date();\n\t\t\t\t\tconst todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());\n\t\t\t\t\tconst selectedStart = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate());\n\t\t\t\t\tconst diffTime = selectedStart - todayStart;\n\t\t\t\t\tconst diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n\t\t\t\t\t// 如果选中的日期在30天范围内，设置对应的索引\n\t\t\t\t\tif (diffDays >= 0 && diffDays < 30) {\n\t\t\t\t\t\tthis.currentDateIndex = diffDays;\n\t\t\t\t\t} else if (diffDays >= 0) {\n\t\t\t\t\t\t// 如果超出30天范围，重新生成以选中日期为起点的日期列表\n\t\t\t\t\t\tthis.generateDateListFromDate(selectedDate);\n\t\t\t\t\t\tthis.currentDateIndex = 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 滚动到选中的日期位置\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.scrollToSelectedDate();\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('解析选中日期失败:', error);\n\t\t\t\t// 如果解析失败，默认选择今天\n\t\t\t\tthis.currentDateIndex = 0;\n\t\t\t}\n\t\t},\n\n\t\t// 从指定日期开始生成日期列表\n\t\tgenerateDateListFromDate(startDate) {\n\t\t\tconst dateList = [];\n\n\t\t\tfor (let i = 0; i < 30; i++) {\n\t\t\t\tconst date = new Date(startDate);\n\t\t\t\tdate.setDate(startDate.getDate() + i);\n\n\t\t\t\tconst weekIndex = date.getDay();\n\t\t\t\tconst weekDay = this.weekDayNames[weekIndex];\n\n\t\t\t\t// 判断是否是今天\n\t\t\t\tconst today = new Date();\n\t\t\t\tconst isToday = date.getDate() === today.getDate() &&\n\t\t\t\t\tdate.getMonth() === today.getMonth() &&\n\t\t\t\t\tdate.getFullYear() === today.getFullYear();\n\n\t\t\t\tdateList.push({\n\t\t\t\t\tdate: date,\n\t\t\t\t\tday: date.getDate(),\n\t\t\t\t\tweekDay: weekDay,\n\t\t\t\t\tisToday: isToday\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tthis.dateList = dateList;\n\t\t},\n\n\t\t// 滚动到选中的日期位置\n\t\tscrollToSelectedDate() {\n\t\t\t// 基于实际屏幕宽度计算滚动位置\n\t\t\t// rpx转px的比例：750rpx = screenWidth px\n\t\t\tconst rpxToPx = this.screenWidth / 750;\n\t\t\tconst itemWidthPx = this.dateItemWidth * rpxToPx; // 日期项的实际像素宽度\n\n\t\t\t// 计算可视区域能显示多少个完整的日期项\n\t\t\tconst visibleItems = Math.floor(this.screenWidth / itemWidthPx);\n\n\t\t\t// 滚动策略：让选中的日期项显示在可视区域的合适位置\n\t\t\tif (this.currentDateIndex > 2) {\n\t\t\t\t// 计算目标位置：让选中项显示在可视区域的左侧1/4位置\n\t\t\t\tconst targetVisiblePosition = Math.min(2, Math.floor(visibleItems / 4));\n\t\t\t\tconst scrollDistance = (this.currentDateIndex - targetVisiblePosition) * itemWidthPx;\n\t\t\t\tthis.scrollLeft = Math.max(0, Math.round(scrollDistance));\n\t\t\t} else {\n\t\t\t\tthis.scrollLeft = 0;\n\t\t\t}\n\n\t\t\tconsole.log('滚动计算:', {\n\t\t\t\tcurrentDateIndex: this.currentDateIndex,\n\t\t\t\tscreenWidth: this.screenWidth,\n\t\t\t\titemWidthPx: itemWidthPx,\n\t\t\t\tvisibleItems: visibleItems,\n\t\t\t\tscrollLeft: this.scrollLeft\n\t\t\t});\n\t\t},\n\n\t\t// 加载车票列表数据\n\t\tasync loadTicketList() {\n\t\t\ttry {\n\t\t\t\tthis.loading = true;\n\t\t\t\tthis.loadingText = '正在查询车票...';\n\n\t\t\t\t// 获取当前选择的日期\n\t\t\t\tconst selectedDate = this.dateList[this.currentDateIndex];\n\t\t\t\tconst departureTime = this.formatDate(selectedDate.date);\n\n\t\t\t\t// 构建请求参数\n\t\t\t\tconst params = {\n\t\t\t\t\tcarTime: departureTime,\n\t\t\t\t\tupAddress: this.upAddressId,\n\t\t\t\t\tdownAddress: this.downAddressId\n\t\t\t\t};\n\n\t\t\t\tconsole.log('查询车票参数:', params);\n\n\t\t\t\t// 调用接口获取数据\n\t\t\t\tconst response = await ticketApi.getTicketList(params);\n\n\t\t\t\tif (response && response.data) {\n\t\t\t\t\t// 处理接口返回的数据\n\t\t\t\t\tthis.ticketList = this.processTicketData(response.data);\n\t\t\t\t} else {\n\t\t\t\t\tthis.ticketList = [];\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '暂无车票信息',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取车票列表失败:', error);\n\t\t\t\tthis.ticketList = [];\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取车票信息失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\n\t\t// 处理接口返回的车票数据\n\t\tprocessTicketData(data) {\n\t\t\tif (!Array.isArray(data)) {\n\t\t\t\treturn [];\n\t\t\t}\n\n\t\t\treturn data.map(item => {\n\t\t\t\treturn {\n\t\t\t\t\t// 根据后端接口字段映射到前端需要的字段\n\t\t\t\t\ttime: this.extractTimeFromDateTime(item.departureTime), // 发车时间，提取时分\n\t\t\t\t\ttype: this.getTicketType(item.vehicleType), // 车辆类型\n\t\t\t\t\ttypeName: item.carType, // 车辆类型名称\n\t\t\t\t\tdepartureDoor: item.departureDoor , // 上车门\n\t\t\t\t\tduration: item.travelTime , // 行程时长\n\t\t\t\t\toriginalPrice: parseFloat(item.originalPrice ), // 原价\n\t\t\t\t\tcurrentPrice: parseFloat(item.currentPrice || item.price ), // 现价\n\t\t\t\t\tdiscount: item.discountPrice, // 优惠金额\n\t\t\t\t\tdiscountCount: item.discountCount || 0, // 优惠数量\n\t\t\t\t\tstatus: this.getTicketStatus(item.ticketNumber), // 根据票数量显示状态\n\t\t\t\t\tisImmediateDeparture: item.isImmediateDeparture || false, // 是否即将发车\n\t\t\t\t\t// 保留原始数据以备后用\n\t\t\t\t\toriginalData: item\n\t\t\t\t};\n\t\t\t});\n\t\t},\n\n\t\t// 根据车辆类型获取对应的样式类型\n\t\tgetTicketType(vehicleType) {\n\t\t\t// 根据实际业务逻辑映射车辆类型\n\t\t\tswitch (vehicleType) {\n\t\t\t\tcase 1:\n\t\t\t\tcase '商务车':\n\t\t\t\t\treturn 'business';\n\t\t\t\tcase 2:\n\t\t\t\tcase '普通车':\n\t\t\t\tdefault:\n\t\t\t\t\treturn 'normal';\n\t\t\t}\n\t\t},\n\n\t\t// 根据票数量获取票务状态\n\t\tgetTicketStatus(ticketNumber) {\n\t\t\t// 将ticketNumber转换为数字，如果无效则默认为0\n\t\t\tconst num = parseInt(ticketNumber) || 0;\n\n\t\t\tif (num >= 10) {\n\t\t\t\treturn '有票';\n\t\t\t} else if (num > 0) {\n\t\t\t\treturn `${num}张`;\n\t\t\t} else {\n\t\t\t\treturn '无票';\n\t\t\t}\n\t\t},\n\n\t\t// 格式化日期为 yyyy-MM-dd 格式\n\t\tformatDate(date) {\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\treturn `${year}-${month}-${day}`;\n\t\t},\n\n\t\t// 从完整的日期时间字符串中提取时分\n\t\textractTimeFromDateTime(dateTimeString) {\n\t\t\tif (!dateTimeString) {\n\t\t\t\treturn '08:00'; // 默认时间\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\t// 处理格式如 \"2025-06-10 00:00:00\" 或 \"2025-06-10T00:00:00\"\n\t\t\t\tlet timeStr = '';\n\n\t\t\t\tif (dateTimeString.includes(' ')) {\n\t\t\t\t\t// 格式: \"2025-06-10 00:00:00\"\n\t\t\t\t\ttimeStr = dateTimeString.split(' ')[1];\n\t\t\t\t} else if (dateTimeString.includes('T')) {\n\t\t\t\t\t// 格式: \"2025-06-10T00:00:00\"\n\t\t\t\t\ttimeStr = dateTimeString.split('T')[1];\n\t\t\t\t} else {\n\t\t\t\t\t// 如果已经是时间格式，直接返回\n\t\t\t\t\treturn dateTimeString;\n\t\t\t\t}\n\n\t\t\t\t// 提取时分部分 (HH:MM)\n\t\t\t\tif (timeStr && timeStr.length >= 5) {\n\t\t\t\t\treturn timeStr.substring(0, 5); // 取前5位 \"HH:MM\"\n\t\t\t\t}\n\n\t\t\t\treturn '08:00'; // 默认时间\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('解析发车时间失败:', dateTimeString, error);\n\t\t\t\treturn '08:00'; // 默认时间\n\t\t\t}\n\t\t},\n\n\t\t// 显示日期选择器\n\t\tshowDatePicker() {\n\t\t\tthis.showCalendar = true;\n\t\t},\n\n\t\t// 取消日期选择\n\t\tcancelDatePicker() {\n\t\t\tthis.showCalendar = false;\n\t\t},\n\t\t\n\t\t// 日历组件日期变化事件\n\t\tcalendarChange(e) {\n\t\t\tif (e.year && e.month && e.date) {\n\t\t\t\t// 保存选中的日期信息\n\t\t\t\tthis.selectedInfo = e;\n\t\t\t\tthis.selectedDate = `${e.year}-${e.month}-${e.date}`;\n\t\t\t\t\n\t\t\t\t// 更新显示的年月\n\t\t\t\tthis.currentYear = e.year;\n\t\t\t\tthis.currentMonth = e.month;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 日历组件月份切换事件\n\t\tmonthSwitch(e) {\n\t\t\tif (e.year && e.month) {\n\t\t\t\tthis.currentYear = e.year;\n\t\t\t\tthis.currentMonth = e.month;\n\t\t\t}\n\t\t},\n\n\t\t// 确认日期选择\n\t\tconfirmDatePicker() {\n\t\t\tif (!this.selectedInfo) return;\n\t\t\t\n\t\t\tconst { year, month, date } = this.selectedInfo;\n\t\t\tconst selectedDate = new Date(year, month - 1, date);\n\n\t\t\t// 检查所选日期是否在当前显示的30天内\n\t\t\tconst today = new Date();\n\t\t\tconst diffTime = selectedDate - today;\n\t\t\tconst diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n\t\t\tif (diffDays >= 0 && diffDays < 30) {\n\t\t\t\t// 如果在30天内，直接选中对应的日期\n\t\t\t\tthis.currentDateIndex = diffDays;\n\t\t\t} else {\n\t\t\t\t// 如果不在30天内，需要重新生成日期列表\n\t\t\t\tconst dateList = [];\n\t\t\t\tfor (let i = 0; i < 30; i++) {\n\t\t\t\t\tconst date = new Date(selectedDate);\n\t\t\t\t\tdate.setDate(selectedDate.getDate() - diffDays + i);\n\n\t\t\t\t\tconst weekIndex = date.getDay();\n\t\t\t\t\tconst weekDay = this.weekDayNames[weekIndex];\n\n\t\t\t\t\tconst isToday = date.getDate() === today.getDate() &&\n\t\t\t\t\t\tdate.getMonth() === today.getMonth() &&\n\t\t\t\t\t\tdate.getFullYear() === today.getFullYear();\n\n\t\t\t\t\tdateList.push({\n\t\t\t\t\t\tdate: date,\n\t\t\t\t\t\tday: date.getDate(),\n\t\t\t\t\t\tweekDay: weekDay,\n\t\t\t\t\t\tisToday: isToday\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tthis.dateList = dateList;\n\t\t\t\tthis.currentDateIndex = diffDays;\n\t\t\t}\n\n\t\t\tthis.showCalendar = false;\n\n\t\t\t// 重新加载车票数据\n\t\t\tthis.loadTicketList();\n\t\t},\n\t\t\n\t\t// 检查日期是否是今天\n\t\tisDateToday(date) {\n\t\t\tconst today = new Date();\n\t\t\treturn date.getDate() === today.getDate() && \n\t\t\t\t   date.getMonth() === today.getMonth() && \n\t\t\t\t   date.getFullYear() === today.getFullYear();\n\t\t},\n\n\t\t// 跳转到购票确认页面\n\t\tgoToTicketConfirm(ticket) {\n\t\t\t// 检查票务状态，如果无票则提示并阻止跳转\n\t\t\tif (ticket.status === '无票') {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '该班次暂无余票',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 获取车票ID，优先使用原始数据中的ID\n\t\t\tconst ticketId = ticket.originalData?.id || ticket.id;\n\n\t\t\tif (!ticketId) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '车票信息异常，请重新选择',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 获取当前选择的日期\n\t\t\tconst selectedDate = this.dateList[this.currentDateIndex];\n\t\t\tconst month = selectedDate.date.getMonth() + 1;\n\t\t\tconst day = selectedDate.date.getDate();\n\t\t\tconst weekDay = selectedDate.weekDay;\n\t\t\tconst today = new Date();\n\t\t\tconst isTomorrow = day === today.getDate() + 1 && month === today.getMonth() + 1;\n\t\t\tconst dateDesc = isTomorrow ? '明天' : `${month}月${day}日`;\n\n\t\t\t// 构建跳转参数，修正日期格式\n\t\t\tconst monthStr = month < 10 ? '0' + month : month;\n\t\t\tconst dayStr = day < 10 ? '0' + day : day;\n\t\t\tconst formattedDate = `${monthStr}月${dayStr}日`;\n\n\t\t\t// 构建参数对象，主要传递车票ID和基本信息\n\t\t\tconst params = {\n\t\t\t\tticketId: ticketId, // 车票ID，用于接口查询\n\t\t\t\tdeparture: this.departureStation,\n\t\t\t\tarrival: this.arrivalStation,\n\t\t\t\tdate: formattedDate,\n\t\t\t\tdateDesc: dateDesc,\n\t\t\t\t// 以下为备用数据，如果接口调用失败时使用\n\t\t\t\tdepartureDoor: ticket.departureDoor,\n\t\t\t\ttime: ticket.time,\n\t\t\t\tduration: ticket.duration,\n\t\t\t\ttypeName: ticket.typeName,\n\t\t\t\ttype: ticket.type,\n\t\t\t\toriginalPrice: ticket.originalPrice,\n\t\t\t\tcurrentPrice: ticket.currentPrice,\n\t\t\t\tdiscount: ticket.discount\n\t\t\t};\n\n\t\t\t// 使用URL参数传递车票ID\n\t\t\tconst queryString = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');\n\t\t\tconst url = `/subpkg-booking/ticket_confirm/ticket_confirm?${queryString}`;\n\n\t\t\tuni.navigateTo({\n\t\t\t\turl: url\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\npage {\n\tbackground-color: #f5f7fa;\n}\n\n.content {\n\twidth: 100%;\n\tmin-height: 100vh;\n\tposition: relative;\n}\n\n/* 日期选择器 */\n.date-scroll {\n\twhite-space: nowrap;\n\tbackground-color: #fff;\n\tposition: relative;\n\tz-index: 9;\n\theight: 140rpx;\n\tborder-bottom: 1px solid #eee;\n\tmargin-top: 0;\n\t/* 调整顶部边距 */\n}\n\n.date-list {\n\tdisplay: flex;\n\tpadding: 0 30rpx;\n\theight: 100%;\n\t/* 添加滑动提示效果 */\n\tposition: relative;\n}\n\n.date-list::after {\n\tcontent: '';\n\tposition: absolute;\n\tright: 120rpx; /* 预留日历图标的位置 */\n\ttop: 0;\n\tbottom: 0;\n\twidth: 60rpx;\n\tbackground: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.8));\n\tpointer-events: none; /* 确保不影响触摸事件 */\n}\n\n.date-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tmin-width: 100rpx;\n\theight: 100%;\n\tpadding: 0 10rpx;\n}\n\n.date-item-active {\n\tcolor: #3F8DF9;\n}\n\n.week-day {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-bottom: 10rpx;\n\tmargin-top: 20rpx;\n}\n\n.date-item-active .week-day {\n\tcolor: #3F8DF9;\n}\n\n.day-num {\n\tfont-size: 32rpx;\n\tcolor: #333;\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 50%;\n\tmargin-bottom: 20rpx;\n}\n\n.date-item-active .day-num {\n\tbackground-color: #3F8DF9;\n\tcolor: #fff;\n}\n\n.today-circle {\n\tbackground-color: #e6f0ff;\n\tcolor: #3F8DF9;\n}\n\n.date-item-active .today-circle {\n\tbackground-color: #3F8DF9;\n\tcolor: #fff;\n}\n\n/* 日历图标 */\n.calendar-icon {\n\tposition: absolute;\n\tright: 20rpx;\n\ttop: 0;\n\t/* 调整顶部位置 */\n\tz-index: 11;\n\twidth: 100rpx;\n\theight: 140rpx;\n\tbackground-color: #fff;\n\tbox-shadow: -5rpx 0 10rpx rgba(0, 0, 0, 0.1); /* 只在左边添加阴影 */\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.calendar-img {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tdisplay: block;\n}\n\n.arrow-img {\n\twidth: 20rpx;\n\theight: 20rpx;\n\tmargin-top: 6rpx;\n}\n\n/* 车票列表 */\n.ticket-list {\n\tpadding: 20rpx;\n}\n\n/* 加载状态 */\n.loading-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 100rpx 0;\n}\n\n.loading-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n/* 空状态 */\n.empty-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 100rpx 0;\n}\n\n.empty-text {\n\tfont-size: 32rpx;\n\tcolor: #666;\n\tmargin-bottom: 20rpx;\n}\n\n.empty-tip {\n\tfont-size: 26rpx;\n\tcolor: #999;\n}\n\n.ticket-item {\n\tbackground-color: #fff;\n\tborder-radius: 12rpx;\n\tpadding: 30rpx 20rpx;\n\tmargin-bottom: 16rpx;\n\tbox-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.08);\n\tposition: relative;\n}\n\n/* 无票状态的禁用样式 */\n.ticket-disabled {\n\topacity: 0.6;\n\tbackground-color: #f5f5f5;\n\tpointer-events: none;\n}\n\n/* 移除商务车类型的左侧竖线 */\n.business {\n\tposition: relative;\n}\n\n/* 移除商务车的黄色竖线 */\n.business::before {\n\tdisplay: none;\n}\n\n/* 即将发车标签 - 左上角绝对定位 */\n.departure-tag {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tfont-size: 20rpx;\n\tcolor: #ff9500; /* 深黄色字体 */\n\tbackground: rgba(246,136,11,0.1); /* 浅黄色背景 */\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 0 0 8rpx 0;\n\tline-height: 1;\n\tz-index: 2;\n}\n\n/* 主要内容区域 */\n.ticket-content {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: flex-start;\n\tmargin-top: 0;\n}\n\n.ticket-left {\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n\tmin-height: 100rpx;\n\twidth: 120rpx;\n}\n\n/* 时间区域 */\n.time-section {\n\tdisplay: flex;\n\tflex-direction: column;\n\tmargin-bottom: 24rpx;\n}\n\n.ticket-time {\n\tfont-size: 44rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tline-height: 1;\n\tmargin-bottom: 20rpx;\n}\n\n.ticket-duration {\n\tfont-size: 25rpx;\n\tcolor: #999;\n\tline-height: 1;\n}\n\n.info-icon {\n\tcolor: #999; /* 修改为与字体相同颜色 */\n\tmargin-left: 4rpx;\n}\n\n/* 站点信息 - 中间区域 */\n.ticket-stations {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin: 0 24rpx;\n\tflex: 1;\n}\n\n.station-line {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tmargin-right: 20rpx;\n\tpadding-top: 0; /* 移除上边距与站点名称对齐 */\n}\n\n.station-dot {\n\twidth: 16rpx;\n\theight: 16rpx;\n\tborder-radius: 50%;\n\tposition: relative;\n}\n\n.start {\n\tbackground-color: #3F8DF9;\n}\n\n.end {\n\tbackground-color: #ff9500;\n}\n\n.station-connector {\n\twidth: 2rpx;\n\theight: 50rpx; /* 增加连接线高度 */\n\tbackground-color: #ddd;\n\tmargin: 6rpx 0;\n}\n\n.station-names {\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n\tmin-height: 108rpx;\n}\n\n.station-name {\n\tfont-size: 30rpx; /* 增大字体 */\n\tfont-weight: bold; /* 加粗 */\n\tcolor: #333;\n\tline-height: 38rpx;\n}\n\n/* 车票类型标签 */\n.ticket-type-tag {\n\talign-self: flex-start;\n\tmargin-top: 6rpx;\n}\n\n.type-text {\n\tfont-size: 22rpx;\n\tfont-weight: bold;\n\tcolor: #000000;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 6rpx;\n\tline-height: 1;\n}\n\n.ticket-right {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-end;\n\tjustify-content: space-between;\n\tmin-height: 100rpx;\n\tpadding-left: 20rpx;\n\twidth: 140rpx; /* 增加宽度 */\n}\n\n.price-box {\n\ttext-align: right;\n\tmargin-bottom: 20rpx;\n\tdisplay: flex; /* 使价格在同一行 */\n\talign-items: baseline;\n\tjustify-content: flex-end;\n}\n\n.price-original {\n\tfont-size: 24rpx;\n\tcolor: #ccc;\n\tmargin-right: 8rpx; /* 添加右侧间距 */\n\tline-height: 1;\n}\n\n.price-through {\n\ttext-decoration: line-through;\n}\n\n.price-current {\n\tcolor: #ff9500;\n\tfont-weight: bold;\n\tline-height: 1;\n}\n\n.price-symbol {\n\tfont-size: 34rpx;\n}\n\n.price-value {\n\tfont-size: 42rpx;\n}\n\n.discount-tag {\n\tfont-size: 20rpx;\n\tcolor: #ff9500;\n\tbackground-color: #fff5e6;\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 6rpx;\n\tmargin-bottom: 30rpx;\n\tborder: 1rpx solid #ffe0b3;\n\tline-height: 1;\n}\n\n.ticket-status {\n\tfont-size: 26rpx;\n\tcolor: #999;\n\tline-height: 1;\n}\n\n/* 日历弹窗 */\n.calendar-popup {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tz-index: 999;\n}\n\n.calendar-mask {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n}\n\n.calendar-container {\n\tposition: absolute;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: #fff;\n\tborder-radius: 20rpx 20rpx 0 0;\n\tpadding: 30rpx;\n\tanimation: slideUp 0.3s ease;\n}\n\n@keyframes slideUp {\n\tfrom {\n\t\ttransform: translateY(100%);\n\t}\n\n\tto {\n\t\ttransform: translateY(0);\n\t}\n}\n\n.calendar-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n\tmargin-bottom: 20rpx;\n}\n\n.calendar-close {\n\tposition: absolute;\n\tleft: 0;\n\tfont-size: 48rpx;\n\tcolor: #333;\n\tline-height: 1;\n}\n\n.calendar-nav {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.calendar-title {\n\tfont-size: 34rpx;\n\tfont-weight: bold;\n}\n\n.calendar-footer {\n\tmargin-top: 20rpx;\n\tpadding-bottom: 20rpx;\n}\n\n.calendar-confirm {\n\theight: 90rpx;\n\tbackground-color: #333;\n\tcolor: #fff;\n\tborder-radius: 45rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tfont-size: 32rpx;\n}\n\n/* 自定义uni-calendar样式 */\n.custom-calendar {\n\t--calendar-border-color: #f5f5f5;\n\t--calendar-text-color: #333;\n\t--calendar-lunar-color: #999;\n\t--calendar-background-color: #fff;\n\t--calendar-selected-background-color: #3F8DF9;\n\t--calendar-selected-lunar-color: #fff;\n\t--calendar-selected-text-color: #fff;\n}\n\n/* uni-calendar组件样式修改 */\n:deep(.uni-calendar) {\n\tbackground-color: #ffffff;\n}\n\n:deep(.uni-calendar__header) {\n\tdisplay: none !important;\n}\n\n:deep(.uni-calendar__weeks) {\n\tpadding: 10rpx 0;\n}\n\n:deep(.uni-calendar__weeks-day) {\n\theight: 90rpx;\n}\n\n:deep(.uni-calendar__weeks-day-text) {\n\tfont-size: 30rpx;\n\tcolor: #333;\n}\n\n:deep(.uni-calendar__selected) {\n\tbackground-color: #3F8DF9;\n\tcolor: #fff;\n\tborder-radius: 50%;\n\twidth: 70rpx;\n\theight: 70rpx;\n\tline-height: 70rpx;\n\ttext-align: center;\n}\n\n:deep(.uni-calendar__disabled) {\n\tcolor: #ccc;\n\tcursor: default;\n}\n\n:deep(.uni-calendar-item--disable) {\n\tcolor: #ccc;\n\tcursor: default;\n}\n\n:deep(.uni-calendar-item--before-checked), \n:deep(.uni-calendar-item--after-checked) {\n\tbackground-color: rgba(63, 141, 249, 0.1);\n\tcolor: #333;\n}\n\n:deep(.uni-calendar__week-day) {\n\theight: 80rpx;\n\tline-height: 80rpx;\n\ttext-align: center;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n:deep(.uni-calendar__week-day-text) {\n\tcolor: #333;\n}\n\n:deep(.uni-calendar__weeks-day-text) {\n\tcolor: #333;\n}\n\n:deep(.uni-calendar-item__weeks-box-circle) {\n\tborder: 2rpx solid #3F8DF9;\n\tcolor: #3F8DF9;\n\tborder-radius: 50%;\n}\n\n/* 字体图标 */\n@font-face {\n\tfont-family: \"iconfont\";\n\tsrc: url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAALEAAsAAAAABpAAAAJ3AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCCcAqBDIERATYCJAMICwYABCAFhG0HMhvZBREVjDWyHwluhGgUUkk5s7uXKwRBUP1Y9v28mCBJNkk0iSSaJBqkE6FJhEIlFO+Be+7tBhLIXC9JK8STaGSSbJ7sj/6fY6bL58PyW5sDyg0cUF/QF8QBF+D+gnaDbsRhXCcwaNEszkbW7qNGJCtgXCCe1DZGkhlZaMmQN4RW09Ki2ChCo5de04CN4ffxj4qGJGkKWLBx95KZ0P6r+NlKU/9vXRpRAj2cQYoFCowDMnFUG9ovMRg+MPCCS2UEePXqFfxsvef/H48AlVZVIVv939OMPQRRgJ+trfSQGPyqeAkBwJB5UDh7uysSDIZMoUcxDBgsWkJnSHbqgFfwzdHMnD29vdKxE2bWLJzp7jZn9iYTA7pi9R9Sp/768F7Lw/ttt2/Xj8ObUT4+xm8WjB8/4/zIR6ejp1f3ro8f5rWFz5/xq3qv3v+KNnH63PTJk3cO9Z07j24/ka+Pnz4pNA2g9gv48ysguw0wWVYc+vuPrln794DX1Qs1IKuWD7wII/5Zf4PJh3+vLWQtf5kVgDUoesEXRx9imYJBBvAPh+2+SrJflhy2xE6QdFqBoNcagyxvHKXIcVDrtQrNnLPRg07r8x6jRJAlF0BvzDBIhpyCYsgNZlneolTpPdSGAoNBb9G2sMsUqYyImCEHFj+QVe0RmSInWfkGiXhNSBmpSbl4QXwRI4ZM54TYqIqseYZUxm0kggyxSBWxJi6hNBkRaa7oQukmYkW5PNLKmCOyHsWQAww+QKyU5iFmFHFK+fsGItFaI9TBw6n8BYTXwuGDjNaGmDGqJeLbyVA9MQ+JgBhEhVARzCacBEWjrD/IXCLnkbqJLZ1UvZZ9vdf4W14BB7CXliRp0pEOLM50o44GAAA=');\n}\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ticket_list.vue?vue&type=style&index=0&id=d11afc2c&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ticket_list.vue?vue&type=style&index=0&id=d11afc2c&scoped=true&lang=scss&\""], "sourceRoot": ""}