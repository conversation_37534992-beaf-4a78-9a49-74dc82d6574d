{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/subpkg-user/passenger_management/passenger_management.vue?654a", "webpack:///E:/购票系统/购票系统/subpkg-user/passenger_management/passenger_management.vue?f6e4", "webpack:///E:/购票系统/购票系统/subpkg-user/passenger_management/passenger_management.vue?ddff", "webpack:///E:/购票系统/购票系统/subpkg-user/passenger_management/passenger_management.vue?7107", "uni-app:///subpkg-user/passenger_management/passenger_management.vue", "webpack:///E:/购票系统/购票系统/subpkg-user/passenger_management/passenger_management.vue?249e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "passengers", "length", "g1", "isSelectMode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_isMounted", "e0", "$event", "index", "_temp", "arguments", "currentTarget", "dataset", "_temp2", "eventParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectPassengerForReturn", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "originalPassengers", "isLoading", "isRefreshing", "onLoad", "uni", "title", "onShow", "onPullDownRefresh", "methods", "loadPassengers", "request", "then", "console", "passenger", "icon", "duration", "catch", "handleLoadError", "formatPassengerData", "id", "name", "idCard", "rawIdNumber", "selected", "formatIdCard", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success", "idNumber", "res", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatePassenger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "savePassengers", "updateSelectedPassengers", "confirmSelection", "prevPage"], "mappings": "gLAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,4DACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,WAAWC,QACpBC,EAAKT,EAAIU,aAAeV,EAAIW,mBAAmBH,OAAS,KACvDR,EAAIY,aACPZ,EAAIa,GAAK,SAAUC,EAAQC,GACzB,IAAIC,EAAQC,UAAUA,UAAUT,OAAS,GAAGU,cAAcC,QACxDC,EAASJ,EAAMK,aAAeL,EAAM,gBACpCD,EAAQK,EAAOL,MAEjBf,EAAIU,aACAV,EAAIsB,gBAAgBP,GACpBf,EAAIuB,yBAAyBR,KAGrCf,EAAIwB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLxB,GAAIA,EACJK,GAAIA,MAKRoB,GAAmB,EACnBC,EAAkB,GACtB/B,EAAOgC,eAAgB,G,iCC9BvB,yHAAqrB,eAAG,G,qJC4CxrB,4lBAEA,CACAN,gBACA,OACAlB,cACAyB,sBACAC,aACAC,gBACAxB,gBACAC,wBAGAwB,mBAEA,uBACA,qBAEAC,yBACAC,iBAKAC,kBAEA,uBAIAC,6BACA,yBAEAC,SAGAC,0BAAA,qEACA,IACA,kBAEAL,eACAC,kBAKAK,uCACAC,kBACAC,2BACA,eACA,kBAEA,GACAR,gBAIA,WAEA,2CAEA,2CAGA,iBACA,kCACAS,iBAEA,8BAIA,mBAEA,IACAT,aACAC,aACAS,eACAC,gBAGAX,0BAGA,uBAGAY,mBACAJ,6BACA,eACA,kBAEA,EAIAR,wBAHAA,gBAMA,wBAKAa,2BACAb,aACAC,iBACAS,cAIA,IACA,qCAEA,gBADA,EACA,cAEA,GAEA,SACAF,8BACA,qBAKAM,gCAAA,WAEA,iCACAC,QACAC,gBACAC,sCACAC,2BACAC,8BAKAC,yBACA,oBACA,wCACA,aACA,qCAEA,GAIAC,wBACArB,cACAsB,oDAKAC,0BAAA,WACAvB,cACAsB,iDACAE,oBAEA,aACA,qBACAC,uCAGAC,2CACAjB,YACA9B,cAOAgD,4BACA,wBACA,sBAEA,uBAIAC,8BACA,iCACA,qBACA,wBAKAC,4BACA,iCACA,4BACA,wBAKAC,0BACA,IACA9B,+DACA,SACAQ,8BAKAtB,4BACA,oBAGA,yDAGA,kCAIA6C,oCACA,kFAIAC,4BACA,uCASA,wBACA,gBAEA,6DAEAC,yDAIAjC,sBAjBAA,aACAC,kBACAS,eAmBAvB,qCAEA,yBAGA,oBACA,gBAEA,6DAEA8C,qCAIAjC,oBAGA,c,6DClTA,yHAA4wC,eAAG,G", "file": "subpkg-user/passenger_management/passenger_management.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg-user/passenger_management/passenger_management.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./passenger_management.vue?vue&type=template&id=d6de57a4&\"\nvar renderjs\nimport script from \"./passenger_management.vue?vue&type=script&lang=js&\"\nexport * from \"./passenger_management.vue?vue&type=script&lang=js&\"\nimport style0 from \"./passenger_management.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg-user/passenger_management/passenger_management.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./passenger_management.vue?vue&type=template&id=d6de57a4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.passengers.length\n  var g1 = _vm.isSelectMode ? _vm.selectedPassengers.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        index = _temp2.index\n      var _temp, _temp2\n      _vm.isSelectMode\n        ? _vm.selectPassenger(index)\n        : _vm.selectPassengerForReturn(index)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./passenger_management.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./passenger_management.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 乘客列表 -->\r\n\t\t<view class=\"passenger-list\">\r\n\t\t\t<view v-if=\"passengers.length > 0\">\r\n\t\t\t\t<view class=\"passenger-item\" v-for=\"(passenger, index) in passengers\" :key=\"index\"\r\n\t\t\t\t\t@click=\"isSelectMode ? selectPassenger(index) : selectPassengerForReturn(index)\">\r\n\t\t\t\t\t<!-- 选择模式下显示选择框 -->\r\n\t\t\t\t\t<view v-if=\"isSelectMode\" class=\"passenger-checkbox\">\r\n\t\t\t\t\t\t<view :class=\"['checkbox-circle', { 'checkbox-active': passenger.selected }]\">\r\n\t\t\t\t\t\t\t<view v-if=\"passenger.selected\" class=\"checkbox-inner\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"passenger-info\">\r\n\t\t\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t\t\t<text class=\"passenger-name\">{{passenger.name}}</text>\r\n\t\t\t\t\t\t\t<text class=\"id-type\">身份证</text>\r\n\t\t\t\t\t\t\t<text class=\"passenger-id\">{{passenger.idCard}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 非选择模式下显示编辑按钮 -->\r\n\t\t\t\t\t<view v-if=\"!isSelectMode\" class=\"edit-button\" @click.stop=\"editPassenger(index)\">\r\n\t\t\t\t\t\t<image class=\"edit-icon\" src=\"/static/icons/idet.png\"></image>\r\n\t\t\t\t\t\t<text class=\"edit-text\">编辑</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-else class=\"empty-tip\">\r\n\t\t\t\t<text>暂无乘客信息</text>\r\n\t\t\t\t<text class=\"add-tip\">点击下方按钮添加新乘客</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 选择模式下显示确认按钮，否则显示添加乘客按钮 -->\r\n\t\t<view v-if=\"isSelectMode\" class=\"confirm-button\" @click=\"confirmSelection\">\r\n\t\t\t确认选择 ({{ selectedPassengers.length }})\r\n\t\t</view>\r\n\t\t<view v-else class=\"add-button\" @click=\"addPassenger\">添加乘客</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport request from '../../utils/request.js';\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpassengers: [],\r\n\t\t\t\toriginalPassengers: [], // 存储原始数据（包含完整身份证号）\r\n\t\t\t\tisLoading: false,\r\n\t\t\t\tisRefreshing: false,\r\n\t\t\t\tisSelectMode: false, // 是否为选择模式\r\n\t\t\t\tselectedPassengers: [] // 已选择的乘客\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// 检查是否为选择模式\r\n\t\t\tif (options && options.mode === 'select') {\r\n\t\t\t\tthis.isSelectMode = true;\r\n\t\t\t\t// 设置导航栏标题\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: '选择乘客'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonShow() {\r\n\t\t\t// 每次页面显示时获取乘客列表\r\n\t\t\tthis.loadPassengers();\r\n\t\t},\r\n\r\n\t\t// 下拉刷新\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.loadPassengers(true);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\r\n\t\t\t// 加载乘客列表\r\n\t\t\tloadPassengers(isRefresh = false) {\r\n\t\t\t\tif (!isRefresh) {\r\n\t\t\t\t\tthis.isLoading = true;\r\n\t\t\t\t\t// 显示加载提示\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 调用接口获取乘客列表\r\n\t\t\t\trequest.get('/app/information/list')\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tconsole.log('获取乘客列表成功:', res);\r\n\t\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\t\tthis.isRefreshing = false;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (!isRefresh) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 判断返回数据是否正确\r\n\t\t\t\t\t\tif (res && res.data) {\r\n\t\t\t\t\t\t\t// 保存原始数据\r\n\t\t\t\t\t\t\tthis.originalPassengers = [...res.data];\r\n\t\t\t\t\t\t\t// 转换成页面所需的数据格式\r\n\t\t\t\t\t\t\tthis.passengers = this.formatPassengerData(res.data);\r\n\r\n\t\t\t\t\t\t\t// 如果是选择模式，确保所有乘客都不选中，并更新选中列表\r\n\t\t\t\t\t\t\tif (this.isSelectMode) {\r\n\t\t\t\t\t\t\t\tthis.passengers.forEach(passenger => {\r\n\t\t\t\t\t\t\t\t\tpassenger.selected = false;\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tthis.updateSelectedPassengers();\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t// 将获取到的数据保存到本地存储中\r\n\t\t\t\t\t\t\tthis.savePassengers();\r\n\r\n\t\t\t\t\t\t\tif (isRefresh) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '刷新成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t// 停止下拉刷新\r\n\t\t\t\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.handleLoadError();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取乘客列表失败:', err);\r\n\t\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\t\tthis.isRefreshing = false;\r\n\r\n\t\t\t\t\t\tif (!isRefresh) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 停止下拉刷新\r\n\t\t\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tthis.handleLoadError();\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理加载错误\r\n\t\t\thandleLoadError() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取乘客列表失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 尝试从本地存储获取\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst passengers = uni.getStorageSync('passengers');\r\n\t\t\t\t\tif (passengers) {\r\n\t\t\t\t\t\tthis.passengers = JSON.parse(passengers);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.passengers = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('获取本地乘客列表失败', e);\r\n\t\t\t\t\tthis.passengers = [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化接口返回的数据\r\n\t\t\tformatPassengerData(apiData) {\r\n\t\t\t\t// 将接口返回的数据格式化为页面所需格式\r\n\t\t\t\treturn apiData.map(item => ({\r\n\t\t\t\t\tid: item.id, // 保存乘客ID\r\n\t\t\t\t\tname: item.name || '',\r\n\t\t\t\t\tidCard: this.formatIdCard(item.idNumber || ''),\r\n\t\t\t\t\trawIdNumber: item.idNumber || '', // 保存原始身份证号\r\n\t\t\t\t\tselected: this.isSelectMode ? false : true // 选择模式下默认不选中，管理模式下默认选中\r\n\t\t\t\t}));\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化身份证号（中间部分用*号代替）\r\n\t\t\tformatIdCard(idCard) {\r\n\t\t\t\tif (idCard.length >= 18) {\r\n\t\t\t\t\treturn idCard.substr(0, 4) + '**********' + idCard.substr(14);\r\n\t\t\t\t} else if (idCard.length >= 15) {\r\n\t\t\t\t\treturn idCard.substr(0, 4) + '*******' + idCard.substr(11);\r\n\t\t\t\t}\r\n\t\t\t\treturn idCard;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 添加乘客\r\n\t\t\taddPassenger() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/subpkg-user/passenger_edit/passenger_edit'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 编辑乘客\r\n\t\t\teditPassenger(index) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/subpkg-user/passenger_edit/passenger_edit',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t// 传递乘客数据到编辑页面（包含完整身份证号）\r\n\t\t\t\t\t\tconst passengerData = {\r\n\t\t\t\t\t\t\t...this.passengers[index],\r\n\t\t\t\t\t\t\tidNumber: this.passengers[index].rawIdNumber // 传递完整的身份证号\r\n\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\tres.eventChannel.emit('acceptPassengerData', {\r\n\t\t\t\t\t\t\tpassenger: passengerData,\r\n\t\t\t\t\t\t\tindex: index\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 添加新乘客（供编辑页面调用）\r\n\t\t\taddNewPassenger(passenger) {\r\n\t\t\t\tthis.passengers.push(passenger);\r\n\t\t\t\tthis.savePassengers();\r\n\t\t\t\t// 刷新数据\r\n\t\t\t\tthis.loadPassengers();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 更新乘客信息（供编辑页面调用）\r\n\t\t\tupdatePassenger(index, passenger) {\r\n\t\t\t\tif (index >= 0 && index < this.passengers.length) {\r\n\t\t\t\t\tthis.passengers[index] = passenger;\r\n\t\t\t\t\tthis.savePassengers();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除乘客（供编辑页面调用）\r\n\t\t\tremovePassenger(index) {\r\n\t\t\t\tif (index >= 0 && index < this.passengers.length) {\r\n\t\t\t\t\tthis.passengers.splice(index, 1);\r\n\t\t\t\t\tthis.savePassengers();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 保存乘客列表到本地存储\r\n\t\t\tsavePassengers() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.setStorageSync('passengers', JSON.stringify(this.passengers));\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('保存乘客列表失败', e);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 选择乘客（选择模式下使用）\r\n\t\t\tselectPassenger(index) {\r\n\t\t\t\tif (!this.isSelectMode) return;\r\n\r\n\t\t\t\t// 切换选中状态\r\n\t\t\t\tthis.passengers[index].selected = !this.passengers[index].selected;\r\n\r\n\t\t\t\t// 更新已选择的乘客列表\r\n\t\t\t\tthis.updateSelectedPassengers();\r\n\t\t\t},\r\n\r\n\t\t\t// 更新已选择的乘客列表\r\n\t\t\tupdateSelectedPassengers() {\r\n\t\t\t\tthis.selectedPassengers = this.passengers.filter(passenger => passenger.selected);\r\n\t\t\t},\r\n\r\n\t\t\t// 确认选择\r\n\t\t\tconfirmSelection() {\r\n\t\t\t\tif (this.selectedPassengers.length === 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请至少选择一位乘客',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 将选中的乘客数据传递回购票确认页面\r\n\t\t\t\tconst pages = getCurrentPages();\r\n\t\t\t\tconst prevPage = pages[pages.length - 2]; // 上一个页面\r\n\r\n\t\t\t\tif (prevPage && prevPage.route === 'subpkg-booking/ticket_confirm/ticket_confirm') {\r\n\t\t\t\t\t// 调用购票确认页面的方法，传递选中的乘客\r\n\t\t\t\t\tprevPage.$vm.receiveSelectedPassengers(this.selectedPassengers);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 返回上一页\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\r\n\t\t\t// 非选择模式下点击乘客卡片直接返回\r\n\t\t\tselectPassengerForReturn(index) {\r\n\t\t\t\t// 获取选中的乘客数据\r\n\t\t\t\tconst selectedPassenger = this.passengers[index];\r\n\r\n\t\t\t\t// 将乘客数据传递回购票确认页面\r\n\t\t\t\tconst pages = getCurrentPages();\r\n\t\t\t\tconst prevPage = pages[pages.length - 2]; // 上一个页面\r\n\r\n\t\t\t\tif (prevPage && prevPage.route === 'subpkg-booking/ticket_confirm/ticket_confirm') {\r\n\t\t\t\t\t// 调用购票确认页面的方法，传递单个乘客（包装成数组）\r\n\t\t\t\t\tprevPage.$vm.receiveSelectedPassengers([selectedPassenger]);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 返回上一页\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 移除不再需要的iconfont样式 */\r\npage {\r\n\tbackground-color: #f5f7fa;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.container {\r\n\tpadding: 20rpx;\r\n\tmin-height: 97vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.passenger-list {\r\n\tflex: 1;\r\n\t/* 移除固定高度，让内容自然撑开 */\r\n}\r\n\r\n.passenger-item {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx 24rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.passenger-info {\r\n\tflex: 1;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.info-row {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex-wrap: wrap;\r\n\tgap: 10rpx; /* 使用gap属性提供更一致的间距 */\r\n}\r\n\r\n.passenger-name {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333333;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.id-type {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999999;\r\n\tbackground-color: #F6F7F9;\r\n\tpadding: 2rpx 12rpx;\r\n\tborder-radius: 12rpx;\r\n}\r\n\r\n.passenger-id {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666666;\r\n}\r\n\r\n.edit-button {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tmin-width: 80rpx;\r\n\tpadding: 10rpx;\r\n\tborder-left: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.edit-icon {\r\n\twidth: 42rpx;\r\n\theight: 42rpx;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.edit-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999999;\r\n}\r\n\r\n.add-button {\r\n\theight: 90rpx;\r\n\tbackground-color: #3F8DF9;\r\n\tcolor: #FFFFFF;\r\n\tfont-size: 32rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tborder-radius: 45rpx;\r\n\tmargin: 40rpx 0;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(63, 141, 249, 0.3);\r\n\ttransition: all 0.2s;\r\n}\r\n\r\n.add-button:active {\r\n\ttransform: scale(0.98);\r\n\topacity: 0.9;\r\n}\r\n\r\n.empty-tip {\r\n\tpadding: 100rpx 0;\r\n\ttext-align: center;\r\n\tcolor: #999;\r\n\tfont-size: 32rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.add-tip {\r\n\tfont-size: 28rpx;\r\n\tcolor: #bbb;\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n/* 选择框样式 */\r\n.passenger-checkbox {\r\n\tmargin-right: 20rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.checkbox-circle {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tborder: 2rpx solid #ddd;\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\ttransition: all 0.2s;\r\n}\r\n\r\n.checkbox-active {\r\n\tborder-color: #3F8DF9;\r\n\tbackground-color: #3F8DF9;\r\n}\r\n\r\n.checkbox-inner {\r\n\twidth: 20rpx;\r\n\theight: 20rpx;\r\n\tbackground-color: white;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n/* 确认按钮样式 */\r\n.confirm-button {\r\n\theight: 90rpx;\r\n\tbackground-color: #3F8DF9;\r\n\tcolor: #FFFFFF;\r\n\tfont-size: 32rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tborder-radius: 45rpx;\r\n\tmargin: 40rpx 0;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(63, 141, 249, 0.3);\r\n\ttransition: all 0.2s;\r\n}\r\n\r\n.confirm-button:active {\r\n\ttransform: scale(0.98);\r\n\topacity: 0.9;\r\n}\r\n</style> ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./passenger_management.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./passenger_management.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}