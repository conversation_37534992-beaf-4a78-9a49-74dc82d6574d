(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["subpkg-booking/ticket_list/ticket_list"],{72:function(e,t,n){"use strict";(function(e,t){var a=n(4);n(26);a(n(25));var r=a(n(73));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n(1)["default"],n(2)["createPage"])},73:function(e,t,n){"use strict";n.r(t);var a=n(74),r=n(76);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n(78);var o,c=n(33),s=Object(c["default"])(r["default"],a["render"],a["staticRenderFns"],!1,null,"d11afc2c",null,!1,a["components"],o);s.options.__file="subpkg-booking/ticket_list/ticket_list.vue",t["default"]=s.exports},74:function(e,t,n){"use strict";n.r(t);var a=n(75);n.d(t,"render",(function(){return a["render"]})),n.d(t,"staticRenderFns",(function(){return a["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return a["recyclableRender"]})),n.d(t,"components",(function(){return a["components"]}))},75:function(e,t,n){"use strict";var a;n.r(t),n.d(t,"render",(function(){return r})),n.d(t,"staticRenderFns",(function(){return o})),n.d(t,"recyclableRender",(function(){return i})),n.d(t,"components",(function(){return a}));try{a={uniCalendar:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-calendar/components/uni-calendar/uni-calendar")]).then(n.bind(null,152))}}}catch(c){if(-1===c.message.indexOf("Cannot find module")||-1===c.message.indexOf(".vue"))throw c;console.error(c.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.loading?null:!e.loading&&0===e.ticketList.length),a=e.loading||n?null:e.__map(e.ticketList,(function(t,n){var a=e.__get_orig(t),r=t.originalPrice.toFixed(2),i=t.status.includes("张");return{$orig:a,g1:r,g2:i}}));e.$mp.data=Object.assign({},{$root:{g0:n,l0:a}})},i=!1,o=[];r._withStripped=!0},76:function(e,t,n){"use strict";n.r(t);var a=n(77),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},77:function(e,t,n){"use strict";(function(e){var a=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n(58)),i=a(n(60)),o=n(61),c={data:function(){var e=new Date,t=e.getFullYear(),n=e.getMonth()+1,a=e.getDate(),r="".concat(t,"-").concat(n,"-").concat(a);return{departureStation:"同济大学",arrivalStation:"泰安各县城",currentDateIndex:0,dateList:[],upAddressId:1,downAddressId:2,ticketList:[],loading:!1,loadingText:"加载中...",showCalendar:!1,currentYear:e.getFullYear(),currentMonth:e.getMonth()+1,currentDay:e.getDate(),selectedInfo:null,startDate:r,endDate:"".concat(t+1,"-").concat(n,"-").concat(a),selectedDate:r,weekDayNames:["日","一","二","三","四","五","六"],today:e,scrollLeft:0,screenWidth:375,dateItemWidth:120}},onLoad:function(t){t.departure&&(this.departureStation=decodeURIComponent(t.departure)),t.arrival&&(this.arrivalStation=decodeURIComponent(t.arrival)),t.upAddressId&&(this.upAddressId=parseInt(t.upAddressId)),t.downAddressId&&(this.downAddressId=parseInt(t.downAddressId)),e.setNavigationBarTitle({title:this.departureStation+" — "+this.arrivalStation}),this.generateDateList(),t.selectedDate&&this.setSelectedDate(t.selectedDate),this.loadTicketList()},onReady:function(){var t=this;e.getSystemInfo({success:function(e){t.screenWidth=e.screenWidth,t.currentDateIndex>0&&t.$nextTick((function(){t.scrollToSelectedDate()}))}})},methods:{goBack:function(){e.navigateBack()},generateDateList:function(){for(var e=new Date,t=[],n=0;n<30;n++){var a=new Date;a.setDate(e.getDate()+n);var r=a.getDay(),i=this.weekDayNames[r];t.push({date:a,day:a.getDate(),weekDay:i,isToday:0===n})}this.dateList=t},selectDate:function(e){this.currentDateIndex=e,this.loadTicketList()},setSelectedDate:function(e){var t=this;try{var n=e.split("-");if(3===n.length){var a=parseInt(n[0]),r=parseInt(n[1]),i=parseInt(n[2]),o=new Date(a,r-1,i),c=new Date,s=new Date(c.getFullYear(),c.getMonth(),c.getDate()),d=new Date(o.getFullYear(),o.getMonth(),o.getDate()),u=d-s,l=Math.ceil(u/864e5);l>=0&&l<30?this.currentDateIndex=l:l>=0&&(this.generateDateListFromDate(o),this.currentDateIndex=0),this.$nextTick((function(){t.scrollToSelectedDate()}))}}catch(h){console.error("解析选中日期失败:",h),this.currentDateIndex=0}},generateDateListFromDate:function(e){for(var t=[],n=0;n<30;n++){var a=new Date(e);a.setDate(e.getDate()+n);var r=a.getDay(),i=this.weekDayNames[r],o=new Date,c=a.getDate()===o.getDate()&&a.getMonth()===o.getMonth()&&a.getFullYear()===o.getFullYear();t.push({date:a,day:a.getDate(),weekDay:i,isToday:c})}this.dateList=t},scrollToSelectedDate:function(){var e=this.screenWidth/750,t=this.dateItemWidth*e,n=Math.floor(this.screenWidth/t);if(this.currentDateIndex>2){var a=Math.min(2,Math.floor(n/4)),r=(this.currentDateIndex-a)*t;this.scrollLeft=Math.max(0,Math.round(r))}else this.scrollLeft=0;console.log("滚动计算:",{currentDateIndex:this.currentDateIndex,screenWidth:this.screenWidth,itemWidthPx:t,visibleItems:n,scrollLeft:this.scrollLeft})},loadTicketList:function(){var t=this;return(0,i.default)(r.default.mark((function n(){var a,i,c,s;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,t.loading=!0,t.loadingText="正在查询车票...",a=t.dateList[t.currentDateIndex],i=t.formatDate(a.date),c={carTime:i,upAddress:t.upAddressId,downAddress:t.downAddressId},console.log("查询车票参数:",c),n.next=9,o.ticketApi.getTicketList(c);case 9:s=n.sent,s&&s.data?t.ticketList=t.processTicketData(s.data):(t.ticketList=[],e.showToast({title:"暂无车票信息",icon:"none"})),n.next=18;break;case 13:n.prev=13,n.t0=n["catch"](0),console.error("获取车票列表失败:",n.t0),t.ticketList=[],e.showToast({title:"获取车票信息失败",icon:"none"});case 18:return n.prev=18,t.loading=!1,n.finish(18);case 21:case"end":return n.stop()}}),n,null,[[0,13,18,21]])})))()},processTicketData:function(e){var t=this;return Array.isArray(e)?e.map((function(e){return{time:t.extractTimeFromDateTime(e.departureTime),type:t.getTicketType(e.vehicleType),typeName:e.carType,departureDoor:e.departureDoor,duration:e.travelTime,originalPrice:parseFloat(e.originalPrice),currentPrice:parseFloat(e.currentPrice||e.price),discount:e.discountPrice,discountCount:e.discountCount||0,status:t.getTicketStatus(e.ticketNumber),isImmediateDeparture:e.isImmediateDeparture||!1,originalData:e}})):[]},getTicketType:function(e){switch(e){case 1:case"商务车":return"business";case 2:case"普通车":default:return"normal"}},getTicketStatus:function(e){var t=parseInt(e)||0;return t>=10?"有票":t>0?"".concat(t,"张"):"无票"},formatDate:function(e){var t=e.getFullYear(),n=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(n,"-").concat(a)},extractTimeFromDateTime:function(e){if(!e)return"08:00";try{var t="";if(e.includes(" "))t=e.split(" ")[1];else{if(!e.includes("T"))return e;t=e.split("T")[1]}return t&&t.length>=5?t.substring(0,5):"08:00"}catch(n){return console.error("解析发车时间失败:",e,n),"08:00"}},showDatePicker:function(){this.showCalendar=!0},cancelDatePicker:function(){this.showCalendar=!1},calendarChange:function(e){e.year&&e.month&&e.date&&(this.selectedInfo=e,this.selectedDate="".concat(e.year,"-").concat(e.month,"-").concat(e.date),this.currentYear=e.year,this.currentMonth=e.month)},monthSwitch:function(e){e.year&&e.month&&(this.currentYear=e.year,this.currentMonth=e.month)},confirmDatePicker:function(){if(this.selectedInfo){var e=this.selectedInfo,t=e.year,n=e.month,a=e.date,r=new Date(t,n-1,a),i=new Date,o=r-i,c=Math.ceil(o/864e5);if(c>=0&&c<30)this.currentDateIndex=c;else{for(var s=[],d=0;d<30;d++){var u=new Date(r);u.setDate(r.getDate()-c+d);var l=u.getDay(),h=this.weekDayNames[l],f=u.getDate()===i.getDate()&&u.getMonth()===i.getMonth()&&u.getFullYear()===i.getFullYear();s.push({date:u,day:u.getDate(),weekDay:h,isToday:f})}this.dateList=s,this.currentDateIndex=c}this.showCalendar=!1,this.loadTicketList()}},isDateToday:function(e){var t=new Date;return e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()},goToTicketConfirm:function(t){var n;if("无票"!==t.status){var a=(null===(n=t.originalData)||void 0===n?void 0:n.id)||t.id;if(a){var r=this.dateList[this.currentDateIndex],i=r.date.getMonth()+1,o=r.date.getDate(),c=(r.weekDay,new Date),s=o===c.getDate()+1&&i===c.getMonth()+1,d=s?"明天":"".concat(i,"月").concat(o,"日"),u=i<10?"0"+i:i,l=o<10?"0"+o:o,h="".concat(u,"月").concat(l,"日"),f={ticketId:a,departure:this.departureStation,arrival:this.arrivalStation,date:h,dateDesc:d,departureDoor:t.departureDoor,time:t.time,duration:t.duration,typeName:t.typeName,type:t.type,originalPrice:t.originalPrice,currentPrice:t.currentPrice,discount:t.discount},g=Object.keys(f).map((function(e){return"".concat(e,"=").concat(encodeURIComponent(f[e]))})).join("&"),D="/subpkg-booking/ticket_confirm/ticket_confirm?".concat(g);e.navigateTo({url:D})}else e.showToast({title:"车票信息异常，请重新选择",icon:"none"})}else e.showToast({title:"该班次暂无余票",icon:"none"})}}};t.default=c}).call(this,n(2)["default"])},78:function(e,t,n){"use strict";n.r(t);var a=n(79),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},79:function(e,t,n){}},[[72,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/subpkg-booking/ticket_list/ticket_list.js.map