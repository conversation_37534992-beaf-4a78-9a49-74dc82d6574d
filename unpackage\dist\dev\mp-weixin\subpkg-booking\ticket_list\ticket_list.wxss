@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-d11afc2c {
  background-color: #f5f7fa;
}
.content.data-v-d11afc2c {
  width: 100%;
  min-height: 100vh;
  position: relative;
}
/* 日期选择器 */
.date-scroll.data-v-d11afc2c {
  white-space: nowrap;
  background-color: #fff;
  position: relative;
  z-index: 9;
  height: 140rpx;
  border-bottom: 1px solid #eee;
  margin-top: 0;
  /* 调整顶部边距 */
}
.date-list.data-v-d11afc2c {
  display: flex;
  padding: 0 30rpx;
  height: 100%;
  /* 添加滑动提示效果 */
  position: relative;
}
.date-list.data-v-d11afc2c::after {
  content: "";
  position: absolute;
  right: 120rpx;
  /* 预留日历图标的位置 */
  top: 0;
  bottom: 0;
  width: 60rpx;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.8));
  pointer-events: none;
  /* 确保不影响触摸事件 */
}
.date-item.data-v-d11afc2c {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 100rpx;
  height: 100%;
  padding: 0 10rpx;
}
.date-item-active.data-v-d11afc2c {
  color: #3F8DF9;
}
.week-day.data-v-d11afc2c {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  margin-top: 20rpx;
}
.date-item-active .week-day.data-v-d11afc2c {
  color: #3F8DF9;
}
.day-num.data-v-d11afc2c {
  font-size: 32rpx;
  color: #333;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 20rpx;
}
.date-item-active .day-num.data-v-d11afc2c {
  background-color: #3F8DF9;
  color: #fff;
}
.today-circle.data-v-d11afc2c {
  background-color: #e6f0ff;
  color: #3F8DF9;
}
.date-item-active .today-circle.data-v-d11afc2c {
  background-color: #3F8DF9;
  color: #fff;
}
/* 日历图标 */
.calendar-icon.data-v-d11afc2c {
  position: absolute;
  right: 20rpx;
  top: 0;
  /* 调整顶部位置 */
  z-index: 11;
  width: 100rpx;
  height: 140rpx;
  background-color: #fff;
  box-shadow: -5rpx 0 10rpx rgba(0, 0, 0, 0.1);
  /* 只在左边添加阴影 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.calendar-img.data-v-d11afc2c {
  width: 40rpx;
  height: 40rpx;
  display: block;
}
.arrow-img.data-v-d11afc2c {
  width: 20rpx;
  height: 20rpx;
  margin-top: 6rpx;
}
/* 车票列表 */
.ticket-list.data-v-d11afc2c {
  padding: 20rpx;
}
/* 加载状态 */
.loading-container.data-v-d11afc2c {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.loading-text.data-v-d11afc2c {
  font-size: 28rpx;
  color: #999;
}
/* 空状态 */
.empty-container.data-v-d11afc2c {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.empty-text.data-v-d11afc2c {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.empty-tip.data-v-d11afc2c {
  font-size: 26rpx;
  color: #999;
}
.ticket-item.data-v-d11afc2c {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.08);
  position: relative;
}
/* 无票状态的禁用样式 */
.ticket-disabled.data-v-d11afc2c {
  opacity: 0.6;
  background-color: #f5f5f5;
  pointer-events: none;
}
/* 移除商务车类型的左侧竖线 */
.business.data-v-d11afc2c {
  position: relative;
}
/* 移除商务车的黄色竖线 */
.business.data-v-d11afc2c::before {
  display: none;
}
/* 即将发车标签 - 左上角绝对定位 */
.departure-tag.data-v-d11afc2c {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 20rpx;
  color: #ff9500;
  /* 深黄色字体 */
  background: rgba(246, 136, 11, 0.1);
  /* 浅黄色背景 */
  padding: 6rpx 12rpx;
  border-radius: 0 0 8rpx 0;
  line-height: 1;
  z-index: 2;
}
/* 主要内容区域 */
.ticket-content.data-v-d11afc2c {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 0;
}
.ticket-left.data-v-d11afc2c {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 100rpx;
  width: 120rpx;
}
/* 时间区域 */
.time-section.data-v-d11afc2c {
  display: flex;
  flex-direction: column;
  margin-bottom: 24rpx;
}
.ticket-time.data-v-d11afc2c {
  font-size: 44rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 20rpx;
}
.ticket-duration.data-v-d11afc2c {
  font-size: 25rpx;
  color: #999;
  line-height: 1;
}
.info-icon.data-v-d11afc2c {
  color: #999;
  /* 修改为与字体相同颜色 */
  margin-left: 4rpx;
}
/* 站点信息 - 中间区域 */
.ticket-stations.data-v-d11afc2c {
  display: flex;
  align-items: center;
  margin: 0 24rpx;
  flex: 1;
}
.station-line.data-v-d11afc2c {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
  padding-top: 0;
  /* 移除上边距与站点名称对齐 */
}
.station-dot.data-v-d11afc2c {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  position: relative;
}
.start.data-v-d11afc2c {
  background-color: #3F8DF9;
}
.end.data-v-d11afc2c {
  background-color: #ff9500;
}
.station-connector.data-v-d11afc2c {
  width: 2rpx;
  height: 50rpx;
  /* 增加连接线高度 */
  background-color: #ddd;
  margin: 6rpx 0;
}
.station-names.data-v-d11afc2c {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 108rpx;
}
.station-name.data-v-d11afc2c {
  font-size: 30rpx;
  /* 增大字体 */
  font-weight: bold;
  /* 加粗 */
  color: #333;
  line-height: 38rpx;
}
/* 车票类型标签 */
.ticket-type-tag.data-v-d11afc2c {
  align-self: flex-start;
  margin-top: 6rpx;
}
.type-text.data-v-d11afc2c {
  font-size: 22rpx;
  font-weight: bold;
  color: #000000;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  line-height: 1;
}
.ticket-right.data-v-d11afc2c {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  min-height: 100rpx;
  padding-left: 20rpx;
  width: 140rpx;
  /* 增加宽度 */
}
.price-box.data-v-d11afc2c {
  text-align: right;
  margin-bottom: 20rpx;
  display: flex;
  /* 使价格在同一行 */
  align-items: baseline;
  justify-content: flex-end;
}
.price-original.data-v-d11afc2c {
  font-size: 24rpx;
  color: #ccc;
  margin-right: 8rpx;
  /* 添加右侧间距 */
  line-height: 1;
}
.price-through.data-v-d11afc2c {
  text-decoration: line-through;
}
.price-current.data-v-d11afc2c {
  color: #ff9500;
  font-weight: bold;
  line-height: 1;
}
.price-symbol.data-v-d11afc2c {
  font-size: 34rpx;
}
.price-value.data-v-d11afc2c {
  font-size: 42rpx;
}
.discount-tag.data-v-d11afc2c {
  font-size: 20rpx;
  color: #ff9500;
  background-color: #fff5e6;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid #ffe0b3;
  line-height: 1;
}
.ticket-status.data-v-d11afc2c {
  font-size: 26rpx;
  color: #999;
  line-height: 1;
}
/* 日历弹窗 */
.calendar-popup.data-v-d11afc2c {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.calendar-mask.data-v-d11afc2c {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.calendar-container.data-v-d11afc2c {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
  -webkit-animation: slideUp-data-v-d11afc2c 0.3s ease;
          animation: slideUp-data-v-d11afc2c 0.3s ease;
}
@-webkit-keyframes slideUp-data-v-d11afc2c {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUp-data-v-d11afc2c {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.calendar-header.data-v-d11afc2c {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 20rpx;
}
.calendar-close.data-v-d11afc2c {
  position: absolute;
  left: 0;
  font-size: 48rpx;
  color: #333;
  line-height: 1;
}
.calendar-nav.data-v-d11afc2c {
  display: flex;
  align-items: center;
}
.calendar-title.data-v-d11afc2c {
  font-size: 34rpx;
  font-weight: bold;
}
.calendar-footer.data-v-d11afc2c {
  margin-top: 20rpx;
  padding-bottom: 20rpx;
}
.calendar-confirm.data-v-d11afc2c {
  height: 90rpx;
  background-color: #333;
  color: #fff;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}
/* 自定义uni-calendar样式 */
.custom-calendar.data-v-d11afc2c {
  --calendar-border-color: #f5f5f5;
  --calendar-text-color: #333;
  --calendar-lunar-color: #999;
  --calendar-background-color: #fff;
  --calendar-selected-background-color: #3F8DF9;
  --calendar-selected-lunar-color: #fff;
  --calendar-selected-text-color: #fff;
}
/* uni-calendar组件样式修改 */
.data-v-d11afc2c:deep(.uni-calendar) {
  background-color: #ffffff;
}
.data-v-d11afc2c:deep(.uni-calendar__header) {
  display: none !important;
}
.data-v-d11afc2c:deep(.uni-calendar__weeks) {
  padding: 10rpx 0;
}
.data-v-d11afc2c:deep(.uni-calendar__weeks-day) {
  height: 90rpx;
}
.data-v-d11afc2c:deep(.uni-calendar__weeks-day-text) {
  font-size: 30rpx;
  color: #333;
}
.data-v-d11afc2c:deep(.uni-calendar__selected) {
  background-color: #3F8DF9;
  color: #fff;
  border-radius: 50%;
  width: 70rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
}
.data-v-d11afc2c:deep(.uni-calendar__disabled) {
  color: #ccc;
  cursor: default;
}
.data-v-d11afc2c:deep(.uni-calendar-item--disable) {
  color: #ccc;
  cursor: default;
}
.data-v-d11afc2c:deep(.uni-calendar-item--before-checked),.data-v-d11afc2c:deep(.uni-calendar-item--after-checked) {
  background-color: rgba(63, 141, 249, 0.1);
  color: #333;
}
.data-v-d11afc2c:deep(.uni-calendar__week-day) {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}
.data-v-d11afc2c:deep(.uni-calendar__week-day-text) {
  color: #333;
}
.data-v-d11afc2c:deep(.uni-calendar__weeks-day-text) {
  color: #333;
}
.data-v-d11afc2c:deep(.uni-calendar-item__weeks-box-circle) {
  border: 2rpx solid #3F8DF9;
  color: #3F8DF9;
  border-radius: 50%;
}
/* 字体图标 */
@font-face {
  font-family: "iconfont";
  src: url("data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAALEAAsAAAAABpAAAAJ3AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCCcAqBDIERATYCJAMICwYABCAFhG0HMhvZBREVjDWyHwluhGgUUkk5s7uXKwRBUP1Y9v28mCBJNkk0iSSaJBqkE6FJhEIlFO+Be+7tBhLIXC9JK8STaGSSbJ7sj/6fY6bL58PyW5sDyg0cUF/QF8QBF+D+gnaDbsRhXCcwaNEszkbW7qNGJCtgXCCe1DZGkhlZaMmQN4RW09Ki2ChCo5de04CN4ffxj4qGJGkKWLBx95KZ0P6r+NlKU/9vXRpRAj2cQYoFCowDMnFUG9ovMRg+MPCCS2UEePXqFfxsvef/H48AlVZVIVv939OMPQRRgJ+trfSQGPyqeAkBwJB5UDh7uysSDIZMoUcxDBgsWkJnSHbqgFfwzdHMnD29vdKxE2bWLJzp7jZn9iYTA7pi9R9Sp/768F7Lw/ttt2/Xj8ObUT4+xm8WjB8/4/zIR6ejp1f3ro8f5rWFz5/xq3qv3v+KNnH63PTJk3cO9Z07j24/ka+Pnz4pNA2g9gv48ysguw0wWVYc+vuPrln794DX1Qs1IKuWD7wII/5Zf4PJh3+vLWQtf5kVgDUoesEXRx9imYJBBvAPh+2+SrJflhy2xE6QdFqBoNcagyxvHKXIcVDrtQrNnLPRg07r8x6jRJAlF0BvzDBIhpyCYsgNZlneolTpPdSGAoNBb9G2sMsUqYyImCEHFj+QVe0RmSInWfkGiXhNSBmpSbl4QXwRI4ZM54TYqIqseYZUxm0kggyxSBWxJi6hNBkRaa7oQukmYkW5PNLKmCOyHsWQAww+QKyU5iFmFHFK+fsGItFaI9TBw6n8BYTXwuGDjNaGmDGqJeLbyVA9MQ+JgBhEhVARzCacBEWjrD/IXCLnkbqJLZ1UvZZ9vdf4W14BB7CXliRp0pEOLM50o44GAAA=");
}
