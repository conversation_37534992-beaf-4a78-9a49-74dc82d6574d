{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/subpkg-booking/order_detail/order_detail.vue?7ab6", "webpack:///E:/购票系统/购票系统/subpkg-booking/order_detail/order_detail.vue?9778", "webpack:///E:/购票系统/购票系统/subpkg-booking/order_detail/order_detail.vue?ac8c", "webpack:///E:/购票系统/购票系统/subpkg-booking/order_detail/order_detail.vue?b76f", "uni-app:///subpkg-booking/order_detail/order_detail.vue", "webpack:///E:/购票系统/购票系统/subpkg-booking/order_detail/order_detail.vue?cec1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniIcons", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "orderStatus", "passengers", "length", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "isPassengerCollapsed", "orderInfo", "paymentInfo", "actualPayment", "orderId", "orderTime", "payTime", "orderData", "tripInfo", "date", "weekday", "time", "departure", "arrival", "showQrPopup", "groupQrCodeUrl", "reasonRefusal", "onLoad", "uni", "title", "icon", "onUnload", "methods", "processOrderData", "name", "idCard", "type", "formatDateForDisplay", "formatTimeForDisplay", "getWeekday", "formatDateTime", "maskIdCard", "togglePassengerList", "openNavigation", "copyOrderId", "success", "openAfterSale", "content", "resolve", "result", "orderApi", "response", "setTimeout", "fetchOrderDetail", "showGroupQrCode", "config<PERSON>pi", "hideGroupQrCode", "saveQrCode", "url", "reject", "fail", "downloadResult", "filePath", "errorMsg"], "mappings": "kKAAA,MAGA,aACA,WAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,iIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,+CACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,yHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACY,QAApBN,EAAIO,YAAyBP,EAAIQ,WAAWC,OAAS,MAChET,EAAIU,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLV,GAAIA,MAKRW,GAAmB,EACnBC,EAAkB,GACtBjB,EAAOkB,eAAgB,G,gCC1CvB,wHAA6qB,eAAG,G,oJCkIhrB,Q,EAEA,CACAN,gBACA,OACAJ,kBACAW,wBACAC,eACAX,cACAY,aACAC,qBACAC,WACAC,aACAC,YAGAC,eAEAC,UACAC,QACAC,WACAC,QACAC,aACAC,YAEAC,eACAC,kBACAC,mBAGAC,mBAEA,WAEA,MACAtC,kCAGA,0CACA,YACA,iBACA,2BAGAA,gCACAuC,aACAC,iBACAC,iBAKAC,oBAEAH,wCAEAI,SAEAC,6BAEA,0BAGA,uCAGA,eACAd,4DACAC,qDACAC,4DACAC,yCACAC,0CAIA,kBACAW,yBACAC,gDACAC,aAIA,kBACAvB,gEACAC,+BACAC,2DACAC,2DAKAqB,iCACA,iBACA,IACA,kBACA,yCACA,sCACA,iCACA,SACA,aAKAC,iCACA,oBACA,IACA,kBACA,uCACA,yCACA,uCACA,SACA,gBAKAC,uBACA,eACA,IACA,kBACA,uCACA,qBACA,SACA,WAKAC,2BACA,iBACA,IACA,kBACA,kBACA,yCACA,sCACA,uCACA,yCACA,yCACA,yFACA,SACA,aAKAC,uBACA,uBACA,uDAIAC,+BACA,sDAIAC,0BACAf,aACAC,kBACAC,eAKAc,uBACAhB,oBACAzB,8BACA0C,mBACAjB,aACAC,eACAC,qBASAgB,yBAAA,wJAGA,4CAIA,OAHAlB,aACAC,gBACAC,cACA,0CAKA,yBACAF,aACAC,aACAkB,oBACAF,oBACAG,mBAGA,OARA,GAAAC,SAUAA,GAAA,gDASA,OAJArB,eACAC,iBAGA,UACAqB,uCAAA,QAAAC,SAEAvB,gBAEA,iBACAA,aACAC,eACAC,iBAIAsB,uBACAxB,mBACA,OAEAA,aACAC,sBACAC,cAEA,qDAEAF,gBACAvC,8BACAuC,aACAC,mBACAC,cACA,yDA1DA,IA+DAuB,+BAWAC,2BAAA,0IAKA,GALA,SAGA,kBAGA,kFAKAC,uCAAA,OAAAJ,SAEA,gBACA,2BAEAvB,aACAC,iBACAC,cAEA,qDAEAzC,gCACAuC,aACAC,iBACAC,cACA,yDA1BA,IA+BA0B,2BACA,qBAIAC,sBAAA,+IACA,iCAIA,OAHA7B,aACAC,mBACAC,cACA,0BAUA,OAVA,SAMAF,eACAC,kBAGA,SACA,2BACAD,gBACA8B,qBACAb,oBACA,mBACAG,kBAEAW,sBAGAC,YAEA,OAZA,OAAAC,SAAA,UAeA,2BACAjC,0BACAkC,WACAjB,UACAe,YAEA,QAEAhC,gBACAA,aACAC,aACAC,iBACA,qDAEAF,gBACAvC,4BAEA0E,aACA,0CACAA,YACA,gDACAA,YAGAnC,aACAC,QACAC,cACA,yDA1DA,MA8DA,c,4DCleA,wHAAowC,eAAG,G", "file": "subpkg-booking/order_detail/order_detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg-booking/order_detail/order_detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order_detail.vue?vue&type=template&id=28cbab74&\"\nvar renderjs\nimport script from \"./order_detail.vue?vue&type=script&lang=js&\"\nexport * from \"./order_detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order_detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg-booking/order_detail/order_detail.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_detail.vue?vue&type=template&id=28cbab74&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !(_vm.orderStatus === \"待出行\") ? _vm.passengers.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"order-detail-container\">\r\n\t\t<!-- 订单状态 -->\r\n\t\t<view class=\"status-section\">\r\n\t\t\t<text class=\"status-title\">订单状态</text>\r\n\t\t\t<view class=\"status-content\">\r\n\t\t\t\t<view class=\"status-icon\">\r\n\t\t\t\t\t<image :src=\"orderStatus === '待出行' ? '/static/icons/status1.png' : '/static/icons/status2.png'\"\r\n\t\t\t\t\t\tclass=\"status-img\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"status-text\" :style=\"{ color: orderStatus === '待出行' ? '#3F8DF9' : '#303133' }\">{{orderStatus}}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 退款理由模块 -->\r\n\t\t\t<view v-if=\"orderStatus === '拒绝退款' && reasonRefusal\" class=\"refusal-reason-section\">\r\n\t\t\t\t<text class=\"refusal-reason-title\">退款理由</text>\r\n\t\t\t\t<text class=\"refusal-reason-content\">{{reasonRefusal}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 班次信息 -->\r\n\t\t<view class=\"trip-section\">\r\n\t\t\t<text class=\"section-title\">班次信息</text>\r\n\t\t\t<view class=\"trip-time\">\r\n\t\t\t\t<text class=\"trip-date\">{{tripInfo.date}}</text>\r\n\t\t\t\t<text class=\"trip-weekday\">{{tripInfo.weekday}}</text>\r\n\t\t\t\t<text class=\"trip-hour\">{{tripInfo.time}}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"trip-route\">\r\n\t\t\t\t<view class=\"route-stations\">\r\n\t\t\t\t\t<view class=\"station-dot start\"></view>\r\n\t\t\t\t\t<view class=\"station-line\"></view>\r\n\t\t\t\t\t<view class=\"station-dot end\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"station-names\">\r\n\t\t\t\t\t<text class=\"departure-station\">{{tripInfo.departure}}</text>\r\n\t\t\t\t\t<text class=\"arrival-station\">{{tripInfo.arrival}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"navigation-link\" v-if=\"orderStatus === '待出行'\" @click=\"openNavigation\">\r\n\t\t\t\t<text class=\"navigation-text\">上车地点导航</text>\r\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#333333\"></uni-icons>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ticket-count\" v-else>\r\n\t\t\t\t<text class=\"ticket-count-text\">成人票×{{passengers.length}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 乘客信息 -->\r\n\t\t<view class=\"passenger-section\">\r\n\t\t\t<view class=\"section-header\" @click=\"togglePassengerList\">\r\n\t\t\t\t<text class=\"section-title\">乘客信息</text>\r\n\t\t\t\t<view class=\"collapse-btn\">\r\n\t\t\t\t\t<text class=\"collapse-text\">收起</text>\r\n\t\t\t\t\t<uni-icons :type=\"isPassengerCollapsed ? 'bottom' : 'top'\" size=\"16\" color=\"#3F8DF9\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"passenger-list\" v-show=\"!isPassengerCollapsed\">\r\n\t\t\t\t<view class=\"passenger-item\" v-for=\"(passenger, index) in passengers\" :key=\"index\">\r\n\t\t\t\t\t<text class=\"passenger-name\">{{passenger.name}}</text>\r\n\t\t\t\t\t<text class=\"passenger-id\">{{passenger.idCard}}</text>\r\n\t\t\t\t\t<text class=\"passenger-type\">{{passenger.type}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 支付信息 -->\r\n\t\t<view class=\"payment-section\">\r\n\t\t\t<view class=\"payment-item\">\r\n\t\t\t\t<text class=\"payment-label\">实付款</text>\r\n\t\t\t\t<text class=\"payment-value price\">¥{{paymentInfo.actualPayment}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"payment-item\">\r\n\t\t\t\t<text class=\"payment-label\">订单编号</text>\r\n\t\t\t\t<view class=\"order-id\">\r\n\t\t\t\t\t<text class=\"payment-value\">{{paymentInfo.orderId}}</text>\r\n\t\t\t\t\t<text class=\"copy-btn\" @click=\"copyOrderId\">复制</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"payment-item\">\r\n\t\t\t\t<text class=\"payment-label\">下单时间</text>\r\n\t\t\t\t<text class=\"payment-value\">{{paymentInfo.orderTime}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"payment-item\">\r\n\t\t\t\t<text class=\"payment-label\">付款时间</text>\r\n\t\t\t\t<text class=\"payment-value\">{{paymentInfo.payTime}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部操作栏 -->\r\n\t\t<view class=\"bottom-actions\">\r\n\t\t\t<button class=\"consult-btn\" open-type=\"contact\">\r\n\t\t\t\t<uni-icons type=\"phone\" size=\"20\" color=\"#3F8DF9\"></uni-icons>\r\n\t\t\t\t<text class=\"action-text\">咨询</text>\r\n\t\t\t</button>\r\n\t\t\t<button class=\"qr-code-btn\" @click=\"showGroupQrCode\">群二维码</button>\r\n\t\t\t<button class=\"after-sale-btn\" @click=\"openAfterSale\">售后服务</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 群二维码弹窗 -->\r\n\t\t<view class=\"qr-popup\" v-if=\"showQrPopup\" @click=\"hideGroupQrCode\">\r\n\t\t\t<view class=\"qr-popup-content\" @click.stop>\r\n\t\t\t\t<view class=\"qr-popup-header\">\r\n\t\t\t\t\t<text class=\"qr-popup-title\">购票成功</text>\r\n\t\t\t\t\t<view class=\"qr-popup-close\" @click=\"hideGroupQrCode\">\r\n\t\t\t\t\t\t<text class=\"close-icon\">×</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"qr-popup-body\">\r\n\t\t\t\t\t<image v-if=\"groupQrCodeUrl\" :src=\"groupQrCodeUrl\" class=\"qr-code-image\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view v-else class=\"qr-loading\">\r\n\t\t\t\t\t\t<text>加载中...</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"qr-popup-desc\">扫码进群，专业售后服务和时效通知</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"qr-popup-footer\">\r\n\t\t\t\t\t<button class=\"qr-save-btn\" @click=\"saveQrCode\">保存二维码</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { orderApi, configApi } from '@/utils/api.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\torderStatus: '待出行', // 默认为待出行状态\r\n\t\t\tisPassengerCollapsed: false,\r\n\t\t\torderInfo: null,\r\n\t\t\tpassengers: [],\r\n\t\t\tpaymentInfo: {\r\n\t\t\t\tactualPayment: '0.00',\r\n\t\t\t\torderId: '',\r\n\t\t\t\torderTime: '',\r\n\t\t\t\tpayTime: ''\r\n\t\t\t},\r\n\t\t\t// 原始订单数据\r\n\t\t\torderData: null,\r\n\t\t\t// 班次信息\r\n\t\t\ttripInfo: {\r\n\t\t\t\tdate: '',\r\n\t\t\t\tweekday: '',\r\n\t\t\t\ttime: '',\r\n\t\t\t\tdeparture: '',\r\n\t\t\t\tarrival: ''\r\n\t\t\t},\r\n\t\t\tshowQrPopup: false,  // 群二维码弹窗显示状态\r\n\t\t\tgroupQrCodeUrl: '',  // 群二维码图片URL\r\n\t\t\treasonRefusal: ''  // 退款理由\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\t// 获取传递过来的订单ID\r\n\t\tconst orderId = options.id;\r\n\r\n\t\tif(orderId) {\r\n\t\t\tconsole.log('Loaded order id:', orderId);\r\n\r\n\t\t\t// 从本地存储获取订单数据\r\n\t\t\tconst orderData = uni.getStorageSync('orderDetailData');\r\n\t\t\tif (orderData && orderData.id == orderId) {\r\n\t\t\t\tthis.orderData = orderData;\r\n\t\t\t\tthis.processOrderData(orderData);\r\n\t\t\t} else {\r\n\t\t\t\t// 如果本地没有数据，可以调用接口获取\r\n\t\t\t\tconsole.warn('订单数据不存在，需要从接口获取');\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '订单数据加载失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonUnload() {\r\n\t\t// 页面卸载时清理本地存储的订单数据\r\n\t\tuni.removeStorageSync('orderDetailData');\r\n\t},\r\n\tmethods: {\r\n\t\t// 处理订单数据\r\n\t\tprocessOrderData(orderData) {\r\n\t\t\t// 设置订单状态\r\n\t\t\tthis.orderStatus = orderData.status;\r\n\r\n\t\t\t// 设置退款理由（当状态为拒绝退款时）\r\n\t\t\tthis.reasonRefusal = orderData.reasonRefusal || '';\r\n\r\n\t\t\t// 设置班次信息\r\n\t\t\tthis.tripInfo = {\r\n\t\t\t\tdate: this.formatDateForDisplay(orderData.originalData.upTicketTime),\r\n\t\t\t\tweekday: this.getWeekday(orderData.originalData.upTicketTime),\r\n\t\t\t\ttime: this.formatTimeForDisplay(orderData.originalData.upTicketTime),\r\n\t\t\t\tdeparture: orderData.originalData.upAddress || '未知',\r\n\t\t\t\tarrival: orderData.originalData.downAddress || '未知'\r\n\t\t\t};\r\n\r\n\t\t\t// 设置乘客信息（从原始数据中提取）\r\n\t\t\tthis.passengers = [{\r\n\t\t\t\tname: orderData.originalData.name,\r\n\t\t\t\tidCard: this.maskIdCard(orderData.originalData.idNumber),\r\n\t\t\t\ttype: '成人票'\r\n\t\t\t}];\r\n\r\n\t\t\t// 设置支付信息\r\n\t\t\tthis.paymentInfo = {\r\n\t\t\t\tactualPayment: parseFloat(orderData.originalData.payAmout || 0).toFixed(2),\r\n\t\t\t\torderId: orderData.originalData.orderNo,\r\n\t\t\t\torderTime: this.formatDateTime(orderData.originalData.upTicketTime),\r\n\t\t\t\tpayTime: this.formatDateTime(orderData.originalData.upTicketTime)\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\t// 格式化日期显示（MM-dd格式）\r\n\t\tformatDateForDisplay(dateStr) {\r\n\t\t\tif (!dateStr) return '待定';\r\n\t\t\ttry {\r\n\t\t\t\tconst date = new Date(dateStr);\r\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\t\treturn `${month}-${day}`;\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn '待定';\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 格式化时间显示（HH:mm 出发格式）\r\n\t\tformatTimeForDisplay(dateStr) {\r\n\t\t\tif (!dateStr) return '待定 出发';\r\n\t\t\ttry {\r\n\t\t\t\tconst date = new Date(dateStr);\r\n\t\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\r\n\t\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\r\n\t\t\t\treturn `${hours}:${minutes} 出发`;\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn '待定 出发';\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 获取星期几\r\n\t\tgetWeekday(dateStr) {\r\n\t\t\tif (!dateStr) return '';\r\n\t\t\ttry {\r\n\t\t\t\tconst date = new Date(dateStr);\r\n\t\t\t\tconst weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n\t\t\t\treturn weekdays[date.getDay()];\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn '';\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 格式化完整日期时间\r\n\t\tformatDateTime(dateStr) {\r\n\t\t\tif (!dateStr) return '待定';\r\n\t\t\ttry {\r\n\t\t\t\tconst date = new Date(dateStr);\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\r\n\t\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\r\n\t\t\t\tconst seconds = String(date.getSeconds()).padStart(2, '0');\r\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn '待定';\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 身份证号脱敏处理\r\n\t\tmaskIdCard(idCard) {\r\n\t\t\tif (!idCard || idCard.length < 8) return idCard;\r\n\t\t\treturn idCard.substring(0, 4) + '**********' + idCard.substring(idCard.length - 4);\r\n\t\t},\r\n\r\n\t\t// 切换乘客信息显示/隐藏\r\n\t\ttogglePassengerList() {\r\n\t\t\tthis.isPassengerCollapsed = !this.isPassengerCollapsed;\r\n\t\t},\r\n\t\t\r\n\t\t// 打开导航\r\n\t\topenNavigation() {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '打开导航功能开发中',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 复制订单编号\r\n\t\tcopyOrderId() {\r\n\t\t\tuni.setClipboardData({\r\n\t\t\t\tdata: this.paymentInfo.orderId,\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '订单号已复制',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\r\n\t\t\r\n\t\t// 打开售后服务\r\n\t\tasync openAfterSale() {\r\n\t\t\ttry {\r\n\t\t\t\t// 检查是否有订单数据\r\n\t\t\t\tif (!this.orderData || !this.orderData.id) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '订单信息不存在',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 显示确认弹框\r\n\t\t\t\tconst result = await new Promise((resolve) => {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '申请售后',\r\n\t\t\t\t\t\tcontent: '确定要申请售后吗？',\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tresolve(res.confirm);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\r\n\t\t\t\tif (!result) {\r\n\t\t\t\t\treturn; // 用户取消\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 显示加载状态\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '申请中...'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 调用申请售后接口\r\n\t\t\t\tconst response = await orderApi.applyRefund(this.orderData.id);\r\n\r\n\t\t\t\tuni.hideLoading();\r\n\r\n\t\t\t\tif (response && response.code === 200) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '申请售后成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 可以选择返回上一页或刷新当前页面数据\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t}, 1500);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.msg || '申请售后失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('申请售后失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '申请售后失败，请重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取订单详情（实际开发中使用）\r\n\t\tfetchOrderDetail(orderId) {\r\n\t\t\t// 这里实现获取订单详情的接口调用逻辑\r\n\t\t\t// 比如：\r\n\t\t\t// this.$http.get(`/api/orders/${orderId}`).then(res => {\r\n\t\t\t//     this.orderInfo = res.data;\r\n\t\t\t//     this.passengers = res.data.passengers;\r\n\t\t\t//     this.paymentInfo = res.data.paymentInfo;\r\n\t\t\t// })\r\n\t\t},\r\n\r\n\t\t// 显示群二维码弹窗\r\n\t\tasync showGroupQrCode() {\r\n\t\t\ttry {\r\n\t\t\t\t// 显示弹窗\r\n\t\t\t\tthis.showQrPopup = true;\r\n\r\n\t\t\t\t// 如果已经有二维码URL，直接显示\r\n\t\t\t\tif (this.groupQrCodeUrl) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 调用接口获取群二维码\r\n\t\t\t\tconst response = await configApi.getConfig('group_qr_code');\r\n\r\n\t\t\t\tif (response && response.code === 200) {\r\n\t\t\t\t\tthis.groupQrCodeUrl = response.msg || '';\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取群二维码失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取群二维码失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取群二维码失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 隐藏群二维码弹窗\r\n\t\thideGroupQrCode() {\r\n\t\t\tthis.showQrPopup = false;\r\n\t\t},\r\n\r\n\t\t// 保存二维码到相册\r\n\t\tasync saveQrCode() {\r\n\t\t\tif (!this.groupQrCodeUrl) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '二维码加载中，请稍后',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\ttry {\r\n\t\t\t\t// 显示加载提示\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在保存...'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 先下载图片到本地\r\n\t\t\t\tconst downloadResult = await new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\turl: this.groupQrCodeUrl,\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\t\t\tresolve(res.tempFilePath);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\treject(new Error('下载失败'));\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: reject\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 保存到相册\r\n\t\t\t\tawait new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\tfilePath: downloadResult,\r\n\t\t\t\t\t\tsuccess: resolve,\r\n\t\t\t\t\t\tfail: reject\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t} catch (error) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('保存失败:', error);\r\n\r\n\t\t\t\tlet errorMsg = '保存失败，请重试';\r\n\t\t\t\tif (error.errMsg && error.errMsg.includes('auth')) {\r\n\t\t\t\t\terrorMsg = '请授权访问相册';\r\n\t\t\t\t} else if (error.errMsg && error.errMsg.includes('download')) {\r\n\t\t\t\t\terrorMsg = '图片下载失败';\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\npage {\r\n\tbackground-color: #F6F7F9;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.order-detail-container {\r\n\tpadding-bottom: 120rpx; /* 为底部操作栏预留空间 */\r\n}\r\n\r\n/* 公共样式 */\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n/* 订单状态 */\r\n.status-section {\r\n\tbackground-color: #FFFFFF;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.status-title {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333333;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.status-content {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\t// padding: 30rpx 0;\r\n}\r\n\r\n.status-icon {\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.status-img {\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n}\r\n\r\n.status-text {\r\n\tfont-size: 45rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n/* 退款理由模块 */\r\n.refusal-reason-section {\r\n\tmargin-top: 30rpx;\r\n\tpadding-top: 30rpx;\r\n\tborder-top: 1rpx solid #EEEEEE;\r\n}\r\n\r\n.refusal-reason-title {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666666;\r\n\tmargin-bottom: 15rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.refusal-reason-content {\r\n\tfont-size: 30rpx;\r\n\tcolor: #EE0A24;\r\n\tline-height: 1.5;\r\n\tdisplay: block;\r\n}\r\n\r\n/* 班次信息 */\r\n.trip-section {\r\n\tbackground-color: #FFFFFF;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.trip-time {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin: 30rpx 0;\r\n}\r\n\r\n.trip-date {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333333;\r\n\tfont-weight: bold;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.trip-weekday {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666666;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.trip-hour {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666666;\r\n}\r\n\r\n.trip-route {\r\n\tdisplay: flex;\r\n\talign-items: flex-start;\r\n\t// margin-bottom: 30rpx;\r\n}\r\n\r\n.route-stations {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tmargin-right: 20rpx;\r\n\tpadding-top: 17rpx;\r\n}\r\n\r\n.station-dot {\r\n\twidth: 16rpx;\r\n\theight: 16rpx;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.station-dot.start {\r\n\tbackground-color: #3F8DF9;\r\n}\r\n\r\n.station-dot.end {\r\n\tbackground-color: #FF7744;\r\n}\r\n\r\n.station-line {\r\n\twidth: 2rpx;\r\n\theight: 60rpx;\r\n\tbackground-color: #DDDDDD;\r\n\tmargin: 5rpx 0;\r\n}\r\n\r\n.station-names {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.departure-station, .arrival-station {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333333;\r\n\tfont-weight: bold;\r\n\tline-height: 65rpx;\r\n}\r\n\r\n.navigation-link {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: flex-end;\r\n}\r\n\r\n.navigation-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333333;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.ticket-count {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: flex-end;\r\n}\r\n\r\n.ticket-count-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333333;\r\n}\r\n\r\n/* 乘客信息 */\r\n.passenger-section {\r\n\tbackground-color: #FFFFFF;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.section-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.collapse-btn {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.collapse-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #3F8DF9;\r\n\tmargin-right: 5rpx;\r\n}\r\n\r\n.passenger-list {\r\n\tborder-top: 1rpx solid #EEEEEE;\r\n\tpadding-top: 20rpx;\r\n}\r\n\r\n.passenger-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.passenger-name {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333333;\r\n\tfont-weight: bold;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.passenger-id {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999999;\r\n\tflex: 1;\r\n}\r\n\r\n.passenger-type {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999999;\r\n\tbackground-color: #F6F7F9;\r\n\tpadding: 4rpx 12rpx;\r\n\tborder-radius: 4rpx;\r\n}\r\n\r\n/* 支付信息 */\r\n.payment-section {\r\n\tbackground-color: #FFFFFF;\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.payment-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.payment-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666666;\r\n}\r\n\r\n.payment-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333333;\r\n\tmax-width: 400rpx;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.payment-value.price {\r\n\tcolor: #EE0A24;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.order-id {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.copy-btn {\r\n\tfont-size: 28rpx;\r\n\tcolor: #3F8DF9;\r\n\tmargin-left: 20rpx;\r\n}\r\n\r\n/* 底部操作栏 */\r\n.bottom-actions {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbackground-color: #FFFFFF;\r\n\tpadding: 20rpx 30rpx;\r\n\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\tz-index: 10;\r\n}\r\n\r\n.consult-btn {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-right: 30rpx;\r\n\twidth: 100rpx;\r\n\tbackground-color: transparent;\r\n\tborder: none;\r\n\tpadding: 0;\r\n\tfont-size: inherit;\r\n\tline-height: inherit;\r\n}\r\n\r\n.action-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #333333;\r\n\tmargin-top: 5rpx;\r\n}\r\n\r\n.qr-code-btn {\r\n\tflex: 1;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\tbackground-color: #FFFFFF;\r\n\tcolor: #3F8DF9;\r\n\tborder: 1rpx solid #3F8DF9;\r\n\tborder-radius: 40rpx;\r\n\tfont-size: 30rpx;\r\n\tfont-weight: normal;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.after-sale-btn {\r\n\tflex: 1;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\tbackground-color: #FFFFFF;\r\n\tcolor: #3F8DF9;\r\n\tborder: 1rpx solid #3F8DF9;\r\n\tborder-radius: 40rpx;\r\n\tfont-size: 30rpx;\r\n\tfont-weight: normal;\r\n}\r\n\r\n/* 群二维码弹窗样式 */\r\n.qr-popup {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tz-index: 1000;\r\n}\r\n\r\n.qr-popup-content {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 20rpx;\r\n\twidth: 600rpx;\r\n\tmax-height: 80vh;\r\n\toverflow: hidden;\r\n}\r\n\r\n.qr-popup-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 40rpx 40rpx 20rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.qr-popup-title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333333;\r\n}\r\n\r\n.qr-popup-close {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #f5f5f5;\r\n}\r\n\r\n.close-icon {\r\n\tfont-size: 40rpx;\r\n\tcolor: #999999;\r\n\tline-height: 1;\r\n}\r\n\r\n.qr-popup-body {\r\n\tpadding: 40rpx;\r\n\ttext-align: center;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n\r\n.qr-code-image {\r\n\twidth: 400rpx;\r\n\theight: 400rpx;\r\n\tmargin: 0 auto 30rpx;\r\n\tborder-radius: 10rpx;\r\n}\r\n\r\n.qr-loading {\r\n\twidth: 400rpx;\r\n\theight: 400rpx;\r\n\tmargin: 0 auto 30rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tbackground-color: #f5f5f5;\r\n\tborder-radius: 10rpx;\r\n}\r\n\r\n.qr-loading text {\r\n\tcolor: #999999;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.qr-popup-desc {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666666;\r\n\tline-height: 1.5;\r\n}\r\n\r\n.qr-popup-footer {\r\n\tpadding: 20rpx 40rpx 40rpx;\r\n}\r\n\r\n.qr-save-btn {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\tbackground-color: #333333;\r\n\tcolor: #FFFFFF;\r\n\tfont-size: 32rpx;\r\n\tborder-radius: 40rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n</style> ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_detail.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}