@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #F6F7F9;
  min-height: 100vh;
}
.order-detail-container {
  padding-bottom: 120rpx;
  /* 为底部操作栏预留空间 */
}
/* 公共样式 */
.section-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
}
/* 订单状态 */
.status-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.status-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}
.status-content {
  display: flex;
  align-items: center;
}
.status-icon {
  margin-right: 20rpx;
}
.status-img {
  width: 80rpx;
  height: 80rpx;
}
.status-text {
  font-size: 45rpx;
  font-weight: bold;
}
/* 班次信息 */
.trip-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.trip-time {
  display: flex;
  align-items: center;
  margin: 30rpx 0;
}
.trip-date {
  font-size: 30rpx;
  color: #333333;
  font-weight: bold;
  margin-right: 20rpx;
}
.trip-weekday {
  font-size: 28rpx;
  color: #666666;
  margin-right: 20rpx;
}
.trip-hour {
  font-size: 28rpx;
  color: #666666;
}
.trip-route {
  display: flex;
  align-items: flex-start;
}
.route-stations {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
  padding-top: 17rpx;
}
.station-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}
.station-dot.start {
  background-color: #3F8DF9;
}
.station-dot.end {
  background-color: #FF7744;
}
.station-line {
  width: 2rpx;
  height: 60rpx;
  background-color: #DDDDDD;
  margin: 5rpx 0;
}
.station-names {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.departure-station, .arrival-station {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  line-height: 65rpx;
}
.navigation-link {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.navigation-text {
  font-size: 28rpx;
  color: #333333;
  margin-right: 10rpx;
}
.ticket-count {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.ticket-count-text {
  font-size: 28rpx;
  color: #333333;
}
/* 乘客信息 */
.passenger-section {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.collapse-btn {
  display: flex;
  align-items: center;
}
.collapse-text {
  font-size: 28rpx;
  color: #3F8DF9;
  margin-right: 5rpx;
}
.passenger-list {
  border-top: 1rpx solid #EEEEEE;
  padding-top: 20rpx;
}
.passenger-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}
.passenger-name {
  font-size: 30rpx;
  color: #333333;
  font-weight: bold;
  margin-right: 20rpx;
}
.passenger-id {
  font-size: 28rpx;
  color: #999999;
  flex: 1;
}
.passenger-type {
  font-size: 24rpx;
  color: #999999;
  background-color: #F6F7F9;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}
/* 支付信息 */
.payment-section {
  background-color: #FFFFFF;
  padding: 30rpx;
}
.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.payment-label {
  font-size: 28rpx;
  color: #666666;
}
.payment-value {
  font-size: 28rpx;
  color: #333333;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.payment-value.price {
  color: #EE0A24;
  font-weight: bold;
}
.order-id {
  display: flex;
  align-items: center;
}
.copy-btn {
  font-size: 28rpx;
  color: #3F8DF9;
  margin-left: 20rpx;
}
/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}
.consult-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  width: 100rpx;
  background-color: transparent;
  border: none;
  padding: 0;
  font-size: inherit;
  line-height: inherit;
}
.action-text {
  font-size: 24rpx;
  color: #333333;
  margin-top: 5rpx;
}
.qr-code-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #FFFFFF;
  color: #3F8DF9;
  border: 1rpx solid #3F8DF9;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: normal;
  margin-right: 20rpx;
}
.after-sale-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #FFFFFF;
  color: #3F8DF9;
  border: 1rpx solid #3F8DF9;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: normal;
}
/* 群二维码弹窗样式 */
.qr-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.qr-popup-content {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}
.qr-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.qr-popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}
.qr-popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}
.close-icon {
  font-size: 40rpx;
  color: #999999;
  line-height: 1;
}
.qr-popup-body {
  padding: 40rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qr-code-image {
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto 30rpx;
  border-radius: 10rpx;
}
.qr-loading {
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 10rpx;
}
.qr-loading text {
  color: #999999;
  font-size: 28rpx;
}
.qr-popup-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}
.qr-popup-footer {
  padding: 20rpx 40rpx 40rpx;
}
.qr-save-btn {
  width: 100%;
  height: 80rpx;
  background-color: #333333;
  color: #FFFFFF;
  font-size: 32rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
