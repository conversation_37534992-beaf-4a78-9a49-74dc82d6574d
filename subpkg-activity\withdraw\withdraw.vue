<template>
	<view class="withdraw-container">
		<!-- 内容区域 -->
		<view class="content-area">
			<!-- 可提现金额 -->
			<view class="available-amount-section">
				<text class="available-label">可提现金额</text>
				<view class="amount-display">
					<text class="currency-symbol">¥</text>
					<text class="amount-value">{{availableAmount}}</text>
				</view>
			</view>

			<!-- 提现金额输入 -->
			<view class="withdraw-amount-section">
				<view class="section-header">
					<text class="section-title">提现金额</text>
					<text class="all-btn" @click="withdrawAll">全部</text>
				</view>
				<view class="amount-input-container">
					<text class="currency-symbol">¥</text>
					<input 
						class="amount-input" 
						type="digit" 
						placeholder="请输入提现金额"
						v-model="withdrawAmount"
						@input="onAmountInput"
					/>
				</view>
			</view>

			<!-- 微信收款码上传 -->
			<view class="payment-code-section">
				<text class="section-title">微信收款码</text>
				<view class="upload-container" @click="choosePaymentCode">
					<view v-if="!paymentCodeLocalPath && !paymentCodeImage" class="upload-placeholder">
						<uni-icons type="camera" size="40" color="#999999"></uni-icons>
						<text class="upload-text">点击上传微信收款码</text>
					</view>
					<image v-else :src="paymentCodeLocalPath || paymentCodeImage" class="payment-code-image" mode="aspectFit"></image>
				</view>
				<text class="upload-tip">请上传清晰的微信收款码图片</text>
			</view>

			<!-- 立即提现按钮 -->
			<view class="withdraw-btn-container">
				<button
					:class="['withdraw-btn', { disabled: !canWithdraw }]"
					@click="submitWithdraw"
					:disabled="!canWithdraw"
				>
					立即提现
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '../../utils/request.js';
	
	export default {
		data() {
			return {
				availableAmount: 100, // 可提现金额，从上一页传入或接口获取
				withdrawAmount: '', // 提现金额
				paymentCodeImage: '', // 微信收款码图片服务器URL
				paymentCodeLocalPath: '', // 微信收款码本地路径（用于预览）
			}
		},
		computed: {
			canWithdraw() {
				const amount = parseFloat(this.withdrawAmount);
				return amount > 0  && (this.paymentCodeImage || this.paymentCodeLocalPath);
			}
		},
		onLoad(options) {
			// 从上一页获取可提现金额
			if (options.amount) {
				this.availableAmount = parseFloat(options.amount);
			}
		},
		methods: {

			// 金额输入处理
			onAmountInput(e) {
				let value = e.detail.value;
				// 限制只能输入数字和小数点
				value = value.replace(/[^\d.]/g, '');
				// 限制只能有一个小数点
				const parts = value.split('.');
				if (parts.length > 2) {
					value = parts[0] + '.' + parts.slice(1).join('');
				}
				// 限制小数点后最多两位
				if (parts[1] && parts[1].length > 2) {
					value = parts[0] + '.' + parts[1].substring(0, 2);
				}
				this.withdrawAmount = value;
			},

			// 全部提现
			withdrawAll() {
				this.withdrawAmount = this.availableAmount.toString();
			},

			// 选择微信收款码
			choosePaymentCode() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						// 显示上传中
						uni.showLoading({
							title: '上传中...'
						});

						// 上传图片到服务器
						request.upload(tempFilePath, '/common/file/upload', 'file').then(uploadRes => {
							uni.hideLoading();
							if (uploadRes.code === 200) {
								// 保存服务器返回的图片URL
								this.paymentCodeImage =  uploadRes.msg;
								// 同时保存本地路径用于预览
								this.paymentCodeLocalPath = tempFilePath;
								uni.showToast({
									title: '上传成功',
									icon: 'success'
								});
							} else {
								uni.showToast({
									title: '上传失败，请重试',
									icon: 'none'
								});
							}
						}).catch(err => {
							uni.hideLoading();
							console.error('上传图片失败:', err);
							uni.showToast({
								title: '上传失败，请重试',
								icon: 'none'
							});
						});
					},
					fail: (err) => {
						console.error('选择图片失败:', err);
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},

			// 提交提现申请
			submitWithdraw() {
				if (!this.canWithdraw) {
					return;
				}

				const amount = parseFloat(this.withdrawAmount);
				
				// 验证金额
				if (amount <= 0) {
					uni.showToast({
						title: '请输入有效的提现金额',
						icon: 'none'
					});
					return;
				}

				

				if (!this.paymentCodeImage && !this.paymentCodeLocalPath) {
					uni.showToast({
						title: '请上传微信收款码',
						icon: 'none'
					});
					return;
				}

				// 显示确认弹窗
				uni.showModal({
					title: '确认提现',
					content: `确认提现 ¥${amount} 元？`,
					success: (res) => {
						if (res.confirm) {
							this.processWithdraw(amount);
						}
					}
				});
			},

			// 处理提现请求
			processWithdraw(amount) {
				uni.showLoading({
					title: '提交中...'
				});

				// 调用立即提现接口
				request.post('/app/record', {
					imageUrl: this.paymentCodeImage,
					income: amount
				}).then(res => {
					uni.hideLoading();
					if (res && res.code === 200) {
						uni.showToast({
							title: '提现申请已提交',
							icon: 'success'
						});
						setTimeout(() => {
							// 跳转到提现明细页面
							uni.redirectTo({
								url: `/subpkg-activity/withdraw_detail/withdraw_detail?amount=${amount}`
							});
						}, 1500);
					} else {
						uni.showToast({
							title: res.msg || '提现申请失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('提现申请失败:', err);
					uni.showToast({
						title: err.msg,
						icon: 'none'
					});
				});
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #F6F7F9;
}

.withdraw-container {
	min-height: 100vh;
	background-color: #F6F7F9;
}

/* 内容区域 */
.content-area {
	padding: 30rpx;
}

/* 可提现金额 */
.available-amount-section {
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 60rpx 40rpx;
	margin-bottom: 30rpx;
	text-align: center;
}

.available-label {
	font-size: 28rpx;
	color: #666666;
	display: block;
	margin-bottom: 20rpx;
}

.amount-display {
	display: flex;
	align-items: baseline;
	justify-content: center;
}

.currency-symbol {
	font-size: 36rpx;
	color: #FF4757;
	font-weight: bold;
	margin-right: 8rpx;
}

.amount-value {
	font-size: 72rpx;
	color: #FF4757;
	font-weight: bold;
}

/* 提现金额输入 */
.withdraw-amount-section {
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	color: #333333;
	font-weight: bold;
}

.all-btn {
	font-size: 28rpx;
	color: #4A90E2;
	padding: 8rpx 16rpx;
	border: 1rpx solid #4A90E2;
	border-radius: 20rpx;
}

.amount-input-container {
	display: flex;
	align-items: center;
	border-bottom: 2rpx solid #E5E5E5;
	padding-bottom: 20rpx;
}

.amount-input {
	flex: 1;
	font-size: 44rpx;
	color: #333333;
	margin-left: 8rpx;
}

/* 微信收款码上传 */
.payment-code-section {
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 60rpx;
}

.upload-container {
	margin: 30rpx 0;
	border: 2rpx dashed #E5E5E5;
	border-radius: 16rpx;
	min-height: 300rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #FAFAFA;
}

.upload-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.upload-text {
	font-size: 28rpx;
	color: #999999;
	margin-top: 20rpx;
}

.payment-code-image {
	max-width: 100%;
	max-height: 300rpx;
	border-radius: 12rpx;
}

.upload-tip {
	font-size: 24rpx;
	color: #999999;
	text-align: center;
	display: block;
}

/* 立即提现按钮 */
.withdraw-btn-container {
	padding: 0 20rpx;
}

.withdraw-btn {
	width: 100%;
	height: 88rpx;
	background-color: #3B99FC !important;
	background: #3B99FC !important;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #ffffff !important;
	font-weight: bold;
	border: none;
	line-height: 88rpx;
}

.withdraw-btn:not(.disabled) {
	background-color: #3B99FC !important;
	background: #3B99FC !important;
}

.withdraw-btn.disabled {
	background-color: #CCCCCC !important;
	color: #999999 !important;
}

.withdraw-btn::after {
	border: none;
}
</style>
