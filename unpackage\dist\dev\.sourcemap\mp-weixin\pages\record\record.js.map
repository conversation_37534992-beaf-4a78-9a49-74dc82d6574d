{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/pages/record/record.vue?3489", "webpack:///E:/购票系统/购票系统/pages/record/record.vue?1d13", "webpack:///E:/购票系统/购票系统/pages/record/record.vue?5471", "webpack:///E:/购票系统/购票系统/pages/record/record.vue?7441", "uni-app:///pages/record/record.vue", "webpack:///E:/购票系统/购票系统/pages/record/record.vue?d364"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniIcons", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "loading", "filteredOrders", "length", "l0", "__map", "order", "index", "$orig", "__get_orig", "m0", "getStatusIcon", "status", "m1", "getStatusColor", "m2", "getStatusClass", "g1", "price", "toFixed", "g2", "paid", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "activeTab", "orders", "loadingText", "showQrPopup", "groupQrCodeUrl", "computed", "pendingCount", "completedCount", "onLoad", "setTimeout", "onShow", "methods", "loadOrderList", "orderApi", "response", "uni", "title", "icon", "processOrderData", "canCancel", "canRefund", "departureTime", "id", "orderNo", "type", "route", "reasonRefusal", "originalData", "formatDateTime", "switchTab", "goToOrderDetail", "url", "cancelOrder", "content", "success", "applyRefund", "resolve", "result", "showGroupQrCode", "config<PERSON>pi", "hideGroupQrCode", "saveQrCode", "reject", "fail", "downloadResult", "filePath", "errorMsg"], "mappings": "6IAAA,MAGA,aACA,WAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,iIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,0BACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,yHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACTN,EAAIO,QAAsC,KAA5BP,EAAIQ,eAAeC,QACvCC,EAAKV,EAAIW,MAAMX,EAAIQ,gBAAgB,SAAUI,EAAOC,GACtD,IAAIC,EAAQd,EAAIe,WAAWH,GACvBI,EAAKhB,EAAIiB,cAAcL,EAAMM,QAC7BC,EAAKnB,EAAIoB,eAAeR,EAAMM,QAC9BG,EAAKrB,EAAIsB,eAAeV,EAAMM,QAC9BK,EAAKX,EAAMY,MAAMC,QAAQ,GACzBC,EAAKd,EAAMe,KAAKF,QAAQ,GAC5B,MAAO,CACLX,MAAOA,EACPE,GAAIA,EACJG,GAAIA,EACJE,GAAIA,EACJE,GAAIA,EACJG,GAAIA,MAGR1B,EAAI4B,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACL5B,GAAIA,EACJM,GAAIA,MAKRuB,GAAmB,EACnBC,EAAkB,GACtBnC,EAAOoC,eAAgB,G,gCC3DvB,wHAAuqB,eAAG,G,oJC2F1qB,Q,EAEA,CACAN,gBACA,OACAO,YACAC,UACA9B,WACA+B,wBACAC,eACAC,oBAGAC,UAEAjC,0BACA,0BAEA,YACA,mBAEA,sCACAI,oBAIA,sCACAA,4EAKA8B,wBACA,6CACA9B,oBACA,QAGA+B,0BACA,6CACA/B,2EACA,SAGAgC,mBAAA,WAKA,8BAEAC,uBACA,sBACA,MAGAC,kBAEA,sBAEAC,SAEAC,yBAAA,0IAKA,OALA,SAEA,aACA,0BAEA,SACAC,0BAAA,OAAAC,SAEA,gBAEA,gCAEA,YACArD,iCACA,mDAEAA,gCACA,YACAsD,aACAC,mBACAC,cACA,QAEA,OAFA,UAEA,wFAvBA,IA4BAC,6BAAA,WAEA,wCAIA,wBAEA,SACA,KACA,KAEA,oBACA,OACApC,QACAqC,KACAC,KACA,MACA,OACAtC,QACAqC,KACAC,KACA,MACA,OACAtC,QACAqC,KACAC,KACA,MACA,OACAtC,QACAqC,KACAC,KACA,MACA,OACAtC,QACAqC,KACAC,KACA,MACA,OACAtC,SACAqC,KACAC,KACA,MACA,QACAtC,SACAqC,KACAC,KAIA,SAGAC,EAFA,eAEAA,iCAGAA,MAIA,qEAEA,OACAC,QACAC,kBACAC,WACA1C,SACA2C,QACArC,gCACAiC,gBACA9B,+BACA4B,YACAC,YAEAM,kCAEAC,mBAzEA,IA+EAC,2BACA,kBAEA,IACA,kBACA,kBACA,yCACA,sCACA,uCACA,yCAEA,2EACA,SAEA,OADAnE,4BACA,QAKAoB,0BACA,UACA,UACA,UACA,0BACA,UACA,mBACA,UACA,UACA,aACA,WACA,qBACA,QACA,qBAKAG,2BACA,UACA,UACA,gBACA,UACA,gBACA,UACA,gBACA,UACA,gBACA,UACA,gBACA,WACA,gBACA,QACA,kBAKAE,2BACA,UACA,UACA,gBACA,UACA,eACA,UACA,kBACA,UACA,iBACA,UACA,kBACA,WACA,gBACA,QACA,oBAIA2C,sBACA,kBAIAC,4BAEA,uDACA,GASAf,sCAGAA,cACAgB,iEAZAhB,aACAC,gBACAC,eAeAe,wBACAjB,aACAC,WACAiB,qBACAC,oBACA,WAEAnB,aACAC,cACAC,qBAQAkB,wBAAA,qKAGA,yBACApB,aACAC,aACAiB,oBACAC,oBACAE,mBAGA,OARA,GAAAC,SAUAA,GAAA,gDASA,OAJAtB,eACAC,iBAGA,SACAH,0BAAA,OAAAC,SAEAC,gBAEA,iBACAA,aACAC,eACAC,iBAIA,mBAEAF,aACAC,sBACAC,cAEA,qDAEAF,gBACAtD,8BACAsD,aACAC,mBACAC,cACA,yDA/CA,IAoDAqB,2BAAA,0IAKA,GALA,SAGA,kBAGA,kFAKAC,uCAAA,OAAAzB,SAEA,gBACA,2BAEAC,aACAC,iBACAC,cAEA,qDAEAxD,gCACAsD,aACAC,iBACAC,cACA,yDA1BA,IA+BAuB,2BACA,qBAIAC,sBAAA,+IACA,iCAIA,OAHA1B,aACAC,mBACAC,cACA,0BAUA,OAVA,SAMAF,eACAC,kBAGA,SACA,2BACAD,gBACAgB,qBACAG,oBACA,mBACAE,kBAEAM,sBAGAC,YAEA,OAZA,OAAAC,SAAA,UAeA,2BACA7B,0BACA8B,WACAX,UACAS,YAEA,QAEA5B,gBACAA,aACAC,aACAC,iBACA,qDAEAF,gBACAtD,4BAEAqF,aACA,0CACAA,YACA,gDACAA,YAGA/B,aACAC,QACAC,cACA,yDA1DA,MA8DA,c,4DCphBA,wHAA8vC,eAAG,G", "file": "pages/record/record.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/record/record.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./record.vue?vue&type=template&id=3b6eb0a6&\"\nvar renderjs\nimport script from \"./record.vue?vue&type=script&lang=js&\"\nexport * from \"./record.vue?vue&type=script&lang=js&\"\nimport style0 from \"./record.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/record/record.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./record.vue?vue&type=template&id=3b6eb0a6&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading ? _vm.filteredOrders.length : null\n  var l0 = _vm.__map(_vm.filteredOrders, function (order, index) {\n    var $orig = _vm.__get_orig(order)\n    var m0 = _vm.getStatusIcon(order.status)\n    var m1 = _vm.getStatusColor(order.status)\n    var m2 = _vm.getStatusClass(order.status)\n    var g1 = order.price.toFixed(2)\n    var g2 = order.paid.toFixed(2)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      g1: g1,\n      g2: g2,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./record.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./record.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"record-container\">\r\n\t\t<!-- 页面顶部标签页 -->\r\n\t\t<view class=\"tab-container\">\r\n\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 0 }\" @click=\"switchTab(0)\">\r\n\t\t\t\t<text class=\"tab-text\">全部订单</text>\r\n\t\t\t\t<view v-if=\"activeTab === 0\" class=\"active-line\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 1 }\" @click=\"switchTab(1)\">\r\n\t\t\t\t<text class=\"tab-text\">待出行<text v-if=\"pendingCount > 0\"\r\n\t\t\t\t\t\tclass=\"tab-count\">({{ pendingCount }})</text></text>\r\n\t\t\t\t<view v-if=\"activeTab === 1\" class=\"active-line\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 2 }\" @click=\"switchTab(2)\">\r\n\t\t\t\t<text class=\"tab-text\">已出行<text v-if=\"completedCount > 0\"\r\n\t\t\t\t\t\tclass=\"tab-count\">({{ completedCount }})</text></text>\r\n\t\t\t\t<view v-if=\"activeTab === 2\" class=\"active-line\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 订单列表 -->\r\n\t\t<view class=\"order-list\">\r\n\t\t\t<!-- 加载状态 -->\r\n\t\t\t<view v-if=\"loading\" class=\"loading-tip\">\r\n\t\t\t\t<text>{{ loadingText }}</text>\r\n\t\t\t</view>\r\n\t\t\t<!-- 空数据提示 -->\r\n\t\t\t<view v-else-if=\"filteredOrders.length === 0\" class=\"empty-tip\">\r\n\t\t\t\t<text>暂无订单记录</text>\r\n\t\t\t</view>\r\n\t\t\t<!-- 使用v-for遍历订单 -->\r\n\t\t\t<view v-for=\"(order, index) in filteredOrders\" :key=\"order.id\" class=\"order-item\"\r\n\t\t\t\t@click=\"goToOrderDetail(order.id)\">\r\n\t\t\t\t<view class=\"order-header\">\r\n\t\t\t\t\t<view class=\"order-type\">\r\n\t\t\t\t\t\t<uni-icons :type=\"getStatusIcon(order.status)\" size=\"22\" :color=\"getStatusColor(order.status)\"\r\n\t\t\t\t\t\t\tclass=\"ticket-icon\"></uni-icons>\r\n\t\t\t\t\t\t<text class=\"ticket-type\">{{ order.type }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text :class=\"['order-status', getStatusClass(order.status)]\">{{ order.status }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"order-route\">\r\n\t\t\t\t\t<text class=\"route-text\">{{ order.route }}</text>\r\n\t\t\t\t\t<text class=\"route-price\">¥{{ order.price.toFixed(2) }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"order-time\">\r\n\t\t\t\t\t<text class=\"time-text\">{{ order.departureTime }} 出发</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"order-divider\"></view>\r\n\t\t\t\t<!-- 合并\"已付\"和按钮到同一行 -->\r\n\t\t\t\t<view class=\"order-payment-actions\">\r\n\t\t\t\t\t<view class=\"payment-part\">\r\n\t\t\t\t\t\t<text class=\"payment-text\">已付: <text\r\n\t\t\t\t\t\t\t\tstyle=\"color: #EE0A24;\">¥{{ order.paid.toFixed(2) }}</text></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"actions-part\">\r\n\t\t\t\t\t\t<!-- <button v-if=\"order.canCancel\" class=\"action-btn cancel-btn\" @click.stop=\"cancelOrder(order.id)\">取消订单</button> -->\r\n\t\t\t\t\t\t<button v-if=\"order.canRefund\" class=\"action-btn refund-btn\"\r\n\t\t\t\t\t\t\************=\"applyRefund(order.id)\">申请售后</button>\r\n\t\t\t\t\t\t<button class=\"action-btn qr-btn\" @click.stop=\"showGroupQrCode\">群二维码</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 群二维码弹窗 -->\r\n\t\t<view class=\"qr-popup\" v-if=\"showQrPopup\" @click=\"hideGroupQrCode\">\r\n\t\t\t<view class=\"qr-popup-content\" @click.stop>\r\n\t\t\t\t<view class=\"qr-popup-header\">\r\n\t\t\t\t\t<text class=\"qr-popup-title\">购票成功</text>\r\n\t\t\t\t\t<view class=\"qr-popup-close\" @click=\"hideGroupQrCode\">\r\n\t\t\t\t\t\t<text class=\"close-icon\">×</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"qr-popup-body\">\r\n\t\t\t\t\t<image v-if=\"groupQrCodeUrl\" :src=\"groupQrCodeUrl\" class=\"qr-code-image\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view v-else class=\"qr-loading\">\r\n\t\t\t\t\t\t<text>加载中...</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"qr-popup-desc\">扫码进群，专业售后服务和时效通知</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"qr-popup-footer\">\r\n\t\t\t\t\t<button class=\"qr-save-btn\" @click=\"saveQrCode\">保存二维码</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\r\n</template>\r\n\r\n<script>\r\nimport { orderApi, configApi } from '@/utils/api.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tactiveTab: 0,  // 默认选中\"全部订单\"\r\n\t\t\torders: [],  // 从接口获取的订单数据\r\n\t\t\tloading: false,  // 加载状态\r\n\t\t\tloadingText: '正在加载订单...',\r\n\t\t\tshowQrPopup: false,  // 群二维码弹窗显示状态\r\n\t\t\tgroupQrCodeUrl: ''  // 群二维码图片URL\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\t// 根据当前选中的标签页筛选订单\r\n\t\tfilteredOrders() {\r\n\t\t\tif (this.activeTab === 0) {\r\n\t\t\t\t// 全部订单\r\n\t\t\t\treturn this.orders;\r\n\t\t\t} else if (this.activeTab === 1) {\r\n\t\t\t\t// 待出行（包括已支付和未支付的订单）\r\n\t\t\t\treturn this.orders.filter(order =>\r\n\t\t\t\t\torder.status === '待出行'\r\n\t\t\t\t);\r\n\t\t\t} else {\r\n\t\t\t\t// 已出行（包括已出行、已退款、待退款、拒绝退款）\r\n\t\t\t\treturn this.orders.filter(order =>\r\n\t\t\t\t\torder.status === '已出行' || order.status === '已退款' || order.status === '待退款' || order.status === '拒绝退款'\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 待出行订单的数量（包括未支付和已支付待出行）\r\n\t\tpendingCount() {\r\n\t\t\treturn this.orders.filter(order =>\r\n\t\t\t\torder.status === '待出行'\r\n\t\t\t).length;\r\n\t\t},\r\n\t\t// 已出行订单的数量（包括已出行、已退款、待退款、拒绝退款）\r\n\t\tcompletedCount() {\r\n\t\t\treturn this.orders.filter(order =>\r\n\t\t\t\torder.status === '已出行' || order.status === '已退款' || order.status === '待退款' || order.status === '拒绝退款'\r\n\t\t\t).length;\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\t// 页面加载时获取订单数据\r\n\t\t// this.loadOrderList();\r\n\r\n\t\t// 检查是否是支付成功跳转过来的\r\n\t\tif (options && options.paymentSuccess === 'true') {\r\n\t\t\t// 延迟显示弹窗，确保页面加载完成\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.showGroupQrCode();\r\n\t\t\t}, 500);\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t// 页面显示时刷新数据（从其他页面返回时也会刷新）\r\n\t\tthis.loadOrderList();\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载订单列表数据\r\n\t\tasync loadOrderList() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tthis.loadingText = '正在加载订单...';\r\n\r\n\t\t\t\t// 调用接口获取订单数据\r\n\t\t\t\tconst response = await orderApi.getOrderList();\r\n\r\n\t\t\t\tif (response && response.code === 200) {\r\n\t\t\t\t\t// 处理接口返回的数据\r\n\t\t\t\t\tthis.orders = this.processOrderData(response);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.orders = [];\r\n\t\t\t\t\tconsole.log('订单数据为空或接口返回异常:', response);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取订单列表失败:', error);\r\n\t\t\t\tthis.orders = [];\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取订单失败，请重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t} finally {\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 处理接口返回的订单数据\r\n\t\tprocessOrderData(data) {\r\n\t\t\t// 处理接口返回的订单数据，转换为页面需要的格式\r\n\t\t\tif (!data || !data.rows || !Array.isArray(data.rows)) {\r\n\t\t\t\treturn [];\r\n\t\t\t}\r\n\r\n\t\t\treturn data.rows.map(order => {\r\n\t\t\t\t// 根据支付状态确定订单状态\r\n\t\t\t\tlet status = '';\r\n\t\t\t\tlet canCancel = false;\r\n\t\t\t\tlet canRefund = false;\r\n\r\n\t\t\t\tswitch (order.payStatus) {\r\n\t\t\t\t\tcase 0: // 未支付\r\n\t\t\t\t\t\tstatus = '未支付';\r\n\t\t\t\t\t\tcanCancel = true;\r\n\t\t\t\t\t\tcanRefund = false;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 1: // 已支付（待出行）\r\n\t\t\t\t\t\tstatus = '待出行';\r\n\t\t\t\t\t\tcanCancel = true;\r\n\t\t\t\t\t\tcanRefund = true;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 2: // 待退款\r\n\t\t\t\t\t\tstatus = '待退款';\r\n\t\t\t\t\t\tcanCancel = false;\r\n\t\t\t\t\t\tcanRefund = false;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 3: // 已退款\r\n\t\t\t\t\t\tstatus = '已退款';\r\n\t\t\t\t\t\tcanCancel = false;\r\n\t\t\t\t\t\tcanRefund = false;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 4: // 已出行\r\n\t\t\t\t\t\tstatus = '已出行';\r\n\t\t\t\t\t\tcanCancel = false;\r\n\t\t\t\t\t\tcanRefund = false;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 6: // 拒绝退款\r\n\t\t\t\t\t\tstatus = '拒绝退款';\r\n\t\t\t\t\t\tcanCancel = false;\r\n\t\t\t\t\t\tcanRefund = false;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\tstatus = '未知状态';\r\n\t\t\t\t\t\tcanCancel = false;\r\n\t\t\t\t\t\tcanRefund = false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 格式化上车时间\r\n\t\t\t\tlet departureTime = '';\r\n\t\t\t\tif (order.upTicketTime) {\r\n\t\t\t\t\t// 如果有具体时间，使用具体时间\r\n\t\t\t\t\tdepartureTime = this.formatDateTime(order.upTicketTime);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果没有具体时间，显示待确定\r\n\t\t\t\t\tdepartureTime = '待确定';\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 构建路线信息\r\n\t\t\t\tconst route = `${order.upAddress || '未知'} → ${order.downAddress || '未知'}`;\r\n\r\n\t\t\t\treturn {\r\n\t\t\t\t\tid: order.id,\r\n\t\t\t\t\torderNo: order.orderNo,\r\n\t\t\t\t\ttype: \"汽车票\", // 固定为汽车票\r\n\t\t\t\t\tstatus: status,\r\n\t\t\t\t\troute: route,\r\n\t\t\t\t\tprice: parseFloat(order.payAmout || 0),\r\n\t\t\t\t\tdepartureTime: departureTime,\r\n\t\t\t\t\tpaid: parseFloat(order.payAmout || 0),\r\n\t\t\t\t\tcanCancel: canCancel,\r\n\t\t\t\t\tcanRefund: canRefund,\r\n\t\t\t\t\t// 退款理由（当状态为拒绝退款时使用）\r\n\t\t\t\t\treasonRefusal: order.reasonRefusal || '',\r\n\t\t\t\t\t// 保留原始数据，便于后续操作\r\n\t\t\t\t\toriginalData: order\r\n\t\t\t\t};\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 格式化日期时间\r\n\t\tformatDateTime(dateStr) {\r\n\t\t\tif (!dateStr) return '待确定';\r\n\r\n\t\t\ttry {\r\n\t\t\t\tconst date = new Date(dateStr);\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\r\n\t\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\r\n\r\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}`;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('日期格式化失败:', error);\r\n\t\t\t\treturn '待确定';\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 根据订单状态获取图标\r\n\t\tgetStatusIcon(status) {\r\n\t\t\tswitch (status) {\r\n\t\t\t\tcase '待出行':\r\n\t\t\t\tcase '未支付':\r\n\t\t\t\t\treturn 'paperplane-filled';\r\n\t\t\t\tcase '已出行':\r\n\t\t\t\t\treturn 'paperplane';\r\n\t\t\t\tcase '已退款':\r\n\t\t\t\tcase '待退款':\r\n\t\t\t\t\treturn 'undo';\r\n\t\t\t\tcase '拒绝退款':\r\n\t\t\t\t\treturn 'close-circle';\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn 'paperplane';\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 根据订单状态获取颜色\r\n\t\tgetStatusColor(status) {\r\n\t\t\tswitch (status) {\r\n\t\t\t\tcase '待出行':\r\n\t\t\t\t\treturn '#3F8DF9';\r\n\t\t\t\tcase '未支付':\r\n\t\t\t\t\treturn '#FF9500';\r\n\t\t\t\tcase '已出行':\r\n\t\t\t\t\treturn '#888888';\r\n\t\t\t\tcase '已退款':\r\n\t\t\t\t\treturn '#00C851';\r\n\t\t\t\tcase '待退款':\r\n\t\t\t\t\treturn '#FF6B6B';\r\n\t\t\t\tcase '拒绝退款':\r\n\t\t\t\t\treturn '#EE0A24';\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn '#888888';\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 根据订单状态获取样式类\r\n\t\tgetStatusClass(status) {\r\n\t\t\tswitch (status) {\r\n\t\t\t\tcase '待出行':\r\n\t\t\t\t\treturn 'pending';\r\n\t\t\t\tcase '未支付':\r\n\t\t\t\t\treturn 'unpaid';\r\n\t\t\t\tcase '已出行':\r\n\t\t\t\t\treturn 'completed';\r\n\t\t\t\tcase '已退款':\r\n\t\t\t\t\treturn 'refunded';\r\n\t\t\t\tcase '待退款':\r\n\t\t\t\t\treturn 'refunding';\r\n\t\t\t\tcase '拒绝退款':\r\n\t\t\t\t\treturn 'refused';\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn 'completed';\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 切换标签页\r\n\t\tswitchTab(index) {\r\n\t\t\tthis.activeTab = index;\r\n\t\t},\r\n\r\n\t\t// 跳转到订单详情页\r\n\t\tgoToOrderDetail(orderId) {\r\n\t\t\t// 找到对应的订单数据\r\n\t\t\tconst orderData = this.orders.find(order => order.id === orderId);\r\n\t\t\tif (!orderData) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '订单信息不存在',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 将订单数据存储到本地，供详情页使用\r\n\t\t\tuni.setStorageSync('orderDetailData', orderData);\r\n\r\n\t\t\t// 跳转到订单详情页\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/subpkg-booking/order_detail/order_detail?id=${orderId}`\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 取消订单\r\n\t\tcancelOrder(orderId) {\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确定要取消该订单吗？',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t// 这里添加取消订单的逻辑\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '订单已取消',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 申请售后\r\n\t\tasync applyRefund(orderId) {\r\n\t\t\ttry {\r\n\t\t\t\t// 显示确认弹框\r\n\t\t\t\tconst result = await new Promise((resolve) => {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '申请售后',\r\n\t\t\t\t\t\tcontent: '确定要申请售后吗？',\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tresolve(res.confirm);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\r\n\t\t\t\tif (!result) {\r\n\t\t\t\t\treturn; // 用户取消\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 显示加载状态\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '申请中...'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 调用申请售后接口\r\n\t\t\t\tconst response = await orderApi.applyRefund(orderId);\r\n\r\n\t\t\t\tuni.hideLoading();\r\n\r\n\t\t\t\tif (response && response.code === 200) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '申请售后成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 刷新订单列表\r\n\t\t\t\t\tthis.loadOrderList();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.msg || '申请售后失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('申请售后失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '申请售后失败，请重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 显示群二维码弹窗\r\n\t\tasync showGroupQrCode() {\r\n\t\t\ttry {\r\n\t\t\t\t// 显示弹窗\r\n\t\t\t\tthis.showQrPopup = true;\r\n\r\n\t\t\t\t// 如果已经有二维码URL，直接显示\r\n\t\t\t\tif (this.groupQrCodeUrl) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 调用接口获取群二维码\r\n\t\t\t\tconst response = await configApi.getConfig('group_qr_code');\r\n\r\n\t\t\t\tif (response && response.code === 200) {\r\n\t\t\t\t\tthis.groupQrCodeUrl = response.msg || '';\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取群二维码失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取群二维码失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取群二维码失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 隐藏群二维码弹窗\r\n\t\thideGroupQrCode() {\r\n\t\t\tthis.showQrPopup = false;\r\n\t\t},\r\n\r\n\t\t// 保存二维码到相册\r\n\t\tasync saveQrCode() {\r\n\t\t\tif (!this.groupQrCodeUrl) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '二维码加载中，请稍后',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\ttry {\r\n\t\t\t\t// 显示加载提示\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在保存...'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 先下载图片到本地\r\n\t\t\t\tconst downloadResult = await new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\turl: this.groupQrCodeUrl,\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\t\t\tresolve(res.tempFilePath);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\treject(new Error('下载失败'));\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: reject\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 保存到相册\r\n\t\t\t\tawait new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\tfilePath: downloadResult,\r\n\t\t\t\t\t\tsuccess: resolve,\r\n\t\t\t\t\t\tfail: reject\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t} catch (error) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('保存失败:', error);\r\n\r\n\t\t\t\tlet errorMsg = '保存失败，请重试';\r\n\t\t\t\tif (error.errMsg && error.errMsg.includes('auth')) {\r\n\t\t\t\t\terrorMsg = '请授权访问相册';\r\n\t\t\t\t} else if (error.errMsg && error.errMsg.includes('download')) {\r\n\t\t\t\t\terrorMsg = '图片下载失败';\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\npage {\r\n\tbackground-color: #F6F7F9;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.record-container {\r\n\tpadding-bottom: 20rpx;\r\n}\r\n\r\n/* 标签页样式 */\r\n.tab-container {\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n\tbackground-color: #FFFFFF;\r\n\tpadding-top: 10rpx;\r\n\tborder-bottom: 1px solid #EEEEEE;\r\n\tposition: sticky;\r\n\ttop: 0;\r\n\tz-index: 10;\r\n}\r\n\r\n.tab-item {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tposition: relative;\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.tab-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333333;\r\n\tpadding-bottom: 16rpx;\r\n}\r\n\r\n.tab-count {\r\n\tfont-size: 24rpx;\r\n\tcolor: #3F8DF9;\r\n\tmargin-left: 4rpx;\r\n}\r\n\r\n.active .tab-text {\r\n\tfont-weight: 500;\r\n\tcolor: #3F8DF9;\r\n}\r\n\r\n.active-line {\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\twidth: 80rpx;\r\n\theight: 6rpx;\r\n\tbackground-color: #3F8DF9;\r\n\tborder-radius: 3rpx;\r\n}\r\n\r\n/* 订单列表样式 */\r\n.order-list {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n/* 加载状态样式 */\r\n.loading-tip {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\theight: 300rpx;\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 8rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.loading-tip text {\r\n\tfont-size: 30rpx;\r\n\tcolor: #3F8DF9;\r\n}\r\n\r\n/* 空订单提示样式 */\r\n.empty-tip {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\theight: 300rpx;\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 8rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.empty-tip text {\r\n\tfont-size: 30rpx;\r\n\tcolor: #999999;\r\n}\r\n\r\n.order-item {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 8rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 订单头部 */\r\n.order-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.order-type {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.ticket-icon {\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.ticket-type {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333333;\r\n}\r\n\r\n.order-status {\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.pending {\r\n\tcolor: #3F8DF9;\r\n}\r\n\r\n.unpaid {\r\n\tcolor: #FF9500;\r\n}\r\n\r\n.completed {\r\n\tcolor: #888888;\r\n}\r\n\r\n.refunded {\r\n\tcolor: #00C851;\r\n}\r\n\r\n.refunding {\r\n\tcolor: #FF6B6B;\r\n}\r\n\r\n.refused {\r\n\tcolor: #EE0A24;\r\n}\r\n\r\n/* 路线信息 */\r\n.order-route {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 16rpx;\r\n}\r\n\r\n.route-text {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333333;\r\n\tfont-weight: bold;\r\n\tflex: 1;\r\n}\r\n\r\n.route-price {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n/* 出发时间 */\r\n.order-time {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.time-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666666;\r\n}\r\n\r\n/* 分割线 */\r\n.order-divider {\r\n\theight: 1rpx;\r\n\tbackground-color: #EEEEEE;\r\n\tmargin: 20rpx 0;\r\n}\r\n\r\n/* 支付信息和操作按钮合并行 */\r\n.order-payment-actions {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.payment-part {\r\n\tflex: 1;\r\n}\r\n\r\n.actions-part {\r\n\tdisplay: flex;\r\n\tjustify-content: flex-end;\r\n}\r\n\r\n/* 支付信息 */\r\n.payment-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666666;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-btn {\r\n\tfont-size: 26rpx;\r\n\theight: 60rpx;\r\n\tline-height: 60rpx;\r\n\tpadding: 0 30rpx;\r\n\tmargin-left: 20rpx;\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 30rpx;\r\n\tdisplay: inline-flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.cancel-btn {\r\n\tcolor: #666666;\r\n\tborder: 1rpx solid #DDDDDD;\r\n}\r\n\r\n.refund-btn {\r\n\tcolor: #666666;\r\n\tborder: 1rpx solid #DDDDDD;\r\n}\r\n\r\n.qr-btn {\r\n\tcolor: #3F8DF9;\r\n\tborder: 1rpx solid #3F8DF9;\r\n}\r\n\r\n/* 群二维码弹窗样式 */\r\n.qr-popup {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tz-index: 1000;\r\n}\r\n\r\n.qr-popup-content {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 20rpx;\r\n\twidth: 600rpx;\r\n\tmax-height: 80vh;\r\n\toverflow: hidden;\r\n}\r\n\r\n.qr-popup-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 40rpx 40rpx 20rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.qr-popup-title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333333;\r\n}\r\n\r\n.qr-popup-close {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #f5f5f5;\r\n}\r\n\r\n.close-icon {\r\n\tfont-size: 40rpx;\r\n\tcolor: #999999;\r\n\tline-height: 1;\r\n}\r\n\r\n.qr-popup-body {\r\n\tpadding: 40rpx;\r\n\ttext-align: center;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n\r\n.qr-code-image {\r\n\twidth: 400rpx;\r\n\theight: 400rpx;\r\n\tmargin: 0 auto 30rpx;\r\n\tborder-radius: 10rpx;\r\n}\r\n\r\n.qr-loading {\r\n\twidth: 400rpx;\r\n\theight: 400rpx;\r\n\tmargin: 0 auto 30rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tbackground-color: #f5f5f5;\r\n\tborder-radius: 10rpx;\r\n}\r\n\r\n.qr-loading text {\r\n\tcolor: #999999;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.qr-popup-desc {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666666;\r\n\tline-height: 1.5;\r\n}\r\n\r\n.qr-popup-footer {\r\n\tpadding: 20rpx 40rpx 40rpx;\r\n}\r\n\r\n.qr-save-btn {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\tbackground-color: #333333;\r\n\tcolor: #FFFFFF;\r\n\tfont-size: 32rpx;\r\n\tborder-radius: 40rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./record.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./record.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}