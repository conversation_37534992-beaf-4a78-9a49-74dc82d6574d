@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #F6F7F9;
}
.withdraw-detail-container {
  min-height: 100vh;
  background-color: #F6F7F9;
}
/* 内容区域 */
.content-area {
  padding: 40rpx 30rpx;
}
/* 金额显示区域 */
.amount-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 30rpx;
  text-align: center;
}
.amount-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #3B99FC;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 40rpx;
}
.currency-symbol {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: bold;
}
.amount-display {
  margin-bottom: 20rpx;
}
.amount-value {
  font-size: 72rpx;
  color: #333333;
  font-weight: bold;
}
.status-text {
  font-size: 28rpx;
  color: #999999;
}
/* 详情信息 */
.detail-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
}
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F5F5F5;
}
.detail-item:last-child {
  border-bottom: none;
}
.detail-label {
  font-size: 32rpx;
  color: #666666;
}
.detail-value {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}
/* 订单进度 */
.progress-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
}
.progress-title {
  font-size: 36rpx;
  color: #333333;
  font-weight: bold;
  display: block;
  margin-bottom: 40rpx;
}
.progress-list {
  position: relative;
}
.progress-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
  position: relative;
}
.progress-item:last-child {
  margin-bottom: 0;
}
.progress-item:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 27rpx;
  top: 56rpx;
  width: 2rpx;
  height: 80rpx;
  background-color: #E5E5E5;
}
.progress-icon {
  margin-right: 30rpx;
  position: relative;
  z-index: 1;
}
.icon-circle {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-circle.completed {
  background-color: #3B99FC;
}
.icon-circle.waiting {
  background-color: #E5E5E5;
  position: relative;
}
.waiting-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #999999;
  border-radius: 50%;
}
.progress-content {
  flex: 1;
  padding-top: 8rpx;
}
.progress-step {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}
.progress-time {
  font-size: 24rpx;
  color: #999999;
  display: block;
  margin-bottom: 4rpx;
}
.progress-desc {
  font-size: 24rpx;
  color: #999999;
  display: block;
}
.progress-item.completed .progress-step {
  color: #333333;
}
.progress-item.waiting .progress-step {
  color: #999999;
}
