<template>
	<view class="share-earn-container">
		<!-- 背景图片 -->
		<image class="background-image" src="/subpkg-activity/static/images/one.png" mode="aspectFill"></image>

		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="navbarStyle">
			<!-- 状态栏占位 -->
			<view :style="{ height: statusBarHeight + 'px' }"></view>
			<!-- 导航栏内容区 -->
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<uni-icons type="left" size="20" color="#ffffff"></uni-icons>
				</view>
				<text class="page-title">活动详情</text>
				<view class="navbar-right">
					<view class="more-btn" @click="showMore">
						<view class="dot"></view>
						<view class="dot"></view>
						<view class="dot"></view>
					</view>
					<view class="target-btn" @click="showTarget">
						<uni-icons type="loop" size="16" color="#ffffff"></uni-icons>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 浮动规则按钮 -->
		<view class="floating-rule-btn" @click="showRules">
			<text class="rule-text">规则</text>
		</view>
		
		<!-- 内容区域 -->
		<view class="content-area">
			<!-- 邀请注册和操作步骤合并模块 -->
			<view class="invite-steps-section">
				<!-- 邀请注册模块 -->
				<view class="invite-module">
					<view class="section-title-with-bar">
						<view class="title-bar"></view>
						<text class="section-title">邀请注册</text>
					</view>
					<view class="invite-reward">
						<text class="reward-label">您将获得奖励</text>
						<view class="reward-info">
							<text style="margin-right: 10rpx;">提现</text>
							<text class="reward-amount">0.2</text>
							<text class="reward-unit">元/单</text>
						</view>
					</view>
				</view>
				
				<!-- 操作步骤模块 -->
				<view class="steps-module">
					<view class="section-title-with-bar">
						<view class="title-bar"></view>
						<text class="section-title">操作步骤</text>
					</view>
					<view class="steps-grid">
						<view class="step-item">
							<image class="step-icon" src="/static/images/img1.png"></image>
							<view class="step-text-container">
								<text class="step-number">01</text>
								<text class="step-text">分享海报</text>
							</view>
						</view>
						<view class="step-item">
							<image class="step-icon" src="/static/images/img2.png"></image>
							<view class="step-text-container">
								<text class="step-number">02</text>
								<text class="step-text">好友注册</text>
							</view>
						</view>
						<view class="step-item">
							<image class="step-icon" src="/static/images/img3.png"></image>
							<view class="step-text-container">
								<text class="step-number">03</text>
								<text class="step-text">完成订单获得奖励</text>
							</view>
						</view>
					</view>
					<view class="generate-poster-btn" @click="generatePoster">
						<text class="poster-btn-text">生 成 海 报</text>
					</view>
				</view>
			</view>
			
			<!-- 当前收益模块 -->
			<view class="earnings-section">
				<view class="section-title-with-bar">
					<view class="title-bar"></view>
					<text class="section-title">当前收益</text>
				</view>
				<view class="earnings-content">
					<view class="earnings-item">
						<text class="earnings-amount">{{shareData.income}}<text class="earnings-unit">元</text></text>
						<text class="earnings-label">可提现金额</text>
						<text class="withdraw-btn" @click="withdraw">去提现</text>
					</view>
					<view class="earnings-item">
						<text class="earnings-amount">{{shareData.shareNumber}}<text class="earnings-unit">人</text></text>
						<text class="earnings-label">总邀请人数</text>
					</view>
				</view>
			</view>
			
			<!-- 邀请记录和奖励记录 -->
			<view class="records-section">
				<view class="tab-header">
					<view class="tab-item" :class="{ active: activeTab === 0 }" @click="switchTab(0)">
						<text class="tab-text">邀请记录</text>
					</view>
					<view class="tab-item" :class="{ active: activeTab === 1 }" @click="switchTab(1)">
						<text class="tab-text">奖励记录</text>
					</view>
				</view>
				<view class="tab-content">
					<view v-show="activeTab === 0" class="record-list">
						<view class="record-item" v-for="(item, index) in inviteRecords" :key="index">
							<image class="record-avatar" src="/static/images/user.png"></image>
							<view class="record-info">
								<text class="record-name">{{item.name}}</text>
								<text class="record-time">{{item.time}}</text>
							</view>
						</view>
						<!-- 空状态提示 -->
						<view v-if="inviteRecords.length === 0" class="empty-state">
							<text class="empty-text">暂无邀请记录</text>
						</view>
					</view>
					<view v-show="activeTab === 1" class="record-list">
						<view class="record-item" v-for="(item, index) in rewardRecords" :key="index">
							<image class="record-avatar" src="/static/images/user.png"></image>
							<view class="record-info">
								<text class="record-name">{{item.name}}</text>
								<text class="record-time">{{item.time}}</text>
							</view>
							<view class="record-reward">
								<text class="reward-label">提现：</text>
								<text class="reward-amount">+¥{{item.amount || '0.2'}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 海报生成弹窗 -->
		<view v-if="showPosterModal" class="poster-modal" @click="closePosterModal">
			<!-- 海报画布区域 -->
			<view class="poster-canvas-container" @click.stop>
				<canvas
					canvas-id="posterCanvas"
					class="poster-canvas"
					:style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
				></canvas>
				<view v-if="!posterGenerated" class="poster-loading">
					<text>正在生成海报...</text>
				</view>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view v-if="showPosterModal" class="poster-bottom-actions">
			<view class="action-btn" @click="savePosterLocal">
				<image class="action-btn-icon" src="/subpkg-activity/static/images/haibao1.png"></image>
				<text class="action-btn-text">保存本地</text>
			</view>
			<!-- <view class="action-btn" @click="shareToWechat">
				<image class="action-btn-icon" src="/subpkg-activity/static/images/haibao2.png"></image>
				<text class="action-btn-text">微信好友</text>
			</view>
			<view class="action-btn" @click="shareToMoments">
				<image class="action-btn-icon" src="/subpkg-activity/static/images/haibao3.png"></image>
				<text class="action-btn-text">朋友圈</text>
			</view> -->
		</view>

		<!-- 取消按钮 -->
		<view v-if="showPosterModal" class="poster-cancel-btn" @click="closePosterModal">
			<text class="cancel-text">取消</text>
		</view>
	</view>
</template>

<script>
	import auth from '../../utils/auth.js';
	import request from '../../utils/request.js';

	export default {
		data() {
			return {
				statusBarHeight: 20,
				navbarHeight: 80,
				activeTab: 0, // 0: 邀请记录, 1: 奖励记录
				showPosterModal: false, // 控制海报弹窗显示
				posterGenerated: false, // 海报是否生成完成
				canvasWidth: 300, // 画布宽度 (将动态计算)
				canvasHeight: 500, // 画布高度 (将动态计算)
				posterImagePath: '', // 生成的海报图片路径
				screenWidth: 375, // 屏幕宽度 (默认值)
				pixelRatio: 1, // 设备像素比
				qrCodePath: '', // 小程序码图片路径
				shareData: {
					income: 0.0, // 可提现金额
					shareNumber: 0 // 总邀请人数
				},
				activityRules: '', // 活动规则内容
				inviteRecords: [], // 邀请记录，从接口获取
				rewardRecords: [] // 奖励记录，从接口获取
			}
		},
		computed: {
			navbarStyle() {
				return {
					height: this.navbarHeight + 'px',
					paddingTop: this.statusBarHeight + 'px'
				}
			}
		},
		onLoad() {
			this.setNavbarInfo();
			this.initCanvasSize();
			this.getShareData();
			this.getInviteRecords();
			this.getRewardRecords();
			this.getActivityRules();
		},
		onShow() {
			// 每次页面显示时刷新数据，包括从其他页面返回
			this.getShareData();
			this.getInviteRecords();
			this.getRewardRecords();
		},
		methods: {
			// 设置导航栏信息
			setNavbarInfo() {
				try {
					const systemInfo = uni.getSystemInfoSync();
					this.statusBarHeight = systemInfo.statusBarHeight;

					const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
					const menuTop = menuButtonInfo.top;
					const menuHeight = menuButtonInfo.height;

					const contentHeight = menuHeight + (menuTop - this.statusBarHeight) * 2;
					this.navbarHeight = this.statusBarHeight + contentHeight;
				} catch (e) {
					console.error('获取系统信息失败:', e);
					this.statusBarHeight = 20;
					this.navbarHeight = 60;
				}
			},

			// 初始化画布尺寸 - 根据屏幕尺寸动态计算
			initCanvasSize() {
				try {
					const systemInfo = uni.getSystemInfoSync();
					this.screenWidth = systemInfo.screenWidth;
					this.pixelRatio = systemInfo.pixelRatio;

					// 计算画布宽度：屏幕宽度的80%，但不超过400px，不小于320px
					const maxWidth = 400; // 增加最大宽度
					const minWidth = 320; // 增加最小宽度
					let calculatedWidth = this.screenWidth * 0.8; // 增加屏幕占比

					if (calculatedWidth > maxWidth) {
						calculatedWidth = maxWidth;
					} else if (calculatedWidth < minWidth) {
						calculatedWidth = minWidth;
					}

					// 考虑设备像素比，确保在高DPI屏幕上显示清晰
					this.canvasWidth = Math.floor(calculatedWidth * this.pixelRatio) / this.pixelRatio;

					// 根据宽度按比例计算高度 (宽高比约为 2:3.5，增加高度)
					this.canvasHeight = Math.floor(this.canvasWidth * 1.8);

					console.log('画布尺寸:', this.canvasWidth, 'x', this.canvasHeight);
					console.log('屏幕信息:', this.screenWidth, 'x', systemInfo.screenHeight, '像素比:', this.pixelRatio);
				} catch (e) {
					console.error('获取屏幕信息失败:', e);
					// 使用默认尺寸
					this.canvasWidth = 300;
					this.canvasHeight = 500;
				}
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 获取分享数据
			getShareData() {
				request.get('/app/share').then(res => {
					if (res && res.code === 200 && res.data) {
						this.shareData.income = res.data.income || 0.0;
						this.shareData.shareNumber = res.data.shareNumber || 0;
						console.log('分享数据获取成功:', this.shareData);
					}
				}).catch(err => {
					console.error('获取分享数据失败:', err);

					// 检查是否是500错误
					if (err && err.code === 500) {
						console.log('接口返回500错误，返回上一页');
						// 延时后关闭当前页面
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							});
						}, 1000);
						return;
					}

					uni.showToast({
						title: '获取数据失败',
						icon: 'none'
					});
				});
			},

			// 获取邀请记录
			getInviteRecords() {
				request.get('/app/share/user/list').then(res => {
					if (res && res.code === 200 && res.data) {
						// 处理邀请记录数据
						this.inviteRecords = res.data.map(item => ({
							name: item.nickName || '微信用户',
							time: item.createTime || '',
							avatar: item.avatar || '/static/images/avatar.png'
						}));
						console.log('邀请记录获取成功:', this.inviteRecords);
					}
				}).catch(err => {
					console.error('获取邀请记录失败:', err);
					uni.showToast({
						title: '获取邀请记录失败',
						icon: 'none'
					});
				});
			},

			// 获取奖励记录
			getRewardRecords() {
				request.get('/app/share/list').then(res => {
					if (res && res.code === 200 && res.rows) {
						// 处理奖励记录数据
						this.rewardRecords = res.rows.map(item => ({
							name: item.name || '用户',
							time: item.createTime || '',
							avatar: '/static/images/avatar.png', // 使用默认头像
							amount: item.orderAmout || 0
						}));
						console.log('奖励记录获取成功:', this.rewardRecords);
					}
				}).catch(err => {
					console.error('获取奖励记录失败:', err);
					uni.showToast({
						title: '获取奖励记录失败',
						icon: 'none'
					});
				});
			},

			// 获取活动规则
			getActivityRules() {
				request.get('/app/configure/activity_rules').then(res => {
					if (res && res.code === 200) {
						this.activityRules = res.msg || '';
						console.log('活动规则获取成功:', this.activityRules);
					}
				}).catch(err => {
					console.error('获取活动规则失败:', err);
					// 使用默认规则内容
					this.activityRules = '邀请好友注册成功后，好友下单即可获得奖励\n每邀请一位好友最多可获得0.2元奖励\n奖励将在好友完成首单后发放';
				});
			},
			
			// 显示更多菜单
			showMore() {
				uni.showActionSheet({
					itemList: ['分享给朋友', '收藏'],
					success: (res) => {
						if (res.tapIndex === 0) {
							// 分享给朋友
							this.shareToFriend();
						} else if (res.tapIndex === 1) {
							// 收藏
							this.collectActivity();
						}
					}
				});
			},
			
			// 显示目标
			showTarget() {
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},
			
			// 显示规则
			showRules() {
				const content = this.activityRules || '1. 邀请好友注册成功后，好友下单即可获得奖励\n2. 每邀请一位好友最多可获得0.2元奖励\n3. 奖励将在好友完成首单后发放\n4. 活动最终解释权归平台所有';
				uni.showModal({
					title: '活动规则',
					content: content,
					showCancel: false
				});
			},
			
			// 生成海报
			generatePoster() {
				// 重新计算画布尺寸，确保适配当前屏幕
				this.initCanvasSize();

				this.showPosterModal = true;
				this.posterGenerated = false;

				// 先生成小程序码，再绘制海报
				this.generateQRCode().then(() => {
					// 延迟一下再开始绘制，确保DOM已渲染
					this.$nextTick(() => {
						this.drawPoster();
					});
				}).catch(() => {
					// 如果生成小程序码失败，使用默认图片
					this.qrCodePath = '/static/images/avatar.png';
					this.$nextTick(() => {
						this.drawPoster();
					});
				});
			},

			// 生成小程序码
			generateQRCode() {
				return new Promise((resolve, reject) => {
					// 获取当前用户ID
					const userId = auth.getUserId();
					console.log('userId', userId);
					if (!userId) {
						console.warn('用户未登录，无法生成专属小程序码');
						reject('用户未登录');
						return;
					}

					// 构建小程序码参数
					const scene = userId; // 场景值，直接使用用户ID（后端会处理为userId参数）
					const page = 'pages/login/login'; // 跳转到登录页

					// 调用后端接口生成小程序码
					request.post('/app/share/url', {
						scene: scene,
						page: page,
						"check_path": false
					}).then(res => {
						// 处理/app/share/url接口返回的base64数据
						if (res && res.code === 200 && res.msg) {
							// res.msg包含base64格式的图片数据
							const base64Data = res.msg;
							const tempFilePath = `${wx.env.USER_DATA_PATH}/qrcode_${Date.now()}.png`;

							// 写入临时文件
							wx.getFileSystemManager().writeFile({
								filePath: tempFilePath,
								data: base64Data,
								encoding: 'base64',
								success: () => {
									this.qrCodePath = tempFilePath;
									console.log('小程序码生成成功:', tempFilePath);
									resolve();
								},
								fail: (err) => {
									console.error('保存小程序码失败:', err);
									reject(err);
								}
							});
						} else {
							console.error('小程序码数据格式错误:', res);
							reject('小程序码数据格式错误');
						}
					}).catch(err => {
						console.error('生成小程序码失败:', err);
						reject(err);
					});
				});
			},

			// 备用方案：使用微信小程序原生API生成小程序码（仅在开发环境或特定条件下使用）
			generateQRCodeFallback() {
				return new Promise((resolve, reject) => {
					// 获取当前用户ID
					const userId = auth.getUserId();
					if (!userId) {
						reject('用户未登录');
						return;
					}

					// 注意：这个方法需要在真实的微信小程序环境中才能使用
					// 并且需要后端配合生成小程序码
					console.log('使用备用方案生成小程序码，用户ID:', userId);

					// 这里可以实现其他备用方案，比如：
					// 1. 生成普通二维码包含小程序链接
					// 2. 使用第三方二维码生成服务
					// 3. 预设的静态小程序码

					// 暂时使用默认图片
					this.qrCodePath = '/static/images/avatar.png';
					resolve();
				});
			},

			// 绘制海报
			drawPoster() {
				const ctx = uni.createCanvasContext('posterCanvas', this);

				// 绘制整体海报圆角背景
				const cornerRadius = 20;

				// 设置画布背景色为蓝色渐变背景
				const gradient = ctx.createLinearGradient(0, 0, 0, this.canvasHeight);
				gradient.addColorStop(0, '#096ff8');
				gradient.addColorStop(1, '#096ff8');
				ctx.setFillStyle(gradient);

				// 绘制圆角背景
				this.fillRoundedRect(ctx, 0, 0, this.canvasWidth, this.canvasHeight, cornerRadius);

				// 绘制顶部图片 (邀好友得好礼的完整图片) - 按比例计算高度
				const headerHeight = Math.floor(this.canvasHeight * 0.35); // 高度占画布的35%
				ctx.drawImage('/subpkg-activity/static/images/hbheader.png', 0, 0, this.canvasWidth, headerHeight);

				// 绘制二维码容器 (白色圆角背景) - 增加宽度到80%和高度
				const qrContainerWidth = this.canvasWidth * 0.85; // 宽度85%
				const qrContainerHeight = Math.floor(this.canvasHeight * 0.45); // 高度占画布的35%
				const qrContainerX = (this.canvasWidth - qrContainerWidth) / 2;
				const qrContainerY = headerHeight + Math.floor(this.canvasHeight * 0.04); // 间距占画布的4%

				// 计算白色背景区域的实际位置和尺寸
				const whiteContainerY = qrContainerY - Math.floor(this.canvasHeight * 0.12);

				// 绘制二维码容器白色圆角背景
				ctx.setFillStyle('#ffffff');
				this.fillRoundedRect(ctx, qrContainerX, whiteContainerY, qrContainerWidth, qrContainerHeight, 15);

				// 绘制二维码 - 占白色背景区域的80%
				const qrSize = Math.floor(Math.min(qrContainerWidth, qrContainerHeight) * 0.8); // 二维码占白色背景区域的80%
				const qrX = qrContainerX + (qrContainerWidth - qrSize) / 2; // 水平居中
				const qrY = whiteContainerY + (qrContainerHeight - qrSize) / 2 - Math.floor(qrContainerHeight * 0.05); // 垂直居中，上移为文字留空间

				// 绘制小程序码
				const qrImagePath = this.qrCodePath || '/subpkg-activity/static/images/haibao1.png'; // 使用生成的小程序码或默认图片
				ctx.drawImage(qrImagePath, qrX, qrY, qrSize, qrSize);

				// 绘制二维码下方文字 "分享码" - 在白色背景区域的最底部，留出小距离
				ctx.setFillStyle('#000000'); // 使用黑色
				ctx.setFontSize(Math.floor(this.canvasWidth * 0.045));
				ctx.setTextAlign('center');
				const textY = whiteContainerY + qrContainerHeight - Math.floor(qrContainerHeight * 0.04); // 在白色背景底部留出小距离
				ctx.fillText('分享码', this.canvasWidth / 2, textY);

				// 绘制活动规则容器 - 增加圆角，按比例计算尺寸
				const rulesContainerY = qrContainerY + qrContainerHeight - Math.floor(this.canvasHeight * 0.08);
				const rulesContainerHeight = Math.floor(this.canvasHeight * 0.22); // 高度占画布的22%
				const rulesContainerWidth = this.canvasWidth * 0.9; // 与二维码容器同宽
				const rulesContainerX = (this.canvasWidth - rulesContainerWidth) / 2;

				// 绘制活动规则白色圆角背景
				ctx.setFillStyle('#ffffff');
				this.fillRoundedRect(ctx, rulesContainerX, rulesContainerY, rulesContainerWidth, rulesContainerHeight, 15);

				// 绘制活动规则标题 - 按比例计算字体大小和位置
				ctx.setFillStyle('#000000'); // 使用黑色
				ctx.setFontSize(Math.floor(this.canvasWidth * 0.055)); // 字体大小为画布宽度的5.5%
				ctx.setTextAlign('center');
				ctx.setTextBaseline('top'); // 改为顶部对齐
				const titleY = rulesContainerY + Math.floor(rulesContainerHeight * 0.1); // 标题位置：容器顶部10%
				ctx.fillText('活动规则', this.canvasWidth / 2, titleY);

				// 绘制活动规则内容 - 按比例计算字体大小和位置，支持自动换行
				ctx.setFillStyle('#666666');
				ctx.setFontSize(Math.floor(this.canvasWidth * 0.045)); // 稍微减小字体大小，确保文字能完整显示
				ctx.setTextAlign('left'); // 改为左对齐，便于换行显示
				ctx.setTextBaseline('top'); // 改为顶部对齐

				// 计算文字区域的参数
				const textMaxWidth = rulesContainerWidth * 0.9; // 白色背景宽度的90%
				const textStartX = rulesContainerX + (rulesContainerWidth - textMaxWidth) / 2; // 文字区域左边界
				const lineHeight = Math.floor(this.canvasWidth * 0.045); // 行高
				const titleHeight = Math.floor(this.canvasWidth * 0.055 * 1.2); // 标题高度（字体大小 + 间距）
				const textStartY = titleY + titleHeight + Math.floor(rulesContainerHeight * 0.05); // 内容开始位置：标题下方 + 5%间距

				// 处理活动规则文本，支持自动换行
				let currentY = textStartY;
				if (this.activityRules) {
					// 按换行符分割文本
					const paragraphs = this.activityRules.split('\n').filter(line => line.trim());

					paragraphs.forEach((paragraph) => {
						if (paragraph.trim()) {
							// 为每个段落绘制自动换行文字
							const linesDrawn = this.drawMultilineText(ctx, paragraph.trim(), textStartX, currentY, textMaxWidth, lineHeight);
							currentY += linesDrawn * lineHeight + Math.floor(lineHeight * 0.3); // 段落间距
						}
					});
				} else {
					// 默认规则内容
					const defaultRules = [
						'邀请好友注册成功后，好友下单即可获得奖励',
						'奖励将在好友完成首单后发放'
					];

					defaultRules.forEach((rule) => {
						const linesDrawn = this.drawMultilineText(ctx, rule, textStartX, currentY, textMaxWidth, lineHeight);
						currentY += linesDrawn * lineHeight + Math.floor(lineHeight * 0.3); // 段落间距
					});
				}

				// 重置文字基线，确保后续绘制正常
				ctx.setTextBaseline('alphabetic');

				ctx.draw(false, () => {
					this.posterGenerated = true;
					// 生成图片
					uni.canvasToTempFilePath({
						canvasId: 'posterCanvas',
						success: (res) => {
							this.posterImagePath = res.tempFilePath;
						}
					}, this);
				});
			},

			// 绘制圆角矩形路径
			drawRoundedRect(ctx, x, y, width, height, radius) {
				ctx.beginPath();
				ctx.moveTo(x + radius, y);
				ctx.lineTo(x + width - radius, y);
				ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
				ctx.lineTo(x + width, y + height - radius);
				ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
				ctx.lineTo(x + radius, y + height);
				ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
				ctx.lineTo(x, y + radius);
				ctx.quadraticCurveTo(x, y, x + radius, y);
				ctx.closePath();
			},

			// 填充圆角矩形
			fillRoundedRect(ctx, x, y, width, height, radius) {
				this.drawRoundedRect(ctx, x, y, width, height, radius);
				ctx.fill();
			},

			// 文字自动换行方法
			wrapText(ctx, text, maxWidth) {
				const words = text.split('');
				const lines = [];
				let currentLine = '';

				for (let i = 0; i < words.length; i++) {
					const testLine = currentLine + words[i];
					const metrics = ctx.measureText(testLine);
					const testWidth = metrics.width;

					if (testWidth > maxWidth && currentLine !== '') {
						lines.push(currentLine);
						currentLine = words[i];
					} else {
						currentLine = testLine;
					}
				}

				if (currentLine !== '') {
					lines.push(currentLine);
				}

				return lines;
			},

			// 绘制多行文字
			drawMultilineText(ctx, text, x, y, maxWidth, lineHeight) {
				const lines = this.wrapText(ctx, text, maxWidth);

				lines.forEach((line, index) => {
					ctx.fillText(line, x, y + (index * lineHeight));
				});

				return lines.length; // 返回行数
			},

			// 关闭海报弹窗
			closePosterModal() {
				this.showPosterModal = false;
				this.posterGenerated = false;
			},

			// 保存到本地
			savePosterLocal() {
				if (!this.posterImagePath) {
					uni.showToast({
						title: '海报还未生成完成',
						icon: 'none'
					});
					return;
				}

				uni.saveImageToPhotosAlbum({
					filePath: this.posterImagePath,
					success: () => {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						});
					},
					fail: () => {
						uni.showToast({
							title: '保存失败',
							icon: 'none'
						});
					}
				});
			},

			// 分享到微信好友
			shareToWechat() {
				if (!this.posterImagePath) {
					uni.showToast({
						title: '海报还未生成完成',
						icon: 'none'
					});
					return;
				}

				uni.showToast({
					title: '分享到微信好友',
					icon: 'none'
				});
			},

			// 分享到朋友圈
			shareToMoments() {
				if (!this.posterImagePath) {
					uni.showToast({
						title: '海报还未生成完成',
						icon: 'none'
					});
					return;
				}

				uni.showToast({
					title: '分享到朋友圈',
					icon: 'none'
				});
			},
			
			// 提现
			withdraw() {
				// 跳转到提现页面，传递可提现金额
				uni.navigateTo({
					url: `/subpkg-activity/withdraw/withdraw?amount=${this.shareData.income}`
				});
			},
			
			// 切换标签
			switchTab(index) {
				this.activeTab = index;
			},
			
			// 分享给朋友
			shareToFriend() {
				uni.share({
					provider: "weixin",
					scene: "WXSceneSession",
					type: 0,
					href: "https://your-domain.com/share",
					title: "邀请好友，得好礼",
					summary: "邀请好友注册成功后，好友下单即得现金",
					imageUrl: "/static/images/share_poster.png"
				});
			},
			
			// 收藏活动
			collectActivity() {
				uni.showToast({
					title: '收藏成功',
					icon: 'success'
				});
			},
			
			// 获取图片信息，用于计算图片比例
			getImageInfo(src) {
				// 默认尺寸，防止图片加载失败时的异常
				let imgInfo = {
					width: 280,
					height: 140
				};
				
				// 同步获取图片信息（注意：真实场景中最好使用异步方式）
				try {
					const res = uni.getImageInfoSync(src);
					if (res) {
						imgInfo.width = res.width;
						imgInfo.height = res.height;
					}
				} catch (e) {
					console.error('获取图片信息失败:', e);
				}
				
				return imgInfo;
			}
		}
	}
</script>

<style lang="scss">
page {
	min-height: 100vh;
	background-color: #F6F7F9;
}

.share-earn-container {
	min-height: 100vh;
	position: relative;
}

/* 背景图片样式 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 490rpx;
}

/* 自定义导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 999;
	display: flex;
	flex-direction: column;
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 44px;
	padding: 0 30rpx;
}

.navbar-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.page-title {
	color: #ffffff;
	font-size: 36rpx;
	font-weight: bold;
}

.navbar-right {
	display: flex;
	align-items: center;
	width: 120rpx;
	justify-content: space-between;
}

.more-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 40rpx;
	height: 40rpx;
}

.dot {
	width: 6rpx;
	height: 6rpx;
	background-color: #ffffff;
	border-radius: 50%;
	margin: 2rpx 0;
}

.target-btn {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 浮动规则按钮 */
.floating-rule-btn {
	position: fixed;
	right: 0;
	top: 300rpx;
	width: 80rpx;
	height: 60rpx;
	background-color: rgba(255, 255, 255);
	border-radius: 20rpx 0 0 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 998;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.rule-text {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

/* 内容区域 */
.content-area {
	width: 100%;
	box-sizing: border-box;
	padding: 0 30rpx;
	padding-top: 465rpx; /* 为背景图片留出空间 */
	background-color: #1476F9;
}

/* 邀请注册和操作步骤合并模块 */
.invite-steps-section {
	background: linear-gradient(180deg, #FFFFFF 0%, #D2EAFF 100%);
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 20rpx;
	margin-top: -50rpx; /* 覆盖背景图片底部一小部分 */
	position: relative;
	z-index: 1;
}

/* 带左侧蓝色柱状条的标题 */
.section-title-with-bar {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.title-bar {
	width: 6rpx;
	height: 32rpx;
	background-color: #3B99FC;
	border-radius: 3rpx;
	margin-right: 16rpx;
}

.section-title {
	font-size: 36rpx;
	color: #000000;
	font-weight: bold;
}

/* 邀请注册模块 */
.invite-module {
	margin-bottom: 40rpx;
}

.invite-reward {
	display: flex;
	align-items: baseline;
	justify-content: space-between;
	background-color: #D4EBFF;
	padding: 20rpx 30rpx;
	border-radius: 16rpx;
}

.reward-label {
	font-size: 28rpx;
	color: #000000;
}

.reward-info {
	display: flex;
	align-items: baseline;
}

.reward-amount {
	font-size: 48rpx;
	color: #FF4757;
	font-weight: bold;
}

.reward-unit {
	font-size: 24rpx;
	color: #909399;
	font-weight: normal;
	margin-left: 4rpx;
}

/* 操作步骤模块 */
.steps-module {
	/* 操作步骤模块样式 */
}

.steps-grid {
	display: flex;
	justify-content: space-between;
	margin-bottom: 40rpx;
}

.step-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 33.33%;
}

.step-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	margin-bottom: 16rpx;
}

.step-text-container {
	display: flex;
	align-items: center;
	flex-direction: row;
}

.step-number {
	font-size: 24rpx;
	color: #999999;
	margin-right: 8rpx;
}

.step-text {
	font-size: 24rpx;
	color: #606266;
	text-align: center;
	line-height: 1.4;
	width: 100rpx;
}

.generate-poster-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.poster-btn-text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: bold;
	letter-spacing: 8rpx; /* 字符间距 */
}

/* 当前收益模块 */
.earnings-section {
	background: linear-gradient(180deg, #FFFFFF 0%, #D2EAFF 100%);
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 20rpx;
}

.earnings-content {
	display: flex;
	justify-content: space-around;
}

.earnings-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
}

.earnings-amount {
	font-size: 48rpx;
	color: #FF4757;
	font-weight: bold;
	line-height: 1;
	margin-bottom: 16rpx;
}

.earnings-unit {
	font-size: 24rpx;
	color: #FF4757;
	font-weight: normal;
}

.earnings-label {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 16rpx;
}

.withdraw-btn {
	font-size: 24rpx;
	color: #4A90E2;
	padding: 8rpx 16rpx;
	border: 1rpx solid #4A90E2;
	border-radius: 20rpx;
}

/* 邀请记录和奖励记录 */
.records-section {
	background-color: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;
	margin-bottom: 410rpx;
}

.tab-header {
	display: flex;
	background-color: #ffffff;
	margin-bottom: 20rpx;
}

.tab-item {
	flex: 1;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.tab-item.active .tab-text {
	color: #000000;
	font-weight: bold;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 6rpx;
	background-color: #4A90E2;
	border-radius: 3rpx;
}

.tab-text {
	font-size: 32rpx;
	color: #666666;
}

.tab-content {
	min-height: 400rpx;
}

.record-list {
	padding: 0 30rpx;
}

.record-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 20rpx;
	background: #D4EBFF;
	border-radius: 8rpx 8rpx 8rpx 8rpx;
	margin-bottom: 20rpx;
}

.record-item:last-child {
	border-bottom: none;
}

.record-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 24rpx;
}

.record-info {
	display: flex;
	flex-direction: column;
	flex: 1;
}

.record-name {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.record-time {
	font-size: 24rpx;
	color: #999999;
}

/* 奖励金额样式 */
.record-reward {
	display: flex;
	align-items: center;
	text-align: right;
}

.reward-label {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.reward-amount {
	font-size: 28rpx;
	color: #FF4757;
	font-weight: bold;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 80rpx 0;
}

.empty-text {
	font-size: 28rpx;
	color: #999999;
}

/* 海报弹窗样式 */
.poster-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 移除不需要的弹窗内容样式 */

.poster-canvas-container {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 300rpx;
	padding: 40rpx 20rpx;
	min-height: 60vh;
}

.poster-canvas {
	border-radius: 20rpx;
	overflow: hidden;
}

.poster-loading {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background-color: rgba(255, 255, 255, 0.9);
	padding: 20rpx 40rpx;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #666666;
}

/* 移除活动规则相关样式 */

/* 底部操作按钮样式 */
.poster-bottom-actions {
	position: fixed;
	bottom: 100rpx;
	left: 0;
	width: 100%;
	background-color: #ffffff;
	padding: 20rpx 0;
	display: flex;
	justify-content: space-around;
	z-index: 10000;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	transition: all 0.3s ease;
	flex: 1;
}

.action-btn:active {
	background-color: rgba(0, 0, 0, 0.05);
}

.action-btn-icon {
	width: 100rpx;
	height: 100rpx;
	margin-bottom: 20rpx;
}

.action-btn-text {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

/* 取消按钮样式 */
.poster-cancel-btn {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #ffffff;
	padding: 40rpx 0;
	z-index: 10000;
	display: flex;
	justify-content: center;
	align-items: center;
}

.cancel-text {
	font-size: 36rpx;
	color: #333333;
	font-weight: 500;
}
</style>