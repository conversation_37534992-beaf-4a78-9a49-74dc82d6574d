/**
 * API接口测试文件
 * 用于测试车票接口是否正常工作
 */

import { ticketApi } from './api.js';

// 测试获取车票列表接口
export const testTicketList = async () => {
  try {
    const params = {
      departureTime: '2024-01-15', // 测试日期
      upAddress: 1, // 上车地点ID
      downAddress: 2 // 下车地点ID
    };
    
    console.log('测试参数:', params);
    
    const response = await ticketApi.getTicketList(params);
    console.log('接口响应:', response);
    
    return response;
  } catch (error) {
    console.error('接口测试失败:', error);
    throw error;
  }
};

// 在控制台中可以调用这个函数进行测试
// testTicketList();
