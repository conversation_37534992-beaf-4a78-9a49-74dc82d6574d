(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/mine"],{64:function(n,t,e){"use strict";(function(n,t){var r=e(4);e(26);r(e(25));var a=r(e(65));n.__webpack_require_UNI_MP_PLUGIN__=e,t(a.default)}).call(this,e(1)["default"],e(2)["createPage"])},65:function(n,t,e){"use strict";e.r(t);var r=e(66),a=e(68);for(var o in a)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(o);e(70);var u,i=e(33),s=Object(i["default"])(a["default"],r["render"],r["staticRenderFns"],!1,null,null,null,!1,r["components"],u);s.options.__file="pages/mine/mine.vue",t["default"]=s.exports},66:function(n,t,e){"use strict";e.r(t);var r=e(67);e.d(t,"render",(function(){return r["render"]})),e.d(t,"staticRenderFns",(function(){return r["staticRenderFns"]})),e.d(t,"recyclableRender",(function(){return r["recyclableRender"]})),e.d(t,"components",(function(){return r["components"]}))},67:function(n,t,e){"use strict";var r;e.r(t),e.d(t,"render",(function(){return a})),e.d(t,"staticRenderFns",(function(){return u})),e.d(t,"recyclableRender",(function(){return o})),e.d(t,"components",(function(){return r}));var a=function(){var n=this,t=n.$createElement;n._self._c},o=!1,u=[];a._withStripped=!0},68:function(n,t,e){"use strict";e.r(t);var r=e(69),a=e.n(r);for(var o in r)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return r[n]}))}(o);t["default"]=a.a},69:function(n,t,e){"use strict";(function(n){var r=e(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(e(30)),o={data:function(){return{userInfo:{nickName:"未知用户",phone:"153****1696",avatar:"/static/images/user.png"},couponCount:5,statusBarHeight:20,navbarHeight:80,menuButtonInfo:{}}},computed:{navbarStyle:function(){return{height:this.navbarHeight+"px",paddingTop:this.statusBarHeight+"px"}}},onLoad:function(){this.setNavbarInfo()},onShow:function(){var n=a.default.getUserInfo();n?this.userInfo=n:a.default.navigateToLogin("/pages/mine/mine")},methods:{setNavbarInfo:function(){try{var t=n.getSystemInfoSync();this.statusBarHeight=t.statusBarHeight,this.menuButtonInfo=n.getMenuButtonBoundingClientRect();this.menuButtonInfo.bottom;var e=this.menuButtonInfo.top,r=this.menuButtonInfo.height,a=r+2*(e-this.statusBarHeight);this.navbarHeight=this.statusBarHeight+a,console.log("导航栏高度:",this.navbarHeight)}catch(o){console.error("获取系统信息失败:",o),this.statusBarHeight=20,this.navbarHeight=60}},goToUserProfile:function(){n.navigateTo({url:"/subpkg-user/user_profile/user_profile"})},shareToEarn:function(){n.navigateTo({url:"/subpkg-activity/share_earn/share_earn"})},managePassengers:function(){n.navigateTo({url:"/subpkg-user/passenger_management/passenger_management"})},goToCoupon:function(){n.navigateTo({url:"/subpkg-booking/coupon/coupon"})}}};t.default=o}).call(this,e(2)["default"])},70:function(n,t,e){"use strict";e.r(t);var r=e(71),a=e.n(r);for(var o in r)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return r[n]}))}(o);t["default"]=a.a},71:function(n,t,e){}},[[64,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mine/mine.js.map