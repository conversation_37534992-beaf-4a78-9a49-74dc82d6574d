/**
 * 权限管理工具
 * 处理登录拦截、token检查等
 */

// 需要登录的页面路径
const needLoginPages = [
  '/pages/mine/mine',
  '/subpkg-user/user_profile/user_profile',
  '/subpkg-user/passenger_edit/passenger_edit',
  '/subpkg-booking/ticket_confirm/ticket_confirm',
  '/pages/record/record',
  '/subpkg-booking/order_detail/order_detail',
  '/subpkg-booking/coupon/coupon'
];

/**
 * 检查是否需要登录
 * @param {String} path 页面路径
 * @returns {Boolean} 是否需要登录
 */
const checkNeedLogin = (path) => {
  return needLoginPages.some(item => path.startsWith(item));
};

/**
 * 检查是否已登录
 * @returns {Boolean} 是否已登录
 */
const isLoggedIn = () => {
  return !!uni.getStorageSync('token');
};

/**
 * 跳转到登录页
 * @param {String} redirectUrl 登录后跳转的页面
 */
const navigateToLogin = (redirectUrl) => {
  let url = '/pages/login/login';
  if (redirectUrl) {
    url += `?redirect=${encodeURIComponent(redirectUrl)}`;
  }
  uni.navigateTo({
    url
  });
};

/**
 * 路由拦截处理
 * 在App.vue的onShow中调用
 */
const routeInterceptor = () => {
  // 获取当前页面路由
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const currentPath = `/${currentPage.route}`;
  
  // 检查是否需要登录
  if (checkNeedLogin(currentPath)) {
    // 检查是否已登录
    if (!isLoggedIn()) {
      // 保存当前页面路径，登录后跳回
      navigateToLogin(currentPath);
      return false;
    }
  }
  return true;
};

/**
 * 获取用户信息
 * @returns {Object} 用户信息
 */
const getUserInfo = () => {
  const userInfo = uni.getStorageSync('userInfo');
  return userInfo ? JSON.parse(userInfo) : null;
};

/**
 * 保存用户信息
 * @param {Object} userInfo 用户信息
 */
const setUserInfo = (userInfo) => {
  uni.setStorageSync('userInfo', JSON.stringify(userInfo));
};

/**
 * 保存token
 * @param {String} token 用户token
 */
const setToken = (token) => {
  uni.setStorageSync('token', token);
};

/**
 * 获取token
 * @returns {String} 用户token
 */
const getToken = () => {
  return uni.getStorageSync('token');
};

/**
 * 获取用户ID
 * @returns {String} 用户ID
 */
const getUserId = () => {
  const userInfo = getUserInfo();
  return userInfo ? userInfo.userid : null;
};

/**
 * 清除登录信息
 */
const clearLoginInfo = () => {
  uni.removeStorageSync('token');
  uni.removeStorageSync('userInfo');
};

export default {
  checkNeedLogin,
  isLoggedIn,
  navigateToLogin,
  routeInterceptor,
  getUserInfo,
  setUserInfo,
  setToken,
  getToken,
  getUserId,
  clearLoginInfo
};