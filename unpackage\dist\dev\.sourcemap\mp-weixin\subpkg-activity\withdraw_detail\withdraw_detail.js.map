{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/subpkg-activity/withdraw_detail/withdraw_detail.vue?0e56", "webpack:///E:/购票系统/购票系统/subpkg-activity/withdraw_detail/withdraw_detail.vue?b51c", "webpack:///E:/购票系统/购票系统/subpkg-activity/withdraw_detail/withdraw_detail.vue?b503", "webpack:///E:/购票系统/购票系统/subpkg-activity/withdraw_detail/withdraw_detail.vue?45c7", "uni-app:///subpkg-activity/withdraw_detail/withdraw_detail.vue", "webpack:///E:/购票系统/购票系统/subpkg-activity/withdraw_detail/withdraw_detail.vue?79fa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniIcons", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "data", "withdrawAmount", "withdrawTime", "createTime", "onLoad", "methods", "formatAmount", "setCurrentTime"], "mappings": "0KAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,sDACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,yHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCjCvB,yHAAgrB,eAAG,G,yGC4DnrB,CACAC,gBACA,OACAC,yBACAC,kCACAC,kCAGAC,mBAEA,aAEA,2BACA,yCAIA,uBAEAC,SAEAC,yBACA,2BAIAC,0BACA,eACA,kBACA,yCACA,sCACA,uCACA,yCACA,yCAEA,qGACA,sGAGA,a,iCCnGA,yHAAuwC,eAAG,G", "file": "subpkg-activity/withdraw_detail/withdraw_detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg-activity/withdraw_detail/withdraw_detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./withdraw_detail.vue?vue&type=template&id=b54daf80&\"\nvar renderjs\nimport script from \"./withdraw_detail.vue?vue&type=script&lang=js&\"\nexport * from \"./withdraw_detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./withdraw_detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg-activity/withdraw_detail/withdraw_detail.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdraw_detail.vue?vue&type=template&id=b54daf80&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdraw_detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdraw_detail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"withdraw-detail-container\">\n\t\t<!-- 内容区域 -->\n\t\t<view class=\"content-area\">\n\t\t\t<!-- 金额显示区域 -->\n\t\t\t<view class=\"amount-section\">\n\t\t\t\t<view class=\"amount-icon\">\n\t\t\t\t\t<text class=\"currency-symbol\">¥</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"amount-display\">\n\t\t\t\t\t<text class=\"amount-value\">- {{withdrawAmount}}</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"status-text\">进行中</text>\n\t\t\t</view>\n\n\t\t\t<!-- 详情信息 -->\n\t\t\t<view class=\"detail-section\">\n\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t<text class=\"detail-label\">时间</text>\n\t\t\t\t\t<text class=\"detail-value\">{{withdrawTime}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t<text class=\"detail-label\">提现位置</text>\n\t\t\t\t\t<text class=\"detail-value\">微信零钱</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 订单进度 -->\n\t\t\t<view class=\"progress-section\">\n\t\t\t\t<text class=\"progress-title\">订单进度</text>\n\t\t\t\t<view class=\"progress-list\">\n\t\t\t\t\t<view class=\"progress-item completed\">\n\t\t\t\t\t\t<view class=\"progress-icon\">\n\t\t\t\t\t\t\t<view class=\"icon-circle completed\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#ffffff\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"progress-content\">\n\t\t\t\t\t\t\t<text class=\"progress-step\">创建充值订单</text>\n\t\t\t\t\t\t\t<text class=\"progress-time\">更新于{{createTime}}</text>\n\t\t\t\t\t\t\t<text class=\"progress-desc\">此过程可能需要1小时</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"progress-item waiting\">\n\t\t\t\t\t\t<view class=\"progress-icon\">\n\t\t\t\t\t\t\t<view class=\"icon-circle waiting\">\n\t\t\t\t\t\t\t\t<view class=\"waiting-dot\"></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"progress-content\">\n\t\t\t\t\t\t\t<text class=\"progress-step\">等待中...</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\twithdrawAmount: '100,000', // 提现金额\n\t\t\t\twithdrawTime: '2024-12-8 18:43:25', // 提现时间\n\t\t\t\tcreateTime: '2023-12-8 18:43:25', // 创建时间\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 从上一页获取提现金额\n\t\t\tif (options.amount) {\n\t\t\t\t// 格式化金额显示，添加千分位分隔符\n\t\t\t\tconst amount = parseFloat(options.amount);\n\t\t\t\tthis.withdrawAmount = this.formatAmount(amount);\n\t\t\t}\n\n\t\t\t// 设置当前时间\n\t\t\tthis.setCurrentTime();\n\t\t},\n\t\tmethods: {\n\t\t\t// 格式化金额，添加千分位分隔符\n\t\t\tformatAmount(amount) {\n\t\t\t\treturn amount.toLocaleString();\n\t\t\t},\n\n\t\t\t// 设置当前时间\n\t\t\tsetCurrentTime() {\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst year = now.getFullYear();\n\t\t\t\tconst month = String(now.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(now.getDate()).padStart(2, '0');\n\t\t\t\tconst hours = String(now.getHours()).padStart(2, '0');\n\t\t\t\tconst minutes = String(now.getMinutes()).padStart(2, '0');\n\t\t\t\tconst seconds = String(now.getSeconds()).padStart(2, '0');\n\n\t\t\t\tthis.withdrawTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n\t\t\t\tthis.createTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\npage {\n\tbackground-color: #F6F7F9;\n}\n\n.withdraw-detail-container {\n\tmin-height: 100vh;\n\tbackground-color: #F6F7F9;\n}\n\n/* 内容区域 */\n.content-area {\n\tpadding: 40rpx 30rpx;\n}\n\n/* 金额显示区域 */\n.amount-section {\n\tbackground-color: #ffffff;\n\tborder-radius: 24rpx;\n\tpadding: 60rpx 40rpx;\n\tmargin-bottom: 30rpx;\n\ttext-align: center;\n}\n\n.amount-icon {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tbackground-color: #3B99FC;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin: 0 auto 40rpx;\n}\n\n.currency-symbol {\n\tfont-size: 48rpx;\n\tcolor: #ffffff;\n\tfont-weight: bold;\n}\n\n.amount-display {\n\tmargin-bottom: 20rpx;\n}\n\n.amount-value {\n\tfont-size: 72rpx;\n\tcolor: #333333;\n\tfont-weight: bold;\n}\n\n.status-text {\n\tfont-size: 28rpx;\n\tcolor: #999999;\n}\n\n/* 详情信息 */\n.detail-section {\n\tbackground-color: #ffffff;\n\tborder-radius: 24rpx;\n\tpadding: 40rpx;\n\tmargin-bottom: 30rpx;\n}\n\n.detail-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx 0;\n\tborder-bottom: 1rpx solid #F5F5F5;\n}\n\n.detail-item:last-child {\n\tborder-bottom: none;\n}\n\n.detail-label {\n\tfont-size: 32rpx;\n\tcolor: #666666;\n}\n\n.detail-value {\n\tfont-size: 32rpx;\n\tcolor: #333333;\n\tfont-weight: 500;\n}\n\n/* 订单进度 */\n.progress-section {\n\tbackground-color: #ffffff;\n\tborder-radius: 24rpx;\n\tpadding: 40rpx;\n}\n\n.progress-title {\n\tfont-size: 36rpx;\n\tcolor: #333333;\n\tfont-weight: bold;\n\tdisplay: block;\n\tmargin-bottom: 40rpx;\n}\n\n.progress-list {\n\tposition: relative;\n}\n\n.progress-item {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tmargin-bottom: 40rpx;\n\tposition: relative;\n}\n\n.progress-item:last-child {\n\tmargin-bottom: 0;\n}\n\n.progress-item:not(:last-child)::after {\n\tcontent: '';\n\tposition: absolute;\n\tleft: 27rpx;\n\ttop: 56rpx;\n\twidth: 2rpx;\n\theight: 80rpx;\n\tbackground-color: #E5E5E5;\n}\n\n.progress-icon {\n\tmargin-right: 30rpx;\n\tposition: relative;\n\tz-index: 1;\n}\n\n.icon-circle {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.icon-circle.completed {\n\tbackground-color: #3B99FC;\n}\n\n.icon-circle.waiting {\n\tbackground-color: #E5E5E5;\n\tposition: relative;\n}\n\n.waiting-dot {\n\twidth: 16rpx;\n\theight: 16rpx;\n\tbackground-color: #999999;\n\tborder-radius: 50%;\n}\n\n.progress-content {\n\tflex: 1;\n\tpadding-top: 8rpx;\n}\n\n.progress-step {\n\tfont-size: 32rpx;\n\tcolor: #333333;\n\tfont-weight: 500;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n}\n\n.progress-time {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tdisplay: block;\n\tmargin-bottom: 4rpx;\n}\n\n.progress-desc {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tdisplay: block;\n}\n\n.progress-item.completed .progress-step {\n\tcolor: #333333;\n}\n\n.progress-item.waiting .progress-step {\n\tcolor: #999999;\n}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdraw_detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./withdraw_detail.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}