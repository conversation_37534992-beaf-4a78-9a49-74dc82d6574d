@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background: linear-gradient(180deg, #E1EDFF 0%, #FDFFFF 100%);
  min-height: 100vh;
}
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  min-height: 100vh;
  position: relative;
}
/* 顶部背景图 */
.banner-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
/* Logo和名称 */
.logo-container {
  margin-top: 180rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.logo-box {
  width: 180rpx;
  height: 180rpx;
  background-color: #ffffff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}
.logo-image {
  width: 140rpx;
  height: 140rpx;
}
.app-name {
  font-size: 60rpx;
  font-weight: bold;
  color: #333333;
  margin-top: 40rpx;
}
/* 登录按钮区域 */
.login-btn-area {
  width: 80%;
  padding: 0 60rpx;
  margin-top: 140rpx;
}
.login-btn {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background: linear-gradient(to right, #3a97fa, #3b87f7);
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 16rpx rgba(59, 135, 247, 0.3);
}
/* 协议区域 */
.agreement-area {
  display: flex;
  align-items: center;
  margin-top: 40rpx;
}
.checkbox-container {
  margin-right: 10rpx;
}
.checkbox-circle {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #cccccc;
  display: flex;
  align-items: center;
  justify-content: center;
}
.checkbox-active {
  border-color: #3F8DF9;
}
.checkbox-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3F8DF9;
}
.agreement-text {
  font-size: 28rpx;
  color: #666666;
}
.agreement-link {
  font-size: 28rpx;
  color: #3F8DF9;
}
