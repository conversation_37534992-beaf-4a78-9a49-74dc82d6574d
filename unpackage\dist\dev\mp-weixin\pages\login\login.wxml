<view class="login-container"><view class="logo-container"><text class="app-name">鲁航校园</text></view><view class="login-btn-area"><button class="login-btn" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">一键授权登录</button></view><view class="agreement-area"><view data-event-opts="{{[['tap',[['toggleAgreement',['$event']]]]]}}" class="checkbox-container" bindtap="__e"><view class="{{['checkbox-circle',[(isAgreed)?'checkbox-active':'']]}}"><block wx:if="{{isAgreed}}"><view class="checkbox-inner"></view></block></view></view><text class="agreement-text">阅读并同意</text><text data-event-opts="{{[['tap',[['viewUserAgreement',['$event']]]]]}}" class="agreement-link" catchtap="__e">《用户协议》</text><text class="agreement-text">和</text><text data-event-opts="{{[['tap',[['viewPrivacyPolicy',['$event']]]]]}}" class="agreement-link" catchtap="__e">《隐私政策》</text></view></view>