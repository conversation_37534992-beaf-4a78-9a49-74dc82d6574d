@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除不再需要的iconfont样式 */
page {
  background-color: #f5f7fa;
  min-height: 100vh;
}
.container {
  padding: 20rpx;
  min-height: 97vh;
  display: flex;
  flex-direction: column;
}
.passenger-list {
  flex: 1;
  /* 移除固定高度，让内容自然撑开 */
}
.passenger-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.passenger-info {
  flex: 1;
  margin-right: 20rpx;
}
.info-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10rpx;
  /* 使用gap属性提供更一致的间距 */
}
.passenger-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}
.id-type {
  font-size: 26rpx;
  color: #999999;
  background-color: #F6F7F9;
  padding: 2rpx 12rpx;
  border-radius: 12rpx;
}
.passenger-id {
  font-size: 28rpx;
  color: #666666;
}
.edit-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80rpx;
  padding: 10rpx;
  border-left: 1rpx solid #f0f0f0;
}
.edit-icon {
  width: 42rpx;
  height: 42rpx;
  margin-bottom: 8rpx;
}
.edit-text {
  font-size: 24rpx;
  color: #999999;
}
.add-button {
  height: 90rpx;
  background-color: #3F8DF9;
  color: #FFFFFF;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 45rpx;
  margin: 40rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(63, 141, 249, 0.3);
  transition: all 0.2s;
}
.add-button:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  opacity: 0.9;
}
.empty-tip {
  padding: 100rpx 0;
  text-align: center;
  color: #999;
  font-size: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.add-tip {
  font-size: 28rpx;
  color: #bbb;
  margin-top: 20rpx;
}
/* 选择框样式 */
.passenger-checkbox {
  margin-right: 20rpx;
  display: flex;
  align-items: center;
}
.checkbox-circle {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}
.checkbox-active {
  border-color: #3F8DF9;
  background-color: #3F8DF9;
}
.checkbox-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: white;
  border-radius: 50%;
}
/* 确认按钮样式 */
.confirm-button {
  height: 90rpx;
  background-color: #3F8DF9;
  color: #FFFFFF;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 45rpx;
  margin: 40rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(63, 141, 249, 0.3);
  transition: all 0.2s;
}
.confirm-button:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  opacity: 0.9;
}
