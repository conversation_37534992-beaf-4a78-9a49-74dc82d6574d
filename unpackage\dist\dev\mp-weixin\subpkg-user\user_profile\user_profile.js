(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["subpkg-user/user_profile/user_profile"],{120:function(n,e,t){"use strict";(function(n,e){var r=t(4);t(26);r(t(25));var o=r(t(121));n.__webpack_require_UNI_MP_PLUGIN__=t,e(o.default)}).call(this,t(1)["default"],t(2)["createPage"])},121:function(n,e,t){"use strict";t.r(e);var r=t(122),o=t(124);for(var u in o)["default"].indexOf(u)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(u);t(126);var a,i=t(33),s=Object(i["default"])(o["default"],r["render"],r["staticRenderFns"],!1,null,null,null,!1,r["components"],a);s.options.__file="subpkg-user/user_profile/user_profile.vue",e["default"]=s.exports},122:function(n,e,t){"use strict";t.r(e);var r=t(123);t.d(e,"render",(function(){return r["render"]})),t.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]})),t.d(e,"recyclableRender",(function(){return r["recyclableRender"]})),t.d(e,"components",(function(){return r["components"]}))},123:function(n,e,t){"use strict";var r;t.r(e),t.d(e,"render",(function(){return o})),t.d(e,"staticRenderFns",(function(){return a})),t.d(e,"recyclableRender",(function(){return u})),t.d(e,"components",(function(){return r}));var o=function(){var n=this,e=n.$createElement;n._self._c},u=!1,a=[];o._withStripped=!0},124:function(n,e,t){"use strict";t.r(e);var r=t(125),o=t.n(r);for(var u in r)["default"].indexOf(u)<0&&function(n){t.d(e,n,(function(){return r[n]}))}(u);e["default"]=o.a},125:function(n,e,t){"use strict";(function(n){var r=t(4);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=r(t(30)),u=t(61),a={data:function(){return{userInfo:{nickName:"",phone:"",avatar:"/static/images/avatar.png"}}},onLoad:function(){var n=o.default.getUserInfo();n&&(this.userInfo=n)},methods:{onChooseAvatar:function(e){var t=e.detail.avatarUrl;t&&(this.userInfo.avatar=t,this.updateUserInfo(),n.showToast({title:"头像更新成功",icon:"success"}))},onNicknameChange:function(e){var t=e.detail.value;t&&t!==this.userInfo.nickName&&(this.userInfo.nickName=t,this.updateUserInfo(),n.showToast({title:"昵称更新成功",icon:"success"}))},updateUserInfo:function(){var n=this;u.userApi.updateUserInfo({avatar:this.userInfo.avatar,nickName:this.userInfo.nickName}).then((function(e){console.log("用户信息更新成功",e),o.default.setUserInfo(n.userInfo)})).catch((function(n){console.error("用户信息更新失败",n)}))},logout:function(){n.showModal({title:"提示",content:"确定要退出登录吗？",success:function(e){e.confirm&&(o.default.clearLoginInfo(),n.switchTab({url:"/pages/index/index"}))}})}}};e.default=a}).call(this,t(2)["default"])},126:function(n,e,t){"use strict";t.r(e);var r=t(127),o=t.n(r);for(var u in r)["default"].indexOf(u)<0&&function(n){t.d(e,n,(function(){return r[n]}))}(u);e["default"]=o.a},127:function(n,e,t){}},[[120,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/subpkg-user/user_profile/user_profile.js.map