<template>
	<view class="coupon-container">
		<!-- 标签页导航 -->
		<view class="tab-nav">
			<view 
				class="tab-item" 
				:class="{ active: activeTab === 'available' }"
				@click="switchTab('available')"
			>
				<text class="tab-text">待使用</text>
				<view class="tab-indicator" v-if="activeTab === 'available'"></view>
			</view>
			<view 
				class="tab-item" 
				:class="{ active: activeTab === 'expired' }"
				@click="switchTab('expired')"
			>
				<text class="tab-text">已过期</text>
				<view class="tab-indicator" v-if="activeTab === 'expired'"></view>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 优惠券列表 -->
		<view class="coupon-list" v-else>
			<view 
				class="coupon-item" 
				v-for="(coupon, index) in currentCoupons" 
				:key="index"
			>
				<image
					class="coupon-bg"
					:src="coupon.status === 'available' ? '/subpkg-booking/static/icons/coupon_yes.png' : '/static/icons/coupon_no.png'"
					mode="aspectFill"
				></image>
				
				<view class="coupon-content">
					<!-- 左侧金额区域 -->
					<view class="coupon-left">
						<view class="amount-container">
							<text class="currency" :class="{ expired: coupon.status === 'expired' }">¥</text>
							<text class="amount" :class="{ expired: coupon.status === 'expired' }">{{ coupon.amount }}</text>
						</view>
						<text class="condition" :class="{ expired: coupon.status === 'expired' }">无门槛</text>
					</view>
					
					<!-- 右侧信息区域 -->
					<view class="coupon-right">
						<view class="coupon-info">
							<text class="coupon-title" :class="{ expired: coupon.status === 'expired' }">{{ coupon.title }}</text>
							<text class="coupon-expire" :class="{ expired: coupon.status === 'expired' }">
								{{ coupon.status === 'expired' ? coupon.expireTime : '有效期至：' + coupon.expireTime }}
							</text>
						</view>
						<view 
							class="use-btn" 
							:class="{ disabled: coupon.status === 'expired' }"
							@click="useCoupon(coupon)"
						>
							<text class="use-btn-text">{{ coupon.status === 'available' ? '去使用' : '已过期' }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="currentCoupons.length === 0">
			<text class="empty-text">暂无{{ activeTab === 'available' ? '待使用' : '已过期' }}的优惠券</text>
		</view>
	</view>
</template>

<script>
	import request from '../../utils/request.js';
	import auth from '../../utils/auth.js';
	
	export default {
		data() {
			return {
				activeTab: 'available', // available: 待使用, expired: 已过期
				coupons: [],
				loading: false
			}
		},
		onLoad() {
			this.loadCoupons();
		},
		
		onShow() {
			// 页面显示时刷新数据
			this.loadCoupons();
		},
		
		computed: {
			// 当前显示的优惠券列表
			currentCoupons() {
				return this.coupons.filter(coupon => {
					if (this.activeTab === 'available') {
						return coupon.status === 'available';
					} else if (this.activeTab === 'expired') {
						return coupon.status === 'expired';
					}
					return false;
				});
			}
		},
		methods: {
			// 加载优惠券列表
			async loadCoupons() {
				try {
					this.loading = true;
					
					
					// 调用接口获取优惠券列表
					const response = await request.get('/app/coupon/user/list');
					
					// 处理接口返回的数据
					if (response.code === 200 && response.data) {
						this.coupons = this.processCouponsData(response.data);
					} else {
						uni.showToast({
							title: response.msg || '获取优惠券失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取优惠券列表失败:', error);
					uni.showToast({
						title: '获取优惠券失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},
			
			// 处理接口返回的优惠券数据
			processCouponsData(data) {
				return data.map(item => {
					// 格式化时间
					const startTime = this.formatDate(item.startTime);
					const endTime = this.formatDate(item.endTime);
					
					// 判断优惠券状态
					let status = 'available';
					if (item.type === 0) {
						status = 'available'; // 未使用
					} else if (item.type === 1) {
						status = 'used'; // 已使用
					} else if (item.type === 2) {
						status = 'expired'; // 已过期
					}
					
					// 生成时间显示文本
					let timeText = '';
					if (status === 'expired') {
						// 已过期显示起止日期
						timeText = `${startTime}-${endTime}`;
					} else {
						// 待使用显示有效期至
						timeText = endTime;
					}
					
					return {
						id: item.id || Math.random(),
						amount: item.balancePice || 0,
						minAmount: 0, // 无门槛
						title: item.couponName || '优惠券',
						expireTime: timeText,
						status: status,
						startTime: startTime,
						endTime: endTime,
						type: item.type
					};
				});
			},
			
			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return '';
				
				try {
					const date = new Date(dateStr);
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					return `${year}.${month}.${day}`;
				} catch (error) {
					console.error('日期格式化失败:', error);
					return dateStr;
				}
			},
			
			// 切换标签页
			switchTab(tab) {
				this.activeTab = tab;
			},
			
			// 使用优惠券
			useCoupon(coupon) {
				if (coupon.status === 'expired') {
					return;
				}
				
				uni.showToast({
					title: '跳转到购票页面',
					icon: 'none'
				});
				
				// 这里可以跳转到购票页面并带上优惠券信息
				// uni.navigateTo({
				// 	url: `/pages/index/index?couponId=${coupon.id}`
				// });
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #F6F7F9;
}

.coupon-container {
	min-height: 100vh;
	background-color: #F6F7F9;
}

/* 标签页导航 */
.tab-nav {
	display: flex;
	background-color: #FFFFFF;
	padding: 0 40rpx;
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx 0;
	position: relative;
}

.tab-text {
	font-size: 32rpx;
	color: #999999;
	transition: color 0.3s;
}

.tab-item.active .tab-text {
	color: #303133;
	font-weight: bold;
}

.tab-indicator {
	position: absolute;
	bottom: 0;
	width: 60rpx;
	height: 6rpx;
	background-color: #3F8DF9;
	border-radius: 3rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.loading-text {
	font-size: 32rpx;
	color: #999999;
}

/* 优惠券列表 */
.coupon-list {
	padding: 30rpx;
}

.coupon-item {
	position: relative;
	margin-bottom: 30rpx;
	height: 200rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.coupon-bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}

.coupon-content {
	position: relative;
	z-index: 1;
	display: flex;
	align-items: center;
	height: 100%;
	padding: 0 40rpx;
}

/* 左侧金额区域 */
.coupon-left {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	width: 160rpx;
	margin-right: 60rpx;
}

.amount-container {
	display: flex;
	align-items: baseline;
	margin-bottom: 8rpx;
}

.currency {
	font-size: 40rpx;
	color: #FFFFFF;
	font-weight: bold;
	margin-right: 8rpx;
}

.currency.expired {
	color: #303133;
}

.amount {
	font-size: 54rpx;
	color: #FFFFFF;
	font-weight: bold;
	line-height: 1;
}

.amount.expired {
	color: #303133;
}

.condition {
	font-size: 28rpx;
	color: #FFFFFF;
	opacity: 0.9;
	margin-left: 25rpx;
}

.condition.expired {
	color: #909399;
	opacity: 1;
}

/* 分割线 */
.coupon-divider {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 140rpx;
	margin: 0 30rpx;
}

.dot {
	width: 16rpx;
	height: 16rpx;
	background-color: #F6F7F9;
	border-radius: 50%;
}

.dot-top {
	margin-bottom: 8rpx;
}

.dot-bottom {
	margin-top: 8rpx;
}

.dash-line {
	flex: 1;
	width: 2rpx;
	background-image: linear-gradient(to bottom, #FFFFFF 50%, transparent 50%);
	background-size: 100% 16rpx;
	background-repeat: repeat-y;
	opacity: 0.6;
}

/* 右侧信息区域 */
.coupon-right {
	flex: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 100%;
}

.coupon-info {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.coupon-title {
	font-size: 36rpx;
	color: #FFFFFF;
	font-weight: bold;
	margin-bottom: 12rpx;
}

.coupon-title.expired {
	color: #303133;
	font-weight: bold;
}

.coupon-expire {
	font-size: 22rpx;
	color: #FFFFFF;
	opacity: 0.9;
	white-space: nowrap;
}

.coupon-expire.expired {
	color: #909399;
	opacity: 1;
}

/* 使用按钮 */
.use-btn {
	background-color: #FFFFFF;
	border-radius: 20rpx;
	padding: 8rpx 16rpx;
	min-width: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 20rpx;
}

.use-btn.disabled {
	background-color: rgba(255, 255, 255, 0.3);
}

.use-btn-text {
	font-size: 22rpx;
	color: #FF6B35;
	font-weight: bold;
}

.use-btn.disabled .use-btn-text {
	color: #FFFFFF;
	opacity: 0.7;
}

/* 空状态 */
.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.empty-text {
	font-size: 32rpx;
	color: #999999;
}
</style> 