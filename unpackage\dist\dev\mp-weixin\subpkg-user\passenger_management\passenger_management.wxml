<view class="container"><view class="passenger-list"><block wx:if="{{$root.g0>0}}"><view><block wx:for="{{passengers}}" wx:for-item="passenger" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" class="passenger-item" bindtap="__e"><block wx:if="{{isSelectMode}}"><view class="passenger-checkbox"><view class="{{['checkbox-circle',[(passenger.selected)?'checkbox-active':'']]}}"><block wx:if="{{passenger.selected}}"><view class="checkbox-inner"></view></block></view></view></block><view class="passenger-info"><view class="info-row"><text class="passenger-name">{{passenger.name}}</text><text class="id-type">身份证</text><text class="passenger-id">{{passenger.idCard}}</text></view></view><block wx:if="{{!isSelectMode}}"><view data-event-opts="{{[['tap',[['editPassenger',[index]]]]]}}" class="edit-button" catchtap="__e"><image class="edit-icon" src="/static/icons/idet.png"></image><text class="edit-text">编辑</text></view></block></view></block></view></block><block wx:else><view class="empty-tip"><text>暂无乘客信息</text><text class="add-tip">点击下方按钮添加新乘客</text></view></block></view><block wx:if="{{isSelectMode}}"><view data-event-opts="{{[['tap',[['confirmSelection',['$event']]]]]}}" class="confirm-button" bindtap="__e">{{'确认选择 ('+$root.g1+')'}}</view></block><block wx:else><view data-event-opts="{{[['tap',[['addPassenger',['$event']]]]]}}" class="add-button" bindtap="__e">添加乘客</view></block></view>