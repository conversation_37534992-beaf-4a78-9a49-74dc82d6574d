<view class="order-detail-container"><view class="status-section"><text class="status-title">订单状态</text><view class="status-content"><view class="status-icon"><image class="status-img" src="{{orderStatus==='待出行'?'/static/icons/status1.png':'/static/icons/status2.png'}}"></image></view><text class="status-text" style="{{'color:'+(orderStatus==='待出行'?'#3F8DF9':'#303133')+';'}}">{{orderStatus}}</text></view></view><view class="trip-section"><text class="section-title">班次信息</text><view class="trip-time"><text class="trip-date">{{tripInfo.date}}</text><text class="trip-weekday">{{tripInfo.weekday}}</text><text class="trip-hour">{{tripInfo.time}}</text></view><view class="trip-route"><view class="route-stations"><view class="station-dot start"></view><view class="station-line"></view><view class="station-dot end"></view></view><view class="station-names"><text class="departure-station">{{tripInfo.departure}}</text><text class="arrival-station">{{tripInfo.arrival}}</text></view></view><block wx:if="{{orderStatus==='待出行'}}"><view data-event-opts="{{[['tap',[['openNavigation',['$event']]]]]}}" class="navigation-link" bindtap="__e"><text class="navigation-text">上车地点导航</text><uni-icons vue-id="f0a8c100-1" type="right" size="16" color="#333333" bind:__l="__l"></uni-icons></view></block><block wx:else><view class="ticket-count"><text class="ticket-count-text">{{"成人票×"+$root.g0}}</text></view></block></view><view class="passenger-section"><view data-event-opts="{{[['tap',[['togglePassengerList',['$event']]]]]}}" class="section-header" bindtap="__e"><text class="section-title">乘客信息</text><view class="collapse-btn"><text class="collapse-text">收起</text><uni-icons vue-id="f0a8c100-2" type="{{isPassengerCollapsed?'bottom':'top'}}" size="16" color="#3F8DF9" bind:__l="__l"></uni-icons></view></view><view hidden="{{!(!isPassengerCollapsed)}}" class="passenger-list"><block wx:for="{{passengers}}" wx:for-item="passenger" wx:for-index="index" wx:key="index"><view class="passenger-item"><text class="passenger-name">{{passenger.name}}</text><text class="passenger-id">{{passenger.idCard}}</text><text class="passenger-type">{{passenger.type}}</text></view></block></view></view><view class="payment-section"><view class="payment-item"><text class="payment-label">实付款</text><text class="payment-value price">{{"¥"+paymentInfo.actualPayment}}</text></view><view class="payment-item"><text class="payment-label">订单编号</text><view class="order-id"><text class="payment-value">{{paymentInfo.orderId}}</text><text data-event-opts="{{[['tap',[['copyOrderId',['$event']]]]]}}" class="copy-btn" bindtap="__e">复制</text></view></view><view class="payment-item"><text class="payment-label">下单时间</text><text class="payment-value">{{paymentInfo.orderTime}}</text></view><view class="payment-item"><text class="payment-label">付款时间</text><text class="payment-value">{{paymentInfo.payTime}}</text></view></view><view class="bottom-actions"><button class="consult-btn" open-type="contact"><uni-icons vue-id="f0a8c100-3" type="phone" size="20" color="#3F8DF9" bind:__l="__l"></uni-icons><text class="action-text">咨询</text></button><button data-event-opts="{{[['tap',[['showGroupQrCode',['$event']]]]]}}" class="qr-code-btn" bindtap="__e">群二维码</button><button data-event-opts="{{[['tap',[['openAfterSale',['$event']]]]]}}" class="after-sale-btn" bindtap="__e">售后服务</button></view><block wx:if="{{showQrPopup}}"><view data-event-opts="{{[['tap',[['hideGroupQrCode',['$event']]]]]}}" class="qr-popup" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="qr-popup-content" catchtap="__e"><view class="qr-popup-header"><text class="qr-popup-title">购票成功</text><view data-event-opts="{{[['tap',[['hideGroupQrCode',['$event']]]]]}}" class="qr-popup-close" bindtap="__e"><text class="close-icon">×</text></view></view><view class="qr-popup-body"><block wx:if="{{groupQrCodeUrl}}"><image class="qr-code-image" src="{{groupQrCodeUrl}}" mode="aspectFit"></image></block><block wx:else><view class="qr-loading"><text>加载中...</text></view></block><text class="qr-popup-desc">扫码进群，专业售后服务和时效通知</text></view><view class="qr-popup-footer"><button data-event-opts="{{[['tap',[['saveQrCode',['$event']]]]]}}" class="qr-save-btn" bindtap="__e">保存二维码</button></view></view></view></block></view>