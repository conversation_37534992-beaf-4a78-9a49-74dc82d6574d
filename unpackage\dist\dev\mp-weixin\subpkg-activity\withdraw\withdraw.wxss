@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #F6F7F9;
}
.withdraw-container {
  min-height: 100vh;
  background-color: #F6F7F9;
}
/* 内容区域 */
.content-area {
  padding: 30rpx;
}
/* 可提现金额 */
.available-amount-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 30rpx;
  text-align: center;
}
.available-label {
  font-size: 28rpx;
  color: #666666;
  display: block;
  margin-bottom: 20rpx;
}
.amount-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
}
.currency-symbol {
  font-size: 36rpx;
  color: #FF4757;
  font-weight: bold;
  margin-right: 8rpx;
}
.amount-value {
  font-size: 72rpx;
  color: #FF4757;
  font-weight: bold;
}
/* 提现金额输入 */
.withdraw-amount-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.section-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
}
.all-btn {
  font-size: 28rpx;
  color: #4A90E2;
  padding: 8rpx 16rpx;
  border: 1rpx solid #4A90E2;
  border-radius: 20rpx;
}
.amount-input-container {
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #E5E5E5;
  padding-bottom: 20rpx;
}
.amount-input {
  flex: 1;
  font-size: 44rpx;
  color: #333333;
  margin-left: 8rpx;
}
/* 微信收款码上传 */
.payment-code-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 60rpx;
}
.upload-container {
  margin: 30rpx 0;
  border: 2rpx dashed #E5E5E5;
  border-radius: 16rpx;
  min-height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FAFAFA;
}
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 20rpx;
}
.payment-code-image {
  max-width: 100%;
  max-height: 300rpx;
  border-radius: 12rpx;
}
.upload-tip {
  font-size: 24rpx;
  color: #999999;
  text-align: center;
  display: block;
}
/* 立即提现按钮 */
.withdraw-btn-container {
  padding: 0 20rpx;
}
.withdraw-btn {
  width: 100%;
  height: 88rpx;
  background-color: #3B99FC !important;
  background: #3B99FC !important;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #ffffff !important;
  font-weight: bold;
  border: none;
  line-height: 88rpx;
}
.withdraw-btn:not(.disabled) {
  background-color: #3B99FC !important;
  background: #3B99FC !important;
}
.withdraw-btn.disabled {
  background-color: #CCCCCC !important;
  color: #999999 !important;
}
.withdraw-btn::after {
  border: none;
}
