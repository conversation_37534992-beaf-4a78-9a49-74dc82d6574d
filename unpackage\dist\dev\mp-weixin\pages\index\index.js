(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/index"],{35:function(t,e,i){"use strict";(function(t,e){var n=i(4);i(26);n(i(25));var a=n(i(36));t.__webpack_require_UNI_MP_PLUGIN__=i,e(a.default)}).call(this,i(1)["default"],i(2)["createPage"])},36:function(t,e,i){"use strict";i.r(e);var n=i(37),a=i(39);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i(42);var o,s=i(33),c=Object(s["default"])(a["default"],n["render"],n["staticRenderFns"],!1,null,"57280228",null,!1,n["components"],o);c.options.__file="pages/index/index.vue",e["default"]=c.exports},37:function(t,e,i){"use strict";i.r(e);var n=i(38);i.d(e,"render",(function(){return n["render"]})),i.d(e,"staticRenderFns",(function(){return n["staticRenderFns"]})),i.d(e,"recyclableRender",(function(){return n["recyclableRender"]})),i.d(e,"components",(function(){return n["components"]}))},38:function(t,e,i){"use strict";var n;i.r(e),i.d(e,"render",(function(){return a})),i.d(e,"staticRenderFns",(function(){return o})),i.d(e,"recyclableRender",(function(){return r})),i.d(e,"components",(function(){return n}));try{n={uniCalendar:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/uni-calendar/components/uni-calendar/uni-calendar")]).then(i.bind(null,152))}}}catch(s){if(-1===s.message.indexOf("Cannot find module")||-1===s.message.indexOf(".vue"))throw s;console.error(s.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var a=function(){var t=this,e=t.$createElement,i=(t._self._c,t.showStationPicker?t.__map(t.currentCity.sysAddressDto,(function(e,i){var n=t.__get_orig(e),a=t.currentCity.sysAddressDto&&t.currentCity.sysAddressDto.length>0,r=a?t.isPlaceSelected(e):null,o=a?t.isPlaceSelected(e):null;return{$orig:n,g0:a,m0:r,m1:o}})):null),n=t.showStationPicker?!t.currentCity.sysAddressDto||0===t.currentCity.sysAddressDto.length:null;t.$mp.data=Object.assign({},{$root:{l0:i,g1:n}})},r=!1,o=[];a._withStripped=!0},39:function(t,e,i){"use strict";i.r(e);var n=i(40),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},40:function(t,e,i){"use strict";(function(t){var n=i(4);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i(41));function r(t,e){var i="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=o(t))||e&&t&&"number"===typeof t.length){i&&(t=i);var n=0,a=function(){};return{s:a,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,s=!0,c=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return s=t.done,t},e:function(t){c=!0,r=t},f:function(){try{s||null==i.return||i.return()}finally{if(c)throw r}}}}function o(t,e){if(t){if("string"===typeof t)return s(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?s(t,e):void 0}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var c={data:function(){var t=new Date,e=t.getFullYear(),i=t.getMonth()+1,n=t.getDate(),a="".concat(e,"-").concat(i,"-").concat(n),r=12===i?e+1:e,o=12===i?1:i+1;"".concat(r,"-").concat(o,"-").concat(n);return{title:"购票系统",departureStation:"",arrivalStation:"",departureStationId:null,arrivalStationId:null,date:"".concat(i,"月").concat(n,"日"),week:"星期".concat(["日","一","二","三","四","五","六"][t.getDay()]),isToday:!0,historyList:[],isRotating:!1,showCalendar:!1,startDate:a,endDate:"".concat(e+1,"-").concat(i,"-").concat(n),selectedDate:a,selectedInfo:null,showStationPicker:!1,isSelectingDeparture:!0,tempStation:"",tempStationId:null,currentCityIndex:0,departureCities:[],arrivalCities:[],cities:[],isLoading:!1,stationAnimateShow:!1}},computed:{currentCity:function(){return this.cities[this.currentCityIndex]||{addressName:"",sysAddressDto:[]}}},onLoad:function(){this.loadHistory(),this.loadDepartureStations(),this.loadArrivalStations()},methods:{loadDepartureStations:function(){var e=this;this.isLoading=!0,t.showLoading({title:"加载中..."}),a.default.get("/app/address/get?type=0").then((function(i){if(i&&200===i.code&&i.data&&i.data.length>0){if(e.departureCities=i.data,e.isSelectingDeparture&&(e.cities=e.departureCities),!e.departureStation){var n,a=r(e.departureCities);try{for(a.s();!(n=a.n()).done;){var o=n.value;if(o.sysAddressDto&&o.sysAddressDto.length>0){e.departureStation=o.sysAddressDto[0].addressName,e.departureStationId=o.sysAddressDto[0].id;break}}}catch(s){a.e(s)}finally{a.f()}!e.departureStation&&e.departureCities.length>0&&(e.departureStation=e.departureCities[0].addressName,e.departureStationId=e.departureCities[0].id)}}else t.showToast({title:"加载出发地数据失败",icon:"none"});e.isLoading=!1,t.hideLoading()})).catch((function(i){console.error("加载出发地数据失败",i),t.showToast({title:"加载出发地数据失败",icon:"none"}),e.isLoading=!1,t.hideLoading()}))},loadArrivalStations:function(){var e=this;a.default.get("/app/address/get?type=1").then((function(i){if(i&&200===i.code&&i.data&&i.data.length>0){if(e.arrivalCities=i.data,e.isSelectingDeparture||(e.cities=e.arrivalCities),!e.arrivalStation){var n,a=r(e.arrivalCities);try{for(a.s();!(n=a.n()).done;){var o=n.value;if(o.sysAddressDto&&o.sysAddressDto.length>0){e.arrivalStation=o.sysAddressDto[0].addressName,e.arrivalStationId=o.sysAddressDto[0].id;break}}}catch(s){a.e(s)}finally{a.f()}!e.arrivalStation&&e.arrivalCities.length>0&&(e.arrivalStation=e.arrivalCities[0].addressName,e.arrivalStationId=e.arrivalCities[0].id)}}else t.showToast({title:"加载目的地数据失败",icon:"none"})})).catch((function(e){console.error("加载目的地数据失败",e),t.showToast({title:"加载目的地数据失败",icon:"none"})}))},loadHistory:function(){try{var e=t.getStorageSync("searchHistory");e&&Array.isArray(e)&&(this.historyList=e.map((function(t){return"".concat(t.from,"--").concat(t.to)})))}catch(i){console.error("加载历史记录失败",i),this.historyList=[]}},calendarChange:function(t){if(t.year&&t.month&&t.date){this.selectedInfo=t,this.selectedDate="".concat(t.year,"-").concat(t.month,"-").concat(t.date);var e=new Date,i=new Date(e.getFullYear(),e.getMonth(),e.getDate()).getTime(),n=new Date(t.year,t.month-1,t.date).getTime();this.isToday=i===n}},monthSwitch:function(t){console.log("月份切换",t)},selectDeparture:function(){var e=this;this.isSelectingDeparture=!0,this.tempStation=this.departureStation,this.tempStationId=this.departureStationId,this.showStationPicker=!0,t.hideTabBar(),this.cities=this.departureCities,setTimeout((function(){e.stationAnimateShow=!0}),50),this.setInitialCityByStation(this.departureStation)},selectArrival:function(){var e=this;this.isSelectingDeparture=!1,this.tempStation=this.arrivalStation,this.tempStationId=this.arrivalStationId,this.showStationPicker=!0,t.hideTabBar(),this.cities=this.arrivalCities,setTimeout((function(){e.stationAnimateShow=!0}),50),this.setInitialCityByStation(this.arrivalStation)},setInitialCityByStation:function(t){this.currentCityIndex=0;for(var e=0;e<this.cities.length;e++){var i=this.cities[e];if(i.sysAddressDto&&i.sysAddressDto.length>0){var n=i.sysAddressDto.some((function(e){return e.addressName===t}));if(n)return void(this.currentCityIndex=e)}}},selectCity:function(t){this.currentCityIndex=t},selectPlace:function(t){this.tempStation=t.addressName,this.tempStationId=t.id},isPlaceSelected:function(t){return this.tempStation===t.addressName},confirmStationPicker:function(){this.tempStation&&this.tempStationId&&(this.isSelectingDeparture?(this.departureStation=this.tempStation,this.departureStationId=this.tempStationId):(this.arrivalStation=this.tempStation,this.arrivalStationId=this.tempStationId)),this.closeStationPicker()},cancelStationPicker:function(){this.closeStationPicker()},closeStationPicker:function(){var e=this;this.stationAnimateShow=!1,setTimeout((function(){e.showStationPicker=!1,t.showTabBar()}),300)},switchStations:function(){var t=this,e=this.departureStation,i=this.departureStationId;this.departureStation=this.arrivalStation,this.departureStationId=this.arrivalStationId,this.arrivalStation=e,this.arrivalStationId=i,this.isRotating=!0,setTimeout((function(){t.isRotating=!1}),500)},showDatePicker:function(){this.showCalendar=!0},cancelDatePicker:function(){this.showCalendar=!1},confirmDatePicker:function(){if(this.selectedInfo){var t=this.selectedInfo,e=t.year,i=t.month,n=t.date,a=new Date(e,i-1,n),r=a.getDay(),o=["日","一","二","三","四","五","六"];this.date="".concat(i,"月").concat(n,"日"),this.week="星期".concat(o[r]),this.isToday=this.isDateToday(a),this.selectedDate="".concat(e,"-").concat(i,"-").concat(n),this.showCalendar=!1}},isDateToday:function(t){var e=new Date;return t.getDate()===e.getDate()&&t.getMonth()===e.getMonth()&&t.getFullYear()===e.getFullYear()},queryTickets:function(){if(this.departureStation!==this.arrivalStation)if(this.departureStationId&&this.arrivalStationId){var e={from:this.departureStation,to:this.arrivalStation,date:this.selectedDate,timestamp:(new Date).getTime()},i=t.getStorageSync("searchHistory")||[],n=i.findIndex((function(t){return t.from===e.from&&t.to===e.to}));n>-1&&i.splice(n,1),i.unshift(e),i.length>10&&(i=i.slice(0,10)),t.setStorageSync("searchHistory",i),this.historyList=i.map((function(t){return"".concat(t.from,"--").concat(t.to)}));var a={departure:encodeURIComponent(this.departureStation),arrival:encodeURIComponent(this.arrivalStation),upAddressId:this.departureStationId,downAddressId:this.arrivalStationId,selectedDate:this.selectedDate},r=Object.keys(a).map((function(t){return"".concat(t,"=").concat(a[t])})).join("&");t.navigateTo({url:"/subpkg-booking/ticket_list/ticket_list?".concat(r)})}else t.showToast({title:"请重新选择出发地和目的地",icon:"none"});else t.showToast({title:"出发地和目的地不能相同",icon:"none"})},useHistory:function(t){var e=t.split("--");if(2===e.length){var i=e[0],n=e[1];this.departureStation=i,this.arrivalStation=n,this.findStationId(i,!0),this.findStationId(n,!1)}},findStationId:function(t,e){var i,n=e?this.departureCities:this.arrivalCities,a=r(n);try{for(a.s();!(i=a.n()).done;){var o=i.value;if(o.sysAddressDto&&o.sysAddressDto.length>0){var s,c=r(o.sysAddressDto);try{for(c.s();!(s=c.n()).done;){var d=s.value;if(d.addressName===t)return void(e?this.departureStationId=d.id:this.arrivalStationId=d.id)}}catch(u){c.e(u)}finally{c.f()}}if(o.addressName===t)return void(e?this.departureStationId=o.id:this.arrivalStationId=o.id)}}catch(u){a.e(u)}finally{a.f()}},clearHistory:function(){this.historyList=[],t.removeStorageSync("searchHistory"),t.showToast({title:"历史记录已清除",icon:"success"})}}};e.default=c}).call(this,i(2)["default"])},42:function(t,e,i){"use strict";i.r(e);var n=i(43),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},43:function(t,e,i){}},[[35,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map