@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #F5F5F5;
}
.profile-container {
  padding: 20rpx 0;
}
/* 通用部分样式 */
.section-label {
  font-size: 32rpx;
  color: #333;
}
/* 头像部分样式 */
.avatar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.avatar-wrapper {
  display: flex;
  align-items: center;
}
.avatar-button {
  background: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
  outline: none;
  width: auto;
  overflow: visible;
}
.avatar-button::after {
  border: none;
}
.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.avatar-edit-icon {
  width: 40rpx;
  display: flex;
  align-items: center;
}
.edit-icon {
  width: 32rpx;
  height: 32rpx;
}
/* 信息部分样式 */
.info-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 2rpx;
  /* 很小的间距让分割线效果 */
}
.info-input-wrapper {
  display: flex;
  align-items: center;
}
.info-value {
  font-size: 32rpx;
  color: #666;
  margin-right: 20rpx;
  height: 40rpx;
  border: none;
  background-color: transparent;
  text-align: right;
}
/* 退出登录按钮样式 */
.logout-button-wrapper {
  padding: 60rpx;
  margin-top: 60rpx;
}
.logout-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #FFFFFF;
  color: #333;
  font-size: 34rpx;
  border-radius: 45rpx;
  text-align: center;
}
