@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #F6F7F9;
}
.coupon-container {
  min-height: 100vh;
  background-color: #F6F7F9;
}
/* 标签页导航 */
.tab-nav {
  display: flex;
  background-color: #FFFFFF;
  padding: 0 40rpx;
}
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
  position: relative;
}
.tab-text {
  font-size: 32rpx;
  color: #999999;
  transition: color 0.3s;
}
.tab-item.active .tab-text {
  color: #303133;
  font-weight: bold;
}
.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 60rpx;
  height: 6rpx;
  background-color: #3F8DF9;
  border-radius: 3rpx;
}
/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text {
  font-size: 32rpx;
  color: #999999;
}
/* 优惠券列表 */
.coupon-list {
  padding: 30rpx;
}
.coupon-item {
  position: relative;
  margin-bottom: 30rpx;
  height: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.coupon-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}
.coupon-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 40rpx;
}
/* 左侧金额区域 */
.coupon-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 160rpx;
  margin-right: 60rpx;
}
.amount-container {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}
.currency {
  font-size: 40rpx;
  color: #FFFFFF;
  font-weight: bold;
  margin-right: 8rpx;
}
.currency.expired {
  color: #303133;
}
.amount {
  font-size: 54rpx;
  color: #FFFFFF;
  font-weight: bold;
  line-height: 1;
}
.amount.expired {
  color: #303133;
}
.condition {
  font-size: 28rpx;
  color: #FFFFFF;
  opacity: 0.9;
  margin-left: 25rpx;
}
.condition.expired {
  color: #909399;
  opacity: 1;
}
/* 分割线 */
.coupon-divider {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 140rpx;
  margin: 0 30rpx;
}
.dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #F6F7F9;
  border-radius: 50%;
}
.dot-top {
  margin-bottom: 8rpx;
}
.dot-bottom {
  margin-top: 8rpx;
}
.dash-line {
  flex: 1;
  width: 2rpx;
  background-image: linear-gradient(to bottom, #FFFFFF 50%, transparent 50%);
  background-size: 100% 16rpx;
  background-repeat: repeat-y;
  opacity: 0.6;
}
/* 右侧信息区域 */
.coupon-right {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}
.coupon-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.coupon-title {
  font-size: 36rpx;
  color: #FFFFFF;
  font-weight: bold;
  margin-bottom: 12rpx;
}
.coupon-title.expired {
  color: #303133;
  font-weight: bold;
}
.coupon-expire {
  font-size: 22rpx;
  color: #FFFFFF;
  opacity: 0.9;
  white-space: nowrap;
}
.coupon-expire.expired {
  color: #909399;
  opacity: 1;
}
/* 使用按钮 */
.use-btn {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  min-width: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20rpx;
}
.use-btn.disabled {
  background-color: rgba(255, 255, 255, 0.3);
}
.use-btn-text {
  font-size: 22rpx;
  color: #FF6B35;
  font-weight: bold;
}
.use-btn.disabled .use-btn-text {
  color: #FFFFFF;
  opacity: 0.7;
}
/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.empty-text {
  font-size: 32rpx;
  color: #999999;
}
