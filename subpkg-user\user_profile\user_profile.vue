<template>
	<view class="profile-container">
		<!-- 头像部分 -->
		<view class="avatar-section">
			<text class="section-label">头像</text>
			<view class="avatar-wrapper">
				<button open-type="chooseAvatar" @chooseavatar="onChooseAvatar" class="avatar-button">
					<image class="avatar" :src="userInfo.avatar"></image>
				</button>
				<view class="avatar-edit-icon">
					<image class="edit-icon" src="/static/icons/arrow_right.png"></image>
				</view>
			</view>
		</view>
		
		<!-- 昵称部分 -->
		<view class="info-section">
			<text class="section-label">昵称</text>
			<view class="info-input-wrapper">
				<input class="info-value" type="nickname" :value="userInfo.nickName" @blur="onNicknameChange" placeholder="请输入昵称" />
				<view class="avatar-edit-icon">
					<image class="edit-icon" src="/static/icons/arrow_right.png"></image>
				</view>
			</view>
		</view>
		
		<!-- 手机号部分 -->
		<view class="info-section">
			<text class="section-label">手机号</text>
			<view class="info-input-wrapper">
				<text class="info-value">{{userInfo.phone}}</text>
			</view>
		</view>
		
		<!-- 退出登录按钮 -->
		<view class="logout-button-wrapper">
			<button class="logout-button" @click="logout">退出登录</button>
		</view>
	</view>
</template>

<script>
	import auth from '../../utils/auth.js';
	import { userApi } from '../../utils/api.js';
	
	export default {
		data() {
			return {
				userInfo: {
					nickName: '',
					phone: '',
					avatar: '/static/images/avatar.png'
				}
			}
		},
		onLoad() {
			// 从缓存或API获取用户信息
			const userInfo = auth.getUserInfo();
			if (userInfo) {
				this.userInfo = userInfo;
			}
		},
		methods: {
			// 微信小程序获取头像回调
			onChooseAvatar(e) {
				const { avatarUrl } = e.detail;
				if (avatarUrl) {
					this.userInfo.avatar = avatarUrl;
					
					// 更新用户信息到服务器
					this.updateUserInfo();
					
					uni.showToast({
						title: '头像更新成功',
						icon: 'success'
					});
				}
			},
			
			// 处理昵称变更
			onNicknameChange(e) {
				const nickName = e.detail.value;
				if (nickName && nickName !== this.userInfo.nickName) {
					this.userInfo.nickName = nickName;
					
					// 更新用户信息到服务器
					this.updateUserInfo();
					
					uni.showToast({
						title: '昵称更新成功',
						icon: 'success'
					});
				}
			},
			
			// 更新用户信息到服务器
			updateUserInfo() {
				// 调用API更新用户信息
				userApi.updateUserInfo({
					avatar: this.userInfo.avatar,
					nickName: this.userInfo.nickName
				}).then(res => {
					console.log('用户信息更新成功', res);
					
					// 保存到缓存
					auth.setUserInfo(this.userInfo);
				}).catch(err => {
					console.error('用户信息更新失败', err);
				});
			},
			
			// 退出登录
			logout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除登录信息
							auth.clearLoginInfo();
							
							// 返回首页
							uni.switchTab({
								url: '/pages/index/index'
							});
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #F5F5F5;
}

.profile-container {
	padding: 20rpx 0;
}

/* 通用部分样式 */
.section-label {
	font-size: 32rpx;
	color: #333;
}

/* 头像部分样式 */
.avatar-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #FFFFFF;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.avatar-wrapper {
	display: flex;
	align-items: center;
}

.avatar-button {
	background: none;
	padding: 0;
	margin: 0;
	line-height: 1;
	border: none;
	outline: none;
	width: auto;
	overflow: visible;
}

.avatar-button::after {
	border: none;
}

.avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.avatar-edit-icon {
	width: 40rpx;
	display: flex;
	align-items: center;
}

.edit-icon {
	width: 32rpx;
	height: 32rpx;
}

/* 信息部分样式 */
.info-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #FFFFFF;
	padding: 30rpx;
	margin-bottom: 2rpx; /* 很小的间距让分割线效果 */
}

.info-input-wrapper {
	display: flex;
	align-items: center;
}

.info-value {
	font-size: 32rpx;
	color: #666;
	margin-right: 20rpx;
	height: 40rpx;
	border: none;
	background-color: transparent;
	text-align: right;
}

/* 退出登录按钮样式 */
.logout-button-wrapper {
	padding: 60rpx;
	margin-top: 60rpx;
}

.logout-button {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	background-color: #FFFFFF;
	color: #333;
	font-size: 34rpx;
	border-radius: 45rpx;
	text-align: center;
}
</style> 