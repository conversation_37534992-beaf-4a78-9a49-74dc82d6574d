(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["subpkg-booking/coupon/coupon"],{100:function(t,n,e){"use strict";e.r(n);var r=e(101),o=e.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return r[t]}))}(a);n["default"]=o.a},101:function(t,n,e){"use strict";(function(t){var r=e(4);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=r(e(58)),a=r(e(60)),u=r(e(41)),c=(r(e(30)),{data:function(){return{activeTab:"available",coupons:[],loading:!1}},onLoad:function(){this.loadCoupons()},onShow:function(){this.loadCoupons()},computed:{currentCoupons:function(){var t=this;return this.coupons.filter((function(n){return"available"===t.activeTab?"available"===n.status:"expired"===t.activeTab&&"expired"===n.status}))}},methods:{loadCoupons:function(){var n=this;return(0,a.default)(o.default.mark((function e(){var r;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,n.loading=!0,e.next=4,u.default.get("/app/coupon/user/list");case 4:r=e.sent,200===r.code&&r.data?n.coupons=n.processCouponsData(r.data):t.showToast({title:r.msg||"获取优惠券失败",icon:"none"}),e.next=12;break;case 8:e.prev=8,e.t0=e["catch"](0),console.error("获取优惠券列表失败:",e.t0),t.showToast({title:"获取优惠券失败",icon:"none"});case 12:return e.prev=12,n.loading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})))()},processCouponsData:function(t){var n=this;return t.map((function(t){var e=n.formatDate(t.startTime),r=n.formatDate(t.endTime),o="available";0===t.type?o="available":1===t.type?o="used":2===t.type&&(o="expired");var a="";return a="expired"===o?"".concat(e,"-").concat(r):r,{id:t.id||Math.random(),amount:t.balancePice||0,minAmount:0,title:t.couponName||"优惠券",expireTime:a,status:o,startTime:e,endTime:r,type:t.type}}))},formatDate:function(t){if(!t)return"";try{var n=new Date(t),e=n.getFullYear(),r=String(n.getMonth()+1).padStart(2,"0"),o=String(n.getDate()).padStart(2,"0");return"".concat(e,".").concat(r,".").concat(o)}catch(a){return console.error("日期格式化失败:",a),t}},switchTab:function(t){this.activeTab=t},useCoupon:function(n){"expired"!==n.status&&t.showToast({title:"跳转到购票页面",icon:"none"})}}});n.default=c}).call(this,e(2)["default"])},102:function(t,n,e){"use strict";e.r(n);var r=e(103),o=e.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return r[t]}))}(a);n["default"]=o.a},103:function(t,n,e){},96:function(t,n,e){"use strict";(function(t,n){var r=e(4);e(26);r(e(25));var o=r(e(97));t.__webpack_require_UNI_MP_PLUGIN__=e,n(o.default)}).call(this,e(1)["default"],e(2)["createPage"])},97:function(t,n,e){"use strict";e.r(n);var r=e(98),o=e(100);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(a);e(102);var u,c=e(33),i=Object(c["default"])(o["default"],r["render"],r["staticRenderFns"],!1,null,null,null,!1,r["components"],u);i.options.__file="subpkg-booking/coupon/coupon.vue",n["default"]=i.exports},98:function(t,n,e){"use strict";e.r(n);var r=e(99);e.d(n,"render",(function(){return r["render"]})),e.d(n,"staticRenderFns",(function(){return r["staticRenderFns"]})),e.d(n,"recyclableRender",(function(){return r["recyclableRender"]})),e.d(n,"components",(function(){return r["components"]}))},99:function(t,n,e){"use strict";var r;e.r(n),e.d(n,"render",(function(){return o})),e.d(n,"staticRenderFns",(function(){return u})),e.d(n,"recyclableRender",(function(){return a})),e.d(n,"components",(function(){return r}));var o=function(){var t=this,n=t.$createElement,e=(t._self._c,t.currentCoupons.length);t.$mp.data=Object.assign({},{$root:{g0:e}})},a=!1,u=[];o._withStripped=!0}},[[96,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/subpkg-booking/coupon/coupon.js.map