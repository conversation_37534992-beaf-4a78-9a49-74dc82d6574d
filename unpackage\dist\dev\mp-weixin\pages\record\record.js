(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/record/record"],{52:function(e,t,r){"use strict";(function(e,t){var n=r(4);r(26);n(r(25));var o=n(r(53));e.__webpack_require_UNI_MP_PLUGIN__=r,t(o.default)}).call(this,r(1)["default"],r(2)["createPage"])},53:function(e,t,r){"use strict";r.r(t);var n=r(54),o=r(56);for(var a in o)["default"].indexOf(a)<0&&function(e){r.d(t,e,(function(){return o[e]}))}(a);r(62);var s,c=r(33),u=Object(c["default"])(o["default"],n["render"],n["staticRenderFns"],!1,null,null,null,!1,n["components"],s);u.options.__file="pages/record/record.vue",t["default"]=u.exports},54:function(e,t,r){"use strict";r.r(t);var n=r(55);r.d(t,"render",(function(){return n["render"]})),r.d(t,"staticRenderFns",(function(){return n["staticRenderFns"]})),r.d(t,"recyclableRender",(function(){return n["recyclableRender"]})),r.d(t,"components",(function(){return n["components"]}))},55:function(e,t,r){"use strict";var n;r.r(t),r.d(t,"render",(function(){return o})),r.d(t,"staticRenderFns",(function(){return s})),r.d(t,"recyclableRender",(function(){return a})),r.d(t,"components",(function(){return n}));try{n={uniIcons:function(){return Promise.all([r.e("common/vendor"),r.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(r.bind(null,165))}}}catch(c){if(-1===c.message.indexOf("Cannot find module")||-1===c.message.indexOf(".vue"))throw c;console.error(c.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var o=function(){var e=this,t=e.$createElement,r=(e._self._c,e.loading?null:e.filteredOrders.length),n=e.__map(e.filteredOrders,(function(t,r){var n=e.__get_orig(t),o=e.getStatusIcon(t.status),a=e.getStatusColor(t.status),s=e.getStatusClass(t.status),c=t.price.toFixed(2),u=t.paid.toFixed(2);return{$orig:n,m0:o,m1:a,m2:s,g1:c,g2:u}}));e.$mp.data=Object.assign({},{$root:{g0:r,l0:n}})},a=!1,s=[];o._withStripped=!0},56:function(e,t,r){"use strict";r.r(t);var n=r(57),o=r.n(n);for(var a in n)["default"].indexOf(a)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(a);t["default"]=o.a},57:function(e,t,r){"use strict";(function(e){var n=r(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(58)),a=n(r(60)),s=r(61),c={data:function(){return{activeTab:0,orders:[],loading:!1,loadingText:"正在加载订单...",showQrPopup:!1,groupQrCodeUrl:""}},computed:{filteredOrders:function(){return 0===this.activeTab?this.orders:1===this.activeTab?this.orders.filter((function(e){return"待出行"===e.status})):this.orders.filter((function(e){return"已出行"===e.status||"已退款"===e.status||"待退款"===e.status}))},pendingCount:function(){return this.orders.filter((function(e){return"待出行"===e.status})).length},completedCount:function(){return this.orders.filter((function(e){return"已出行"===e.status||"已退款"===e.status||"待退款"===e.status})).length}},onLoad:function(e){var t=this;e&&"true"===e.paymentSuccess&&setTimeout((function(){t.showGroupQrCode()}),500)},onShow:function(){this.loadOrderList()},methods:{loadOrderList:function(){var t=this;return(0,a.default)(o.default.mark((function r(){var n;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,t.loading=!0,t.loadingText="正在加载订单...",r.next=5,s.orderApi.getOrderList();case 5:n=r.sent,n&&200===n.code?t.orders=t.processOrderData(n):(t.orders=[],console.log("订单数据为空或接口返回异常:",n)),r.next=14;break;case 9:r.prev=9,r.t0=r["catch"](0),console.error("获取订单列表失败:",r.t0),t.orders=[],e.showToast({title:"获取订单失败，请重试",icon:"none"});case 14:return r.prev=14,t.loading=!1,r.finish(14);case 17:case"end":return r.stop()}}),r,null,[[0,9,14,17]])})))()},processOrderData:function(e){var t=this;return e&&e.rows&&Array.isArray(e.rows)?e.rows.map((function(e){var r="",n=!1,o=!1;switch(e.payStatus){case 0:r="未支付",n=!0,o=!1;break;case 1:r="待出行",n=!0,o=!0;break;case 2:r="待退款",n=!1,o=!1;break;case 3:r="已退款",n=!1,o=!1;break;case 4:r="已出行",n=!1,o=!1;break;default:r="未知状态",n=!1,o=!1}var a="";a=e.upTicketTime?t.formatDateTime(e.upTicketTime):"待确定";var s="".concat(e.upAddress||"未知"," → ").concat(e.downAddress||"未知");return{id:e.id,orderNo:e.orderNo,type:"汽车票",status:r,route:s,price:parseFloat(e.payAmout||0),departureTime:a,paid:parseFloat(e.payAmout||0),canCancel:n,canRefund:o,originalData:e}})):[]},formatDateTime:function(e){if(!e)return"待确定";try{var t=new Date(e),r=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),o=String(t.getDate()).padStart(2,"0"),a=String(t.getHours()).padStart(2,"0"),s=String(t.getMinutes()).padStart(2,"0");return"".concat(r,"-").concat(n,"-").concat(o," ").concat(a,":").concat(s)}catch(c){return console.error("日期格式化失败:",c),"待确定"}},getStatusIcon:function(e){switch(e){case"待出行":case"未支付":return"paperplane-filled";case"已出行":return"paperplane";case"已退款":case"待退款":return"undo";default:return"paperplane"}},getStatusColor:function(e){switch(e){case"待出行":return"#3F8DF9";case"未支付":return"#FF9500";case"已出行":return"#888888";case"已退款":return"#00C851";case"待退款":return"#FF6B6B";default:return"#888888"}},getStatusClass:function(e){switch(e){case"待出行":return"pending";case"未支付":return"unpaid";case"已出行":return"completed";case"已退款":return"refunded";case"待退款":return"refunding";default:return"completed"}},switchTab:function(e){this.activeTab=e},goToOrderDetail:function(t){var r=this.orders.find((function(e){return e.id===t}));r?(e.setStorageSync("orderDetailData",r),e.navigateTo({url:"/subpkg-booking/order_detail/order_detail?id=".concat(t)})):e.showToast({title:"订单信息不存在",icon:"none"})},cancelOrder:function(t){e.showModal({title:"提示",content:"确定要取消该订单吗？",success:function(t){t.confirm&&e.showToast({title:"订单已取消",icon:"success"})}})},applyRefund:function(t){var r=this;return(0,a.default)(o.default.mark((function n(){var a,c;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,new Promise((function(t){e.showModal({title:"申请售后",content:"确定要申请售后吗？",success:function(e){t(e.confirm)}})}));case 3:if(a=n.sent,a){n.next=6;break}return n.abrupt("return");case 6:return e.showLoading({title:"申请中..."}),n.next=9,s.orderApi.applyRefund(t);case 9:c=n.sent,e.hideLoading(),c&&200===c.code?(e.showToast({title:"申请售后成功",icon:"success"}),r.loadOrderList()):e.showToast({title:c.msg||"申请售后失败",icon:"none"}),n.next=19;break;case 14:n.prev=14,n.t0=n["catch"](0),e.hideLoading(),console.error("申请售后失败:",n.t0),e.showToast({title:"申请售后失败，请重试",icon:"none"});case 19:case"end":return n.stop()}}),n,null,[[0,14]])})))()},showGroupQrCode:function(){var t=this;return(0,a.default)(o.default.mark((function r(){var n;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(r.prev=0,t.showQrPopup=!0,!t.groupQrCodeUrl){r.next=4;break}return r.abrupt("return");case 4:return r.next=6,s.configApi.getConfig("group_qr_code");case 6:n=r.sent,n&&200===n.code?t.groupQrCodeUrl=n.msg||"":e.showToast({title:"获取群二维码失败",icon:"none"}),r.next=14;break;case 10:r.prev=10,r.t0=r["catch"](0),console.error("获取群二维码失败:",r.t0),e.showToast({title:"获取群二维码失败",icon:"none"});case 14:case"end":return r.stop()}}),r,null,[[0,10]])})))()},hideGroupQrCode:function(){this.showQrPopup=!1},saveQrCode:function(){var t=this;return(0,a.default)(o.default.mark((function r(){var n,a;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t.groupQrCodeUrl){r.next=3;break}return e.showToast({title:"二维码加载中，请稍后",icon:"none"}),r.abrupt("return");case 3:return r.prev=3,e.showLoading({title:"正在保存..."}),r.next=7,new Promise((function(r,n){e.downloadFile({url:t.groupQrCodeUrl,success:function(e){200===e.statusCode?r(e.tempFilePath):n(new Error("下载失败"))},fail:n})}));case 7:return n=r.sent,r.next=10,new Promise((function(t,r){e.saveImageToPhotosAlbum({filePath:n,success:t,fail:r})}));case 10:e.hideLoading(),e.showToast({title:"保存成功",icon:"success"}),r.next=21;break;case 14:r.prev=14,r.t0=r["catch"](3),e.hideLoading(),console.error("保存失败:",r.t0),a="保存失败，请重试",r.t0.errMsg&&r.t0.errMsg.includes("auth")?a="请授权访问相册":r.t0.errMsg&&r.t0.errMsg.includes("download")&&(a="图片下载失败"),e.showToast({title:a,icon:"none"});case 21:case"end":return r.stop()}}),r,null,[[3,14]])})))()}}};t.default=c}).call(this,r(2)["default"])},62:function(e,t,r){"use strict";r.r(t);var n=r(63),o=r.n(n);for(var a in n)["default"].indexOf(a)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(a);t["default"]=o.a},63:function(e,t,r){}},[[52,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/record/record.js.map