<template>
	<view class="login-container">
		
		<!-- 中心Logo和名称 -->
		<view class="logo-container">
			<text class="app-name">鲁航校园</text>
		</view>
		
		<!-- 授权登录按钮 -->
		<view class="login-btn-area">
			<button class="login-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">一键授权登录</button>
		</view>
		
		<!-- 协议勾选区域 -->
		<view class="agreement-area">
			<view class="checkbox-container" @click="toggleAgreement">
				<view :class="['checkbox-circle', { 'checkbox-active': isAgreed }]">
					<view v-if="isAgreed" class="checkbox-inner"></view>
				</view>
			</view>
			<text class="agreement-text">阅读并同意</text>
			<text class="agreement-link" @click.stop="viewUserAgreement">《用户协议》</text>
			<text class="agreement-text">和</text>
			<text class="agreement-link" @click.stop="viewPrivacyPolicy">《隐私政策》</text>
		</view>
	</view>
</template>

<script>
	import auth from '../../utils/auth.js';
	import request from '../../utils/request.js';
	
	export default {
		data() {
			return {
				isAgreed: false, // 是否同意协议
				redirect: '', // 登录后重定向的页面
				inviteUserId: '', // 邀请用户ID
			}
		},
		onLoad(options) {
			// 获取登录后需要跳转的页面
			if (options.redirect) {
				this.redirect = decodeURIComponent(options.redirect);
			}

			// 获取邀请用户ID（从小程序码扫码或分享链接进入）
			if (options.userId) {
				this.inviteUserId = options.userId;
				console.log('检测到邀请用户ID:', this.inviteUserId);
			}

			// 处理场景值（从小程序码扫码进入时的参数）
			if (options.scene) {
				// 解析场景值，格式可能是 "userId=123" 或直接是 "123"
				const scene = decodeURIComponent(options.scene);
				if (scene.includes('userId=')) {
					this.inviteUserId = scene.split('userId=')[1];
				} else {
					// 如果场景值直接是用户ID
					this.inviteUserId = scene;
				}
				console.log('从场景值获取邀请用户ID:', this.inviteUserId);
			}
		},
		methods: {
			// 切换协议同意状态
			toggleAgreement() {
				this.isAgreed = !this.isAgreed;
			},
			
			// 查看用户协议
			viewUserAgreement() {
				// 显示加载中
				uni.showLoading({
					title: '加载中...'
				});
				
				// 请求用户协议内容
				request.get('/app/configure/user_agreement').then(res => {
					uni.hideLoading();
					if (res && res.code === 200) {
						// 使用弹窗展示内容
						uni.showModal({
							title: '用户协议',
							content: res.msg || '暂无内容',
							showCancel: false,
							confirmText: '我知道了'
						});
					} else {
						uni.showToast({
							title: '获取协议内容失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: '获取协议内容失败',
						icon: 'none'
					});
				});
			},
			
			// 查看隐私政策
			viewPrivacyPolicy() {
				// 显示加载中
				uni.showLoading({
					title: '加载中...'
				});
				
				// 请求隐私政策内容
				request.get('/app/configure/privacy_agreement').then(res => {
					uni.hideLoading();
					if (res && res.code === 200) {
						// 使用弹窗展示内容
						uni.showModal({
							title: '隐私政策',
							content: res.msg || '暂无内容',
							showCancel: false,
							confirmText: '我知道了'
						});
					} else {
						uni.showToast({
							title: '获取隐私政策内容失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: '获取隐私政策内容失败',
						icon: 'none'
					});
				});
			},
			
			// 获取手机号
			getPhoneNumber(e) {
				if (!this.isAgreed) {
					uni.showToast({
						title: '请先阅读并同意协议',
						icon: 'none'
					});
					return;
				}
				
				// 显示加载中
				uni.showLoading({
					title: '登录中...',
					mask: true
				});
				
				// 获取手机号成功
				if (e.detail.code) {
					// 获取登录凭证
					uni.login({
						provider: 'weixin',
						success: (loginRes) => {
							if (loginRes.code) {
                                console.log(loginRes.code, e.detail.code);
								// 直接调用后端登录接口
								this.loginApi(loginRes.code, e.detail.code);
							} else {
								uni.hideLoading();
								uni.showToast({
									title: '获取登录凭证失败',
									icon: 'none'
								});
							}
						},
						fail: () => {
							uni.hideLoading();
							uni.showToast({
								title: '登录失败，请重试',
								icon: 'none'
							});
						}
					});
				} else {
					// 用户拒绝授权
					uni.hideLoading();
					uni.showToast({
						title: '获取手机号失败，请重试',
						icon: 'none'
					});
				}
			},
			
			// 调用登录API
			loginApi(jsCode, phoneCode) {
				// 构建请求参数
				const requestData = {
					phoneCode: phoneCode
				};

				// 如果有邀请用户ID，添加到请求参数中
				if (this.inviteUserId) {
					requestData.inviteUserId = this.inviteUserId;
					console.log('登录时传递邀请用户ID:', this.inviteUserId);
				}

				// 调用后端登录接口
				request.post('/app/login?phoneCode='+ phoneCode +'&jsCode='+ jsCode + '&inviteUserId=' + this.inviteUserId, requestData).then(res => {

					// 保存token
					auth.setToken(res.token);

					// 获取token后，立即调用/app/user接口获取用户信息
					this.getUserInfo();

				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err.msg || '登录失败，请重试',
						icon: 'none'
					});
				});
			},

			// 获取用户信息
			getUserInfo() {
				// 调用/app/user接口获取用户详细信息
				request.get('/app/user').then(res => {
					// 保存用户信息，包含userid
					const userInfo = {
						userid: res.data.userId, // 根据接口返回字段调整
						nickName:  res.data.nickName || '微信用户',
						phone: res.data.phonenumber,
						avatar: res.data.avatar
					};
					auth.setUserInfo(userInfo);

					uni.hideLoading();
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					});

					// 登录成功后跳转
					setTimeout(() => {
						// 如果有重定向页面，跳转到该页面
						if (this.redirect) {
							// 判断是否为 tabbar 页面
							const tabbarPages = [
								'/pages/index/index',
								'/pages/record/record',
								'/pages/mine/mine'
							];

							if (tabbarPages.includes(this.redirect)) {
								// 如果是 tabbar 页面，使用 switchTab
								uni.switchTab({
									url: this.redirect
								});
							} else {
								// 如果不是 tabbar 页面，使用 redirectTo
								uni.redirectTo({
									url: this.redirect
								});
							}
						} else {
							// 默认跳转到首页
							uni.switchTab({
								url: '/pages/index/index'
							});
						}
					}, 1000);
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err.msg || '获取用户信息失败',
						icon: 'none'
					});
					// 即使获取用户信息失败，也清除token，要求重新登录
					auth.clearLoginInfo();
				});
			},
			
			// 这些方法已不再使用，可以删除
			/*
			// 获取用户信息
			getUserProfile(phoneCode) {
				// 调用微信getUserProfile获取用户信息
				uni.getUserProfile({
					desc: '用于完善用户资料', // 声明获取用户个人信息后的用途
					success: (profileRes) => {
						// 获取用户信息成功
						const userInfo = {
							name: profileRes.userInfo.nickName,
							avatar: profileRes.userInfo.avatarUrl,
							phone: '' // 手机号需要通过后端解密获取
						};
						
						// 调用登录接口
						this.login(userInfo, phoneCode);
					},
					fail: (err) => {
						uni.hideLoading();
						uni.showToast({
							title: '授权失败，请重试',
							icon: 'none'
						});
						console.error('获取用户信息失败:', err);
					}
				});
			},
			
			// 登录方法
			login(userInfo, phoneCode) {
				// 获取微信登录凭证
				uni.login({
					provider: 'weixin',
					success: (loginRes) => {
						if (loginRes.code) {
							// 调用后端登录接口
							// 实际开发中替换为真实接口
							setTimeout(() => {
								// 模拟请求成功
								// 模拟手机号(实际中应该由后端解密获取)
								userInfo.phone = '133****5678';
								// 保存token
								auth.setToken('mock_token_123456');
								// 保存用户信息
								auth.setUserInfo(userInfo);
								
								uni.hideLoading();
								uni.showToast({
									title: '登录成功',
									icon: 'success'
								});
								
								// 登录成功后跳转
								setTimeout(() => {
									// 如果有重定向页面，跳转到该页面
									if (this.redirect) {
										uni.redirectTo({
											url: this.redirect
										});
									} else {
										// 默认跳转到首页
										uni.switchTab({
											url: '/pages/index/index'
										});
									}
								}, 1500);
							}, 1000);
						} else {
							uni.hideLoading();
							uni.showToast({
								title: '获取登录凭证失败',
								icon: 'none'
							});
						}
					},
					fail: () => {
						uni.hideLoading();
						uni.showToast({
							title: '登录失败，请重试',
							icon: 'none'
						});
					}
				});
			}
			*/
		}
	}
</script>

<style lang="scss">
page {
    background: linear-gradient( 180deg, #E1EDFF 0%, #FDFFFF 100%);
	min-height: 100vh;
}

.login-container {
	display: flex;
	flex-direction: column;
	align-items: center;
    justify-content: space-around;
	width: 100%;
	min-height: 100vh;
	position: relative;
}

/* 顶部背景图 */
.banner-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: -1;
}

/* Logo和名称 */
.logo-container {
	margin-top: 180rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.logo-box {
	width: 180rpx;
	height: 180rpx;
	background-color: #ffffff;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}

.logo-image {
	width: 140rpx;
	height: 140rpx;
}

.app-name {
	font-size: 60rpx;
	font-weight: bold;
	color: #333333;
	margin-top: 40rpx;
}

/* 登录按钮区域 */
.login-btn-area {
	width: 80%;
	padding: 0 60rpx;
	margin-top: 140rpx;
}

.login-btn {
	width: 100%;
	height: 100rpx;
	line-height: 100rpx;
	background: linear-gradient(to right, #3a97fa, #3b87f7);
	color: #ffffff;
	font-size: 36rpx;
	font-weight: bold;
	border-radius: 50rpx;
	box-shadow: 0 8rpx 16rpx rgba(59, 135, 247, 0.3);
}

/* 协议区域 */
.agreement-area {
	display: flex;
	align-items: center;
	margin-top: 40rpx;
}

.checkbox-container {
	margin-right: 10rpx;
}

.checkbox-circle {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	border: 2rpx solid #cccccc;
	display: flex;
	align-items: center;
	justify-content: center;
}

.checkbox-active {
	border-color: #3F8DF9;
}

.checkbox-inner {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	background-color: #3F8DF9;
}

.agreement-text {
	font-size: 28rpx;
	color: #666666;
}

.agreement-link {
	font-size: 28rpx;
	color: #3F8DF9;
}
</style> 