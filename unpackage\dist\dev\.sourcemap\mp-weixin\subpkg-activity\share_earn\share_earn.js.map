{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/subpkg-activity/share_earn/share_earn.vue?2cfa", "webpack:///E:/购票系统/购票系统/subpkg-activity/share_earn/share_earn.vue?ab09", "webpack:///E:/购票系统/购票系统/subpkg-activity/share_earn/share_earn.vue?ad8d", "webpack:///E:/购票系统/购票系统/subpkg-activity/share_earn/share_earn.vue?4958", "uni-app:///subpkg-activity/share_earn/share_earn.vue", "webpack:///E:/购票系统/购票系统/subpkg-activity/share_earn/share_earn.vue?79d4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniIcons", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "inviteRecords", "length", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "statusBarHeight", "navbarHeight", "activeTab", "showPosterModal", "posterGenerated", "canvasWidth", "canvasHeight", "posterImagePath", "screenWidth", "pixelRatio", "qrCodePath", "shareData", "income", "shareNumber", "activityRules", "rewardRecords", "computed", "navbarStyle", "height", "paddingTop", "onLoad", "onShow", "methods", "setNavbarInfo", "initCanvasSize", "calculatedWidth", "goBack", "uni", "getShareData", "request", "setTimeout", "delta", "title", "icon", "getInviteRecords", "name", "time", "avatar", "getRewardRecords", "amount", "getActivityRules", "showMore", "itemList", "success", "showTarget", "showRules", "content", "showCancel", "generatePoster", "generateQRCode", "reject", "scene", "page", "filePath", "encoding", "resolve", "fail", "generateQRCodeFallback", "drawPoster", "gradient", "ctx", "paragraphs", "currentY", "defaultRules", "canvasId", "drawRoundedRect", "fillRoundedRect", "wrapText", "lines", "currentLine", "drawMultilineText", "closePosterModal", "savePosterLocal", "shareToWechat", "shareToMoments", "withdraw", "url", "switchTab", "shareToFriend", "provider", "type", "href", "summary", "imageUrl", "collectActivity", "getImageInfo", "width", "imgInfo"], "mappings": "gKAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,4CACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,yHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,cAAcC,QAC3BR,EAAIS,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLT,GAAIA,MAKRU,GAAmB,EACnBC,EAAkB,GACtBhB,EAAOiB,eAAgB,G,iCC1CvB,yHAA2qB,eAAG,G,6HC2L9qB,eACA,W,EAEA,CACAN,gBACA,OACAO,mBACAC,gBACAC,YACAC,mBACAC,mBACAC,gBACAC,iBACAC,mBACAC,gBACAC,aACAC,cACAC,WACAC,SACAC,eAEAC,iBACAxB,iBACAyB,mBAGAC,UACAC,uBACA,OACAC,8BACAC,wCAIAC,kBACA,qBACA,sBACA,oBACA,wBACA,wBACA,yBAEAC,kBAEA,oBACA,wBACA,yBAEAC,SAEAC,yBACA,IACA,4BACA,uCAEA,0CACA,QACA,WAEA,+BACA,yCACA,SACA3C,6BACA,wBACA,uBAKA4C,0BACA,IACA,4BACA,+BACA,6BAGA,UACA,MACA,sBAEA,IACAC,IACA,MACAA,KAIA,+DAGA,mDAEA7C,4DACAA,gFACA,SACAA,6BAEA,qBACA,wBAKA8C,kBACAC,kBAIAC,wBAAA,WACAC,8CACA,0BACA,oCACA,8CACAjD,yCAEA,mBAIA,GAHAA,6BAGA,gBAQA,OAPAA,oCAEAkD,uBACAH,gBACAI,YAEA,KAIAJ,aACAK,eACAC,kBAMAC,4BAAA,WACAL,wDACA,0BAEA,+CACAM,wBACAC,sBACAC,iDAEAzD,6CAEA,mBACAA,6BACA+C,aACAK,iBACAC,kBAMAK,4BAAA,WACAT,mDACA,0BAEA,+CACAM,kBACAC,sBACAC,mCACAE,2BAEA3D,6CAEA,mBACAA,6BACA+C,aACAK,iBACAC,kBAMAO,4BAAA,WACAX,iEACA,kBACA,0BACAjD,6CAEA,mBACAA,6BAEA,8EAKA6D,oBAAA,WACAd,mBACAe,wBACAC,oBACA,eAEA,kBACA,gBAEA,wBAOAC,sBACAjB,aACAK,cACAC,eAKAY,qBACA,8GACAlB,aACAK,aACAc,UACAC,iBAKAC,0BAAA,WAEA,sBAEA,wBACA,wBAGA,uCAEA,wBACA,qBAEA,kBAEA,yCACA,wBACA,sBAMAC,0BAAA,WACA,kCAEA,4BAEA,GADArE,yBACA,EAGA,OAFAA,sCACAsE,WAKA,QACA,sBAGArB,iCACAsB,QACAC,OACA,gBACA,kBAEA,2BAEA,YACA,uEAGAtF,oCACAuF,WACA5D,OACA6D,kBACAX,mBACA,eACA/D,2BACA2E,KAEAC,iBACA5E,6BACAsE,aAIAtE,+BACAsE,mBAEA,mBACAtE,6BACAsE,YAMAO,kCAAA,WACA,kCAEA,4BACA,GAOA7E,oCAQA,yCACA2E,KAfAL,eAoBAQ,sBAAA,WACA,6CAGA,KAGA,kDACAC,4BACAA,4BACAC,kBAGA,iEAGA,wCACAA,kFAGA,2BACA,oCACA,yBACA,sCAGA,sCAGAA,0BACA,mCAGA,mCACA,YACA,8BAGA,gEACAA,uBAGAA,0BACAA,iDACAA,yBACA,4BACAA,uCAGA,4CACA,oCACA,sBACA,yBAGAA,0BACA,mCAGAA,0BACAA,iDACAA,yBACAA,yBACA,yBACAA,wCAGAA,0BACAA,iDACAA,uBACAA,yBAGA,WACA,YACA,oCACA,wCACA,wBAGA,IACA,uBAEA,4EAEAC,uBACA,aAEA,8CACAC,gCAGA,CAEA,OACA,uBACA,iBAGAC,uBACA,uCACAD,2BAKAF,gCAEAA,sBACA,qBAEAjC,wBACAqC,wBACArB,oBACA,mCAEA,OAKAsB,sCACAL,cACAA,gBACAA,kBACAA,kCACAA,oBACAA,sCACAA,kBACAA,kCACAA,gBACAA,8BACAA,eAIAM,sCACA,kCACAN,UAIAO,yBAKA,IAJA,kBACA,KACA,KAEA,oBACA,aACA,mBACA,UAEA,aACAC,UACAC,QAEAA,IAQA,MAJA,QACAD,UAGA,GAIAE,wCACA,2BAMA,OAJAF,yBACAR,yBAGA,UAIAW,4BACA,wBACA,yBAIAC,2BACA,qBAQA7C,0BACA0B,8BACAV,mBACAhB,aACAK,aACAC,kBAGAuB,gBACA7B,aACAK,aACAC,iBAlBAN,aACAK,iBACAC,eAuBAwC,yBACA,qBAQA9C,aACAK,gBACAC,cATAN,aACAK,iBACAC,eAYAyC,0BACA,qBAQA/C,aACAK,eACAC,cATAN,aACAK,iBACAC,eAYA0C,oBAEAhD,cACAiD,kFAKAC,sBACA,kBAIAC,yBACAnD,SACAoD,kBACA5B,uBACA6B,OACAC,qCACAjD,iBACAkD,6BACAC,8CAKAC,2BACAzD,aACAK,aACAC,kBAKAoD,yBAEA,OACAC,UACApE,YAIA,IACA,4BACA,IACAqE,gBACAA,mBAEA,SACA3G,6BAGA,YAGA,c,6ECjzBA,yHAAkwC,eAAG,G", "file": "subpkg-activity/share_earn/share_earn.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg-activity/share_earn/share_earn.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./share_earn.vue?vue&type=template&id=1a709132&\"\nvar renderjs\nimport script from \"./share_earn.vue?vue&type=script&lang=js&\"\nexport * from \"./share_earn.vue?vue&type=script&lang=js&\"\nimport style0 from \"./share_earn.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg-activity/share_earn/share_earn.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./share_earn.vue?vue&type=template&id=1a709132&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.inviteRecords.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./share_earn.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./share_earn.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"share-earn-container\">\n\t\t<!-- 背景图片 -->\n\t\t<image class=\"background-image\" src=\"/subpkg-activity/static/images/one.png\" mode=\"aspectFill\"></image>\n\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"custom-navbar\" :style=\"navbarStyle\">\n\t\t\t<!-- 状态栏占位 -->\n\t\t\t<view :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\t<!-- 导航栏内容区 -->\n\t\t\t<view class=\"navbar-content\">\n\t\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\n\t\t\t\t\t<uni-icons type=\"left\" size=\"20\" color=\"#ffffff\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"page-title\">活动详情</text>\n\t\t\t\t<view class=\"navbar-right\">\n\t\t\t\t\t<view class=\"more-btn\" @click=\"showMore\">\n\t\t\t\t\t\t<view class=\"dot\"></view>\n\t\t\t\t\t\t<view class=\"dot\"></view>\n\t\t\t\t\t\t<view class=\"dot\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"target-btn\" @click=\"showTarget\">\n\t\t\t\t\t\t<uni-icons type=\"loop\" size=\"16\" color=\"#ffffff\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 浮动规则按钮 -->\n\t\t<view class=\"floating-rule-btn\" @click=\"showRules\">\n\t\t\t<text class=\"rule-text\">规则</text>\n\t\t</view>\n\t\t\n\t\t<!-- 内容区域 -->\n\t\t<view class=\"content-area\">\n\t\t\t<!-- 邀请注册和操作步骤合并模块 -->\n\t\t\t<view class=\"invite-steps-section\">\n\t\t\t\t<!-- 邀请注册模块 -->\n\t\t\t\t<view class=\"invite-module\">\n\t\t\t\t\t<view class=\"section-title-with-bar\">\n\t\t\t\t\t\t<view class=\"title-bar\"></view>\n\t\t\t\t\t\t<text class=\"section-title\">邀请注册</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"invite-reward\">\n\t\t\t\t\t\t<text class=\"reward-label\">您将获得奖励</text>\n\t\t\t\t\t\t<view class=\"reward-info\">\n\t\t\t\t\t\t\t<text style=\"margin-right: 10rpx;\">提现</text>\n\t\t\t\t\t\t\t<text class=\"reward-amount\">0.2</text>\n\t\t\t\t\t\t\t<text class=\"reward-unit\">元/单</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 操作步骤模块 -->\n\t\t\t\t<view class=\"steps-module\">\n\t\t\t\t\t<view class=\"section-title-with-bar\">\n\t\t\t\t\t\t<view class=\"title-bar\"></view>\n\t\t\t\t\t\t<text class=\"section-title\">操作步骤</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"steps-grid\">\n\t\t\t\t\t\t<view class=\"step-item\">\n\t\t\t\t\t\t\t<image class=\"step-icon\" src=\"/static/images/img1.png\"></image>\n\t\t\t\t\t\t\t<view class=\"step-text-container\">\n\t\t\t\t\t\t\t\t<text class=\"step-number\">01</text>\n\t\t\t\t\t\t\t\t<text class=\"step-text\">分享海报</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"step-item\">\n\t\t\t\t\t\t\t<image class=\"step-icon\" src=\"/static/images/img2.png\"></image>\n\t\t\t\t\t\t\t<view class=\"step-text-container\">\n\t\t\t\t\t\t\t\t<text class=\"step-number\">02</text>\n\t\t\t\t\t\t\t\t<text class=\"step-text\">好友注册</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"step-item\">\n\t\t\t\t\t\t\t<image class=\"step-icon\" src=\"/static/images/img3.png\"></image>\n\t\t\t\t\t\t\t<view class=\"step-text-container\">\n\t\t\t\t\t\t\t\t<text class=\"step-number\">03</text>\n\t\t\t\t\t\t\t\t<text class=\"step-text\">完成订单获得奖励</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"generate-poster-btn\" @click=\"generatePoster\">\n\t\t\t\t\t\t<text class=\"poster-btn-text\">生 成 海 报</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 当前收益模块 -->\n\t\t\t<view class=\"earnings-section\">\n\t\t\t\t<view class=\"section-title-with-bar\">\n\t\t\t\t\t<view class=\"title-bar\"></view>\n\t\t\t\t\t<text class=\"section-title\">当前收益</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"earnings-content\">\n\t\t\t\t\t<view class=\"earnings-item\">\n\t\t\t\t\t\t<text class=\"earnings-amount\">{{shareData.income}}<text class=\"earnings-unit\">元</text></text>\n\t\t\t\t\t\t<text class=\"earnings-label\">可提现金额</text>\n\t\t\t\t\t\t<text class=\"withdraw-btn\" @click=\"withdraw\">去提现</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"earnings-item\">\n\t\t\t\t\t\t<text class=\"earnings-amount\">{{shareData.shareNumber}}<text class=\"earnings-unit\">人</text></text>\n\t\t\t\t\t\t<text class=\"earnings-label\">总邀请人数</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 邀请记录和奖励记录 -->\n\t\t\t<view class=\"records-section\">\n\t\t\t\t<view class=\"tab-header\">\n\t\t\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 0 }\" @click=\"switchTab(0)\">\n\t\t\t\t\t\t<text class=\"tab-text\">邀请记录</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 1 }\" @click=\"switchTab(1)\">\n\t\t\t\t\t\t<text class=\"tab-text\">奖励记录</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"tab-content\">\n\t\t\t\t\t<view v-show=\"activeTab === 0\" class=\"record-list\">\n\t\t\t\t\t\t<view class=\"record-item\" v-for=\"(item, index) in inviteRecords\" :key=\"index\">\n\t\t\t\t\t\t\t<image class=\"record-avatar\" src=\"/static/images/user.png\"></image>\n\t\t\t\t\t\t\t<view class=\"record-info\">\n\t\t\t\t\t\t\t\t<text class=\"record-name\">{{item.name}}</text>\n\t\t\t\t\t\t\t\t<text class=\"record-time\">{{item.time}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 空状态提示 -->\n\t\t\t\t\t\t<view v-if=\"inviteRecords.length === 0\" class=\"empty-state\">\n\t\t\t\t\t\t\t<text class=\"empty-text\">暂无邀请记录</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-show=\"activeTab === 1\" class=\"record-list\">\n\t\t\t\t\t\t<view class=\"record-item\" v-for=\"(item, index) in rewardRecords\" :key=\"index\">\n\t\t\t\t\t\t\t<image class=\"record-avatar\" src=\"/static/images/user.png\"></image>\n\t\t\t\t\t\t\t<view class=\"record-info\">\n\t\t\t\t\t\t\t\t<text class=\"record-name\">{{item.name}}</text>\n\t\t\t\t\t\t\t\t<text class=\"record-time\">{{item.time}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"record-reward\">\n\t\t\t\t\t\t\t\t<text class=\"reward-label\">提现：</text>\n\t\t\t\t\t\t\t\t<text class=\"reward-amount\">+¥{{item.amount || '0.2'}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 海报生成弹窗 -->\n\t\t<view v-if=\"showPosterModal\" class=\"poster-modal\" @click=\"closePosterModal\">\n\t\t\t<!-- 海报画布区域 -->\n\t\t\t<view class=\"poster-canvas-container\" @click.stop>\n\t\t\t\t<canvas\n\t\t\t\t\tcanvas-id=\"posterCanvas\"\n\t\t\t\t\tclass=\"poster-canvas\"\n\t\t\t\t\t:style=\"{ width: canvasWidth + 'px', height: canvasHeight + 'px' }\"\n\t\t\t\t></canvas>\n\t\t\t\t<view v-if=\"!posterGenerated\" class=\"poster-loading\">\n\t\t\t\t\t<text>正在生成海报...</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部操作按钮 -->\n\t\t<view v-if=\"showPosterModal\" class=\"poster-bottom-actions\">\n\t\t\t<view class=\"action-btn\" @click=\"savePosterLocal\">\n\t\t\t\t<image class=\"action-btn-icon\" src=\"/subpkg-activity/static/images/haibao1.png\"></image>\n\t\t\t\t<text class=\"action-btn-text\">保存本地</text>\n\t\t\t</view>\n\t\t\t<!-- <view class=\"action-btn\" @click=\"shareToWechat\">\n\t\t\t\t<image class=\"action-btn-icon\" src=\"/subpkg-activity/static/images/haibao2.png\"></image>\n\t\t\t\t<text class=\"action-btn-text\">微信好友</text>\n\t\t\t</view>\n\t\t\t<view class=\"action-btn\" @click=\"shareToMoments\">\n\t\t\t\t<image class=\"action-btn-icon\" src=\"/subpkg-activity/static/images/haibao3.png\"></image>\n\t\t\t\t<text class=\"action-btn-text\">朋友圈</text>\n\t\t\t</view> -->\n\t\t</view>\n\n\t\t<!-- 取消按钮 -->\n\t\t<view v-if=\"showPosterModal\" class=\"poster-cancel-btn\" @click=\"closePosterModal\">\n\t\t\t<text class=\"cancel-text\">取消</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport auth from '../../utils/auth.js';\n\timport request from '../../utils/request.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tstatusBarHeight: 20,\n\t\t\t\tnavbarHeight: 80,\n\t\t\t\tactiveTab: 0, // 0: 邀请记录, 1: 奖励记录\n\t\t\t\tshowPosterModal: false, // 控制海报弹窗显示\n\t\t\t\tposterGenerated: false, // 海报是否生成完成\n\t\t\t\tcanvasWidth: 300, // 画布宽度 (将动态计算)\n\t\t\t\tcanvasHeight: 500, // 画布高度 (将动态计算)\n\t\t\t\tposterImagePath: '', // 生成的海报图片路径\n\t\t\t\tscreenWidth: 375, // 屏幕宽度 (默认值)\n\t\t\t\tpixelRatio: 1, // 设备像素比\n\t\t\t\tqrCodePath: '', // 小程序码图片路径\n\t\t\t\tshareData: {\n\t\t\t\t\tincome: 0.0, // 可提现金额\n\t\t\t\t\tshareNumber: 0 // 总邀请人数\n\t\t\t\t},\n\t\t\t\tactivityRules: '', // 活动规则内容\n\t\t\t\tinviteRecords: [], // 邀请记录，从接口获取\n\t\t\t\trewardRecords: [] // 奖励记录，从接口获取\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tnavbarStyle() {\n\t\t\t\treturn {\n\t\t\t\t\theight: this.navbarHeight + 'px',\n\t\t\t\t\tpaddingTop: this.statusBarHeight + 'px'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.setNavbarInfo();\n\t\t\tthis.initCanvasSize();\n\t\t\tthis.getShareData();\n\t\t\tthis.getInviteRecords();\n\t\t\tthis.getRewardRecords();\n\t\t\tthis.getActivityRules();\n\t\t},\n\t\tonShow() {\n\t\t\t// 每次页面显示时刷新数据，包括从其他页面返回\n\t\t\tthis.getShareData();\n\t\t\tthis.getInviteRecords();\n\t\t\tthis.getRewardRecords();\n\t\t},\n\t\tmethods: {\n\t\t\t// 设置导航栏信息\n\t\t\tsetNavbarInfo() {\n\t\t\t\ttry {\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\tthis.statusBarHeight = systemInfo.statusBarHeight;\n\n\t\t\t\t\tconst menuButtonInfo = uni.getMenuButtonBoundingClientRect();\n\t\t\t\t\tconst menuTop = menuButtonInfo.top;\n\t\t\t\t\tconst menuHeight = menuButtonInfo.height;\n\n\t\t\t\t\tconst contentHeight = menuHeight + (menuTop - this.statusBarHeight) * 2;\n\t\t\t\t\tthis.navbarHeight = this.statusBarHeight + contentHeight;\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取系统信息失败:', e);\n\t\t\t\t\tthis.statusBarHeight = 20;\n\t\t\t\t\tthis.navbarHeight = 60;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 初始化画布尺寸 - 根据屏幕尺寸动态计算\n\t\t\tinitCanvasSize() {\n\t\t\t\ttry {\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\tthis.screenWidth = systemInfo.screenWidth;\n\t\t\t\t\tthis.pixelRatio = systemInfo.pixelRatio;\n\n\t\t\t\t\t// 计算画布宽度：屏幕宽度的80%，但不超过400px，不小于320px\n\t\t\t\t\tconst maxWidth = 400; // 增加最大宽度\n\t\t\t\t\tconst minWidth = 320; // 增加最小宽度\n\t\t\t\t\tlet calculatedWidth = this.screenWidth * 0.8; // 增加屏幕占比\n\n\t\t\t\t\tif (calculatedWidth > maxWidth) {\n\t\t\t\t\t\tcalculatedWidth = maxWidth;\n\t\t\t\t\t} else if (calculatedWidth < minWidth) {\n\t\t\t\t\t\tcalculatedWidth = minWidth;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 考虑设备像素比，确保在高DPI屏幕上显示清晰\n\t\t\t\t\tthis.canvasWidth = Math.floor(calculatedWidth * this.pixelRatio) / this.pixelRatio;\n\n\t\t\t\t\t// 根据宽度按比例计算高度 (宽高比约为 2:3.5，增加高度)\n\t\t\t\t\tthis.canvasHeight = Math.floor(this.canvasWidth * 1.8);\n\n\t\t\t\t\tconsole.log('画布尺寸:', this.canvasWidth, 'x', this.canvasHeight);\n\t\t\t\t\tconsole.log('屏幕信息:', this.screenWidth, 'x', systemInfo.screenHeight, '像素比:', this.pixelRatio);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取屏幕信息失败:', e);\n\t\t\t\t\t// 使用默认尺寸\n\t\t\t\t\tthis.canvasWidth = 300;\n\t\t\t\t\tthis.canvasHeight = 500;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 返回上一页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\n\t\t\t// 获取分享数据\n\t\t\tgetShareData() {\n\t\t\t\trequest.get('/app/share').then(res => {\n\t\t\t\t\tif (res && res.code === 200 && res.data) {\n\t\t\t\t\t\tthis.shareData.income = res.data.income || 0.0;\n\t\t\t\t\t\tthis.shareData.shareNumber = res.data.shareNumber || 0;\n\t\t\t\t\t\tconsole.log('分享数据获取成功:', this.shareData);\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('获取分享数据失败:', err);\n\n\t\t\t\t\t// 检查是否是500错误\n\t\t\t\t\tif (err && err.code === 500) {\n\t\t\t\t\t\tconsole.log('接口返回500错误，返回上一页');\n\t\t\t\t\t\t// 延时后关闭当前页面\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack({\n\t\t\t\t\t\t\t\tdelta: 1\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 获取邀请记录\n\t\t\tgetInviteRecords() {\n\t\t\t\trequest.get('/app/share/user/list').then(res => {\n\t\t\t\t\tif (res && res.code === 200 && res.data) {\n\t\t\t\t\t\t// 处理邀请记录数据\n\t\t\t\t\t\tthis.inviteRecords = res.data.map(item => ({\n\t\t\t\t\t\t\tname: item.nickName || '微信用户',\n\t\t\t\t\t\t\ttime: item.createTime || '',\n\t\t\t\t\t\t\tavatar: item.avatar || '/static/images/avatar.png'\n\t\t\t\t\t\t}));\n\t\t\t\t\t\tconsole.log('邀请记录获取成功:', this.inviteRecords);\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('获取邀请记录失败:', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取邀请记录失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 获取奖励记录\n\t\t\tgetRewardRecords() {\n\t\t\t\trequest.get('/app/share/list').then(res => {\n\t\t\t\t\tif (res && res.code === 200 && res.rows) {\n\t\t\t\t\t\t// 处理奖励记录数据\n\t\t\t\t\t\tthis.rewardRecords = res.rows.map(item => ({\n\t\t\t\t\t\t\tname: item.name || '用户',\n\t\t\t\t\t\t\ttime: item.createTime || '',\n\t\t\t\t\t\t\tavatar: '/static/images/avatar.png', // 使用默认头像\n\t\t\t\t\t\t\tamount: item.orderAmout || 0\n\t\t\t\t\t\t}));\n\t\t\t\t\t\tconsole.log('奖励记录获取成功:', this.rewardRecords);\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('获取奖励记录失败:', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取奖励记录失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 获取活动规则\n\t\t\tgetActivityRules() {\n\t\t\t\trequest.get('/app/configure/activity_rules').then(res => {\n\t\t\t\t\tif (res && res.code === 200) {\n\t\t\t\t\t\tthis.activityRules = res.msg || '';\n\t\t\t\t\t\tconsole.log('活动规则获取成功:', this.activityRules);\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('获取活动规则失败:', err);\n\t\t\t\t\t// 使用默认规则内容\n\t\t\t\t\tthis.activityRules = '邀请好友注册成功后，好友下单即可获得奖励\\n每邀请一位好友最多可获得0.2元奖励\\n奖励将在好友完成首单后发放';\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 显示更多菜单\n\t\t\tshowMore() {\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: ['分享给朋友', '收藏'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.tapIndex === 0) {\n\t\t\t\t\t\t\t// 分享给朋友\n\t\t\t\t\t\t\tthis.shareToFriend();\n\t\t\t\t\t\t} else if (res.tapIndex === 1) {\n\t\t\t\t\t\t\t// 收藏\n\t\t\t\t\t\t\tthis.collectActivity();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 显示目标\n\t\t\tshowTarget() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '功能开发中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 显示规则\n\t\t\tshowRules() {\n\t\t\t\tconst content = this.activityRules || '1. 邀请好友注册成功后，好友下单即可获得奖励\\n2. 每邀请一位好友最多可获得0.2元奖励\\n3. 奖励将在好友完成首单后发放\\n4. 活动最终解释权归平台所有';\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '活动规则',\n\t\t\t\t\tcontent: content,\n\t\t\t\t\tshowCancel: false\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 生成海报\n\t\t\tgeneratePoster() {\n\t\t\t\t// 重新计算画布尺寸，确保适配当前屏幕\n\t\t\t\tthis.initCanvasSize();\n\n\t\t\t\tthis.showPosterModal = true;\n\t\t\t\tthis.posterGenerated = false;\n\n\t\t\t\t// 先生成小程序码，再绘制海报\n\t\t\t\tthis.generateQRCode().then(() => {\n\t\t\t\t\t// 延迟一下再开始绘制，确保DOM已渲染\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.drawPoster();\n\t\t\t\t\t});\n\t\t\t\t}).catch(() => {\n\t\t\t\t\t// 如果生成小程序码失败，使用默认图片\n\t\t\t\t\tthis.qrCodePath = '/static/images/avatar.png';\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.drawPoster();\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 生成小程序码\n\t\t\tgenerateQRCode() {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\t// 获取当前用户ID\n\t\t\t\t\tconst userId = auth.getUserId();\n\t\t\t\t\tconsole.log('userId', userId);\n\t\t\t\t\tif (!userId) {\n\t\t\t\t\t\tconsole.warn('用户未登录，无法生成专属小程序码');\n\t\t\t\t\t\treject('用户未登录');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 构建小程序码参数\n\t\t\t\t\tconst scene = userId; // 场景值，直接使用用户ID（后端会处理为userId参数）\n\t\t\t\t\tconst page = 'pages/login/login'; // 跳转到登录页\n\n\t\t\t\t\t// 调用后端接口生成小程序码\n\t\t\t\t\trequest.post('/app/share/url', {\n\t\t\t\t\t\tscene: scene,\n\t\t\t\t\t\tpage: page,\n\t\t\t\t\t\t\"check_path\": false\n\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t// 处理/app/share/url接口返回的base64数据\n\t\t\t\t\t\tif (res && res.code === 200 && res.msg) {\n\t\t\t\t\t\t\t// res.msg包含base64格式的图片数据\n\t\t\t\t\t\t\tconst base64Data = res.msg;\n\t\t\t\t\t\t\tconst tempFilePath = `${wx.env.USER_DATA_PATH}/qrcode_${Date.now()}.png`;\n\n\t\t\t\t\t\t\t// 写入临时文件\n\t\t\t\t\t\t\twx.getFileSystemManager().writeFile({\n\t\t\t\t\t\t\t\tfilePath: tempFilePath,\n\t\t\t\t\t\t\t\tdata: base64Data,\n\t\t\t\t\t\t\t\tencoding: 'base64',\n\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\tthis.qrCodePath = tempFilePath;\n\t\t\t\t\t\t\t\t\tconsole.log('小程序码生成成功:', tempFilePath);\n\t\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tconsole.error('保存小程序码失败:', err);\n\t\t\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.error('小程序码数据格式错误:', res);\n\t\t\t\t\t\t\treject('小程序码数据格式错误');\n\t\t\t\t\t\t}\n\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\tconsole.error('生成小程序码失败:', err);\n\t\t\t\t\t\treject(err);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 备用方案：使用微信小程序原生API生成小程序码（仅在开发环境或特定条件下使用）\n\t\t\tgenerateQRCodeFallback() {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\t// 获取当前用户ID\n\t\t\t\t\tconst userId = auth.getUserId();\n\t\t\t\t\tif (!userId) {\n\t\t\t\t\t\treject('用户未登录');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 注意：这个方法需要在真实的微信小程序环境中才能使用\n\t\t\t\t\t// 并且需要后端配合生成小程序码\n\t\t\t\t\tconsole.log('使用备用方案生成小程序码，用户ID:', userId);\n\n\t\t\t\t\t// 这里可以实现其他备用方案，比如：\n\t\t\t\t\t// 1. 生成普通二维码包含小程序链接\n\t\t\t\t\t// 2. 使用第三方二维码生成服务\n\t\t\t\t\t// 3. 预设的静态小程序码\n\n\t\t\t\t\t// 暂时使用默认图片\n\t\t\t\t\tthis.qrCodePath = '/static/images/avatar.png';\n\t\t\t\t\tresolve();\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 绘制海报\n\t\t\tdrawPoster() {\n\t\t\t\tconst ctx = uni.createCanvasContext('posterCanvas', this);\n\n\t\t\t\t// 绘制整体海报圆角背景\n\t\t\t\tconst cornerRadius = 20;\n\n\t\t\t\t// 设置画布背景色为蓝色渐变背景\n\t\t\t\tconst gradient = ctx.createLinearGradient(0, 0, 0, this.canvasHeight);\n\t\t\t\tgradient.addColorStop(0, '#096ff8');\n\t\t\t\tgradient.addColorStop(1, '#096ff8');\n\t\t\t\tctx.setFillStyle(gradient);\n\n\t\t\t\t// 绘制圆角背景\n\t\t\t\tthis.fillRoundedRect(ctx, 0, 0, this.canvasWidth, this.canvasHeight, cornerRadius);\n\n\t\t\t\t// 绘制顶部图片 (邀好友得好礼的完整图片) - 按比例计算高度\n\t\t\t\tconst headerHeight = Math.floor(this.canvasHeight * 0.35); // 高度占画布的35%\n\t\t\t\tctx.drawImage('/subpkg-activity/static/images/hbheader.png', 0, 0, this.canvasWidth, headerHeight);\n\n\t\t\t\t// 绘制二维码容器 (白色圆角背景) - 增加宽度到80%和高度\n\t\t\t\tconst qrContainerWidth = this.canvasWidth * 0.85; // 宽度85%\n\t\t\t\tconst qrContainerHeight = Math.floor(this.canvasHeight * 0.45); // 高度占画布的35%\n\t\t\t\tconst qrContainerX = (this.canvasWidth - qrContainerWidth) / 2;\n\t\t\t\tconst qrContainerY = headerHeight + Math.floor(this.canvasHeight * 0.04); // 间距占画布的4%\n\n\t\t\t\t// 计算白色背景区域的实际位置和尺寸\n\t\t\t\tconst whiteContainerY = qrContainerY - Math.floor(this.canvasHeight * 0.12);\n\n\t\t\t\t// 绘制二维码容器白色圆角背景\n\t\t\t\tctx.setFillStyle('#ffffff');\n\t\t\t\tthis.fillRoundedRect(ctx, qrContainerX, whiteContainerY, qrContainerWidth, qrContainerHeight, 15);\n\n\t\t\t\t// 绘制二维码 - 占白色背景区域的80%\n\t\t\t\tconst qrSize = Math.floor(Math.min(qrContainerWidth, qrContainerHeight) * 0.8); // 二维码占白色背景区域的80%\n\t\t\t\tconst qrX = qrContainerX + (qrContainerWidth - qrSize) / 2; // 水平居中\n\t\t\t\tconst qrY = whiteContainerY + (qrContainerHeight - qrSize) / 2 - Math.floor(qrContainerHeight * 0.05); // 垂直居中，上移为文字留空间\n\n\t\t\t\t// 绘制小程序码\n\t\t\t\tconst qrImagePath = this.qrCodePath || '/subpkg-activity/static/images/haibao1.png'; // 使用生成的小程序码或默认图片\n\t\t\t\tctx.drawImage(qrImagePath, qrX, qrY, qrSize, qrSize);\n\n\t\t\t\t// 绘制二维码下方文字 \"分享码\" - 在白色背景区域的最底部，留出小距离\n\t\t\t\tctx.setFillStyle('#000000'); // 使用黑色\n\t\t\t\tctx.setFontSize(Math.floor(this.canvasWidth * 0.045));\n\t\t\t\tctx.setTextAlign('center');\n\t\t\t\tconst textY = whiteContainerY + qrContainerHeight - Math.floor(qrContainerHeight * 0.04); // 在白色背景底部留出小距离\n\t\t\t\tctx.fillText('分享码', this.canvasWidth / 2, textY);\n\n\t\t\t\t// 绘制活动规则容器 - 增加圆角，按比例计算尺寸\n\t\t\t\tconst rulesContainerY = qrContainerY + qrContainerHeight - Math.floor(this.canvasHeight * 0.08);\n\t\t\t\tconst rulesContainerHeight = Math.floor(this.canvasHeight * 0.22); // 高度占画布的22%\n\t\t\t\tconst rulesContainerWidth = this.canvasWidth * 0.9; // 与二维码容器同宽\n\t\t\t\tconst rulesContainerX = (this.canvasWidth - rulesContainerWidth) / 2;\n\n\t\t\t\t// 绘制活动规则白色圆角背景\n\t\t\t\tctx.setFillStyle('#ffffff');\n\t\t\t\tthis.fillRoundedRect(ctx, rulesContainerX, rulesContainerY, rulesContainerWidth, rulesContainerHeight, 15);\n\n\t\t\t\t// 绘制活动规则标题 - 按比例计算字体大小和位置\n\t\t\t\tctx.setFillStyle('#000000'); // 使用黑色\n\t\t\t\tctx.setFontSize(Math.floor(this.canvasWidth * 0.055)); // 字体大小为画布宽度的5.5%\n\t\t\t\tctx.setTextAlign('center');\n\t\t\t\tctx.setTextBaseline('top'); // 改为顶部对齐\n\t\t\t\tconst titleY = rulesContainerY + Math.floor(rulesContainerHeight * 0.1); // 标题位置：容器顶部10%\n\t\t\t\tctx.fillText('活动规则', this.canvasWidth / 2, titleY);\n\n\t\t\t\t// 绘制活动规则内容 - 按比例计算字体大小和位置，支持自动换行\n\t\t\t\tctx.setFillStyle('#666666');\n\t\t\t\tctx.setFontSize(Math.floor(this.canvasWidth * 0.045)); // 稍微减小字体大小，确保文字能完整显示\n\t\t\t\tctx.setTextAlign('left'); // 改为左对齐，便于换行显示\n\t\t\t\tctx.setTextBaseline('top'); // 改为顶部对齐\n\n\t\t\t\t// 计算文字区域的参数\n\t\t\t\tconst textMaxWidth = rulesContainerWidth * 0.9; // 白色背景宽度的90%\n\t\t\t\tconst textStartX = rulesContainerX + (rulesContainerWidth - textMaxWidth) / 2; // 文字区域左边界\n\t\t\t\tconst lineHeight = Math.floor(this.canvasWidth * 0.045); // 行高\n\t\t\t\tconst titleHeight = Math.floor(this.canvasWidth * 0.055 * 1.2); // 标题高度（字体大小 + 间距）\n\t\t\t\tconst textStartY = titleY + titleHeight + Math.floor(rulesContainerHeight * 0.05); // 内容开始位置：标题下方 + 5%间距\n\n\t\t\t\t// 处理活动规则文本，支持自动换行\n\t\t\t\tlet currentY = textStartY;\n\t\t\t\tif (this.activityRules) {\n\t\t\t\t\t// 按换行符分割文本\n\t\t\t\t\tconst paragraphs = this.activityRules.split('\\n').filter(line => line.trim());\n\n\t\t\t\t\tparagraphs.forEach((paragraph) => {\n\t\t\t\t\t\tif (paragraph.trim()) {\n\t\t\t\t\t\t\t// 为每个段落绘制自动换行文字\n\t\t\t\t\t\t\tconst linesDrawn = this.drawMultilineText(ctx, paragraph.trim(), textStartX, currentY, textMaxWidth, lineHeight);\n\t\t\t\t\t\t\tcurrentY += linesDrawn * lineHeight + Math.floor(lineHeight * 0.3); // 段落间距\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 默认规则内容\n\t\t\t\t\tconst defaultRules = [\n\t\t\t\t\t\t'邀请好友注册成功后，好友下单即可获得奖励',\n\t\t\t\t\t\t'奖励将在好友完成首单后发放'\n\t\t\t\t\t];\n\n\t\t\t\t\tdefaultRules.forEach((rule) => {\n\t\t\t\t\t\tconst linesDrawn = this.drawMultilineText(ctx, rule, textStartX, currentY, textMaxWidth, lineHeight);\n\t\t\t\t\t\tcurrentY += linesDrawn * lineHeight + Math.floor(lineHeight * 0.3); // 段落间距\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\t// 重置文字基线，确保后续绘制正常\n\t\t\t\tctx.setTextBaseline('alphabetic');\n\n\t\t\t\tctx.draw(false, () => {\n\t\t\t\t\tthis.posterGenerated = true;\n\t\t\t\t\t// 生成图片\n\t\t\t\t\tuni.canvasToTempFilePath({\n\t\t\t\t\t\tcanvasId: 'posterCanvas',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tthis.posterImagePath = res.tempFilePath;\n\t\t\t\t\t\t}\n\t\t\t\t\t}, this);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 绘制圆角矩形路径\n\t\t\tdrawRoundedRect(ctx, x, y, width, height, radius) {\n\t\t\t\tctx.beginPath();\n\t\t\t\tctx.moveTo(x + radius, y);\n\t\t\t\tctx.lineTo(x + width - radius, y);\n\t\t\t\tctx.quadraticCurveTo(x + width, y, x + width, y + radius);\n\t\t\t\tctx.lineTo(x + width, y + height - radius);\n\t\t\t\tctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);\n\t\t\t\tctx.lineTo(x + radius, y + height);\n\t\t\t\tctx.quadraticCurveTo(x, y + height, x, y + height - radius);\n\t\t\t\tctx.lineTo(x, y + radius);\n\t\t\t\tctx.quadraticCurveTo(x, y, x + radius, y);\n\t\t\t\tctx.closePath();\n\t\t\t},\n\n\t\t\t// 填充圆角矩形\n\t\t\tfillRoundedRect(ctx, x, y, width, height, radius) {\n\t\t\t\tthis.drawRoundedRect(ctx, x, y, width, height, radius);\n\t\t\t\tctx.fill();\n\t\t\t},\n\n\t\t\t// 文字自动换行方法\n\t\t\twrapText(ctx, text, maxWidth) {\n\t\t\t\tconst words = text.split('');\n\t\t\t\tconst lines = [];\n\t\t\t\tlet currentLine = '';\n\n\t\t\t\tfor (let i = 0; i < words.length; i++) {\n\t\t\t\t\tconst testLine = currentLine + words[i];\n\t\t\t\t\tconst metrics = ctx.measureText(testLine);\n\t\t\t\t\tconst testWidth = metrics.width;\n\n\t\t\t\t\tif (testWidth > maxWidth && currentLine !== '') {\n\t\t\t\t\t\tlines.push(currentLine);\n\t\t\t\t\t\tcurrentLine = words[i];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcurrentLine = testLine;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (currentLine !== '') {\n\t\t\t\t\tlines.push(currentLine);\n\t\t\t\t}\n\n\t\t\t\treturn lines;\n\t\t\t},\n\n\t\t\t// 绘制多行文字\n\t\t\tdrawMultilineText(ctx, text, x, y, maxWidth, lineHeight) {\n\t\t\t\tconst lines = this.wrapText(ctx, text, maxWidth);\n\n\t\t\t\tlines.forEach((line, index) => {\n\t\t\t\t\tctx.fillText(line, x, y + (index * lineHeight));\n\t\t\t\t});\n\n\t\t\t\treturn lines.length; // 返回行数\n\t\t\t},\n\n\t\t\t// 关闭海报弹窗\n\t\t\tclosePosterModal() {\n\t\t\t\tthis.showPosterModal = false;\n\t\t\t\tthis.posterGenerated = false;\n\t\t\t},\n\n\t\t\t// 保存到本地\n\t\t\tsavePosterLocal() {\n\t\t\t\tif (!this.posterImagePath) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '海报还未生成完成',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\t\tfilePath: this.posterImagePath,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '保存成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '保存失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 分享到微信好友\n\t\t\tshareToWechat() {\n\t\t\t\tif (!this.posterImagePath) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '海报还未生成完成',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '分享到微信好友',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 分享到朋友圈\n\t\t\tshareToMoments() {\n\t\t\t\tif (!this.posterImagePath) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '海报还未生成完成',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '分享到朋友圈',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 提现\n\t\t\twithdraw() {\n\t\t\t\t// 跳转到提现页面，传递可提现金额\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/subpkg-activity/withdraw/withdraw?amount=${this.shareData.income}`\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 切换标签\n\t\t\tswitchTab(index) {\n\t\t\t\tthis.activeTab = index;\n\t\t\t},\n\t\t\t\n\t\t\t// 分享给朋友\n\t\t\tshareToFriend() {\n\t\t\t\tuni.share({\n\t\t\t\t\tprovider: \"weixin\",\n\t\t\t\t\tscene: \"WXSceneSession\",\n\t\t\t\t\ttype: 0,\n\t\t\t\t\thref: \"https://your-domain.com/share\",\n\t\t\t\t\ttitle: \"邀请好友，得好礼\",\n\t\t\t\t\tsummary: \"邀请好友注册成功后，好友下单即得现金\",\n\t\t\t\t\timageUrl: \"/static/images/share_poster.png\"\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 收藏活动\n\t\t\tcollectActivity() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '收藏成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 获取图片信息，用于计算图片比例\n\t\t\tgetImageInfo(src) {\n\t\t\t\t// 默认尺寸，防止图片加载失败时的异常\n\t\t\t\tlet imgInfo = {\n\t\t\t\t\twidth: 280,\n\t\t\t\t\theight: 140\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 同步获取图片信息（注意：真实场景中最好使用异步方式）\n\t\t\t\ttry {\n\t\t\t\t\tconst res = uni.getImageInfoSync(src);\n\t\t\t\t\tif (res) {\n\t\t\t\t\t\timgInfo.width = res.width;\n\t\t\t\t\t\timgInfo.height = res.height;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取图片信息失败:', e);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn imgInfo;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\npage {\n\tmin-height: 100vh;\n\tbackground-color: #F6F7F9;\n}\n\n.share-earn-container {\n\tmin-height: 100vh;\n\tposition: relative;\n}\n\n/* 背景图片样式 */\n.background-image {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 490rpx;\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\tz-index: 999;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.navbar-content {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\theight: 44px;\n\tpadding: 0 30rpx;\n}\n\n.navbar-left {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.page-title {\n\tcolor: #ffffff;\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n}\n\n.navbar-right {\n\tdisplay: flex;\n\talign-items: center;\n\twidth: 120rpx;\n\tjustify-content: space-between;\n}\n\n.more-btn {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 40rpx;\n\theight: 40rpx;\n}\n\n.dot {\n\twidth: 6rpx;\n\theight: 6rpx;\n\tbackground-color: #ffffff;\n\tborder-radius: 50%;\n\tmargin: 2rpx 0;\n}\n\n.target-btn {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n/* 浮动规则按钮 */\n.floating-rule-btn {\n\tposition: fixed;\n\tright: 0;\n\ttop: 300rpx;\n\twidth: 80rpx;\n\theight: 60rpx;\n\tbackground-color: rgba(255, 255, 255);\n\tborder-radius: 20rpx 0 0 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 998;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.rule-text {\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\tfont-weight: 500;\n}\n\n/* 内容区域 */\n.content-area {\n\twidth: 100%;\n\tbox-sizing: border-box;\n\tpadding: 0 30rpx;\n\tpadding-top: 465rpx; /* 为背景图片留出空间 */\n\tbackground-color: #1476F9;\n}\n\n/* 邀请注册和操作步骤合并模块 */\n.invite-steps-section {\n\tbackground: linear-gradient(180deg, #FFFFFF 0%, #D2EAFF 100%);\n\tborder-radius: 24rpx;\n\tpadding: 40rpx 30rpx;\n\tmargin-bottom: 20rpx;\n\tmargin-top: -50rpx; /* 覆盖背景图片底部一小部分 */\n\tposition: relative;\n\tz-index: 1;\n}\n\n/* 带左侧蓝色柱状条的标题 */\n.section-title-with-bar {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 30rpx;\n}\n\n.title-bar {\n\twidth: 6rpx;\n\theight: 32rpx;\n\tbackground-color: #3B99FC;\n\tborder-radius: 3rpx;\n\tmargin-right: 16rpx;\n}\n\n.section-title {\n\tfont-size: 36rpx;\n\tcolor: #000000;\n\tfont-weight: bold;\n}\n\n/* 邀请注册模块 */\n.invite-module {\n\tmargin-bottom: 40rpx;\n}\n\n.invite-reward {\n\tdisplay: flex;\n\talign-items: baseline;\n\tjustify-content: space-between;\n\tbackground-color: #D4EBFF;\n\tpadding: 20rpx 30rpx;\n\tborder-radius: 16rpx;\n}\n\n.reward-label {\n\tfont-size: 28rpx;\n\tcolor: #000000;\n}\n\n.reward-info {\n\tdisplay: flex;\n\talign-items: baseline;\n}\n\n.reward-amount {\n\tfont-size: 48rpx;\n\tcolor: #FF4757;\n\tfont-weight: bold;\n}\n\n.reward-unit {\n\tfont-size: 24rpx;\n\tcolor: #909399;\n\tfont-weight: normal;\n\tmargin-left: 4rpx;\n}\n\n/* 操作步骤模块 */\n.steps-module {\n\t/* 操作步骤模块样式 */\n}\n\n.steps-grid {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tmargin-bottom: 40rpx;\n}\n\n.step-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\twidth: 33.33%;\n}\n\n.step-icon {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\tmargin-bottom: 16rpx;\n}\n\n.step-text-container {\n\tdisplay: flex;\n\talign-items: center;\n\tflex-direction: row;\n}\n\n.step-number {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tmargin-right: 8rpx;\n}\n\n.step-text {\n\tfont-size: 24rpx;\n\tcolor: #606266;\n\ttext-align: center;\n\tline-height: 1.4;\n\twidth: 100rpx;\n}\n\n.generate-poster-btn {\n\twidth: 100%;\n\theight: 88rpx;\n\tbackground: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);\n\tborder-radius: 44rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.poster-btn-text {\n\tfont-size: 32rpx;\n\tcolor: #ffffff;\n\tfont-weight: bold;\n\tletter-spacing: 8rpx; /* 字符间距 */\n}\n\n/* 当前收益模块 */\n.earnings-section {\n\tbackground: linear-gradient(180deg, #FFFFFF 0%, #D2EAFF 100%);\n\tborder-radius: 24rpx;\n\tpadding: 40rpx 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.earnings-content {\n\tdisplay: flex;\n\tjustify-content: space-around;\n}\n\n.earnings-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tposition: relative;\n}\n\n.earnings-amount {\n\tfont-size: 48rpx;\n\tcolor: #FF4757;\n\tfont-weight: bold;\n\tline-height: 1;\n\tmargin-bottom: 16rpx;\n}\n\n.earnings-unit {\n\tfont-size: 24rpx;\n\tcolor: #FF4757;\n\tfont-weight: normal;\n}\n\n.earnings-label {\n\tfont-size: 28rpx;\n\tcolor: #666666;\n\tmargin-bottom: 16rpx;\n}\n\n.withdraw-btn {\n\tfont-size: 24rpx;\n\tcolor: #4A90E2;\n\tpadding: 8rpx 16rpx;\n\tborder: 1rpx solid #4A90E2;\n\tborder-radius: 20rpx;\n}\n\n/* 邀请记录和奖励记录 */\n.records-section {\n\tbackground-color: #ffffff;\n\tborder-radius: 24rpx;\n\toverflow: hidden;\n\tmargin-bottom: 410rpx;\n}\n\n.tab-header {\n\tdisplay: flex;\n\tbackground-color: #ffffff;\n\tmargin-bottom: 20rpx;\n}\n\n.tab-item {\n\tflex: 1;\n\theight: 88rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n}\n\n.tab-item.active .tab-text {\n\tcolor: #000000;\n\tfont-weight: bold;\n}\n\n.tab-item.active::after {\n\tcontent: '';\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\twidth: 60rpx;\n\theight: 6rpx;\n\tbackground-color: #4A90E2;\n\tborder-radius: 3rpx;\n}\n\n.tab-text {\n\tfont-size: 32rpx;\n\tcolor: #666666;\n}\n\n.tab-content {\n\tmin-height: 400rpx;\n}\n\n.record-list {\n\tpadding: 0 30rpx;\n}\n\n.record-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 30rpx 20rpx;\n\tbackground: #D4EBFF;\n\tborder-radius: 8rpx 8rpx 8rpx 8rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.record-item:last-child {\n\tborder-bottom: none;\n}\n\n.record-avatar {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tmargin-right: 24rpx;\n}\n\n.record-info {\n\tdisplay: flex;\n\tflex-direction: column;\n\tflex: 1;\n}\n\n.record-name {\n\tfont-size: 32rpx;\n\tcolor: #333333;\n\tfont-weight: 500;\n\tmargin-bottom: 8rpx;\n}\n\n.record-time {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n}\n\n/* 奖励金额样式 */\n.record-reward {\n\tdisplay: flex;\n\talign-items: center;\n\ttext-align: right;\n}\n\n.reward-label {\n\tfont-size: 24rpx;\n\tcolor: #666666;\n\tmargin-bottom: 4rpx;\n}\n\n.reward-amount {\n\tfont-size: 28rpx;\n\tcolor: #FF4757;\n\tfont-weight: bold;\n}\n\n/* 空状态样式 */\n.empty-state {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 80rpx 0;\n}\n\n.empty-text {\n\tfont-size: 28rpx;\n\tcolor: #999999;\n}\n\n/* 海报弹窗样式 */\n.poster-modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tz-index: 9999;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n/* 移除不需要的弹窗内容样式 */\n\n.poster-canvas-container {\n\tposition: relative;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tmargin-bottom: 300rpx;\n\tpadding: 40rpx 20rpx;\n\tmin-height: 60vh;\n}\n\n.poster-canvas {\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n}\n\n.poster-loading {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\tbackground-color: rgba(255, 255, 255, 0.9);\n\tpadding: 20rpx 40rpx;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tcolor: #666666;\n}\n\n/* 移除活动规则相关样式 */\n\n/* 底部操作按钮样式 */\n.poster-bottom-actions {\n\tposition: fixed;\n\tbottom: 100rpx;\n\tleft: 0;\n\twidth: 100%;\n\tbackground-color: #ffffff;\n\tpadding: 20rpx 0;\n\tdisplay: flex;\n\tjustify-content: space-around;\n\tz-index: 10000;\n\tbox-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);\n}\n\n.action-btn {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 20rpx;\n\ttransition: all 0.3s ease;\n\tflex: 1;\n}\n\n.action-btn:active {\n\tbackground-color: rgba(0, 0, 0, 0.05);\n}\n\n.action-btn-icon {\n\twidth: 100rpx;\n\theight: 100rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.action-btn-text {\n\tfont-size: 32rpx;\n\tcolor: #333333;\n\tfont-weight: 500;\n}\n\n/* 取消按钮样式 */\n.poster-cancel-btn {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\twidth: 100%;\n\tbackground-color: #ffffff;\n\tpadding: 40rpx 0;\n\tz-index: 10000;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n}\n\n.cancel-text {\n\tfont-size: 36rpx;\n\tcolor: #333333;\n\tfont-weight: 500;\n}\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./share_earn.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./share_earn.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}