{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/pages/login/login.vue?fb8f", "webpack:///E:/购票系统/购票系统/pages/login/login.vue?797e", "webpack:///E:/购票系统/购票系统/pages/login/login.vue?1feb", "webpack:///E:/购票系统/购票系统/pages/login/login.vue?dee0", "uni-app:///pages/login/login.vue", "webpack:///E:/购票系统/购票系统/pages/login/login.vue?a4c0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "data", "isAgreed", "redirect", "inviteUserId", "onLoad", "console", "methods", "toggleAgreement", "viewUserAgreement", "uni", "title", "request", "content", "showCancel", "confirmText", "icon", "viewPrivacyPolicy", "getPhoneNumber", "mask", "provider", "success", "fail", "loginApi", "phoneCode", "requestData", "auth", "getUserInfo", "userid", "nick<PERSON><PERSON>", "phone", "avatar", "setTimeout", "url"], "mappings": "2IAAA,MAGA,aACA,WAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,iIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,wBACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,gCCRvB,wHAAsqB,eAAG,G,0HC6BzqB,eACA,W,EAEA,CACAC,gBACA,OACAC,YACAC,YACAC,kBAGAC,mBAaA,GAXA,aACA,8CAIA,WACA,2BACAC,6CAIA,SAEA,kCACA,sBACA,wCAGA,oBAEAA,iDAGAC,SAEAC,2BACA,8BAIAC,6BAEAC,eACAC,iBAIAC,iEACAF,gBACA,gBAEAA,aACAC,aACAE,sBACAC,cACAC,qBAGAL,aACAC,iBACAK,iBAGA,mBACAN,gBACAA,aACAC,iBACAK,kBAMAC,6BAEAP,eACAC,iBAIAC,oEACAF,gBACA,gBAEAA,aACAC,aACAE,sBACAC,cACAC,qBAGAL,aACAC,mBACAK,iBAGA,mBACAN,gBACAA,aACAC,mBACAK,kBAMAE,2BAAA,WACA,eASAR,eACAC,eACAQ,UAIA,cAEAT,SACAU,kBACAC,oBACA,QACAf,kCAEA,mCAEAI,gBACAA,aACAC,iBACAK,gBAIAM,gBACAZ,gBACAA,aACAC,iBACAK,kBAMAN,gBACAA,aACAC,oBACAK,gBA5CAN,aACAC,kBACAK,eAgDAO,uBAAA,WAEA,GACAC,aAIA,oBACAC,iCACAnB,+CAIAM,8GAGAc,4BAGA,mBAEA,mBACAhB,gBACAA,aACAC,wBACAK,kBAMAW,uBAAA,WAEAf,6CAEA,OACAgB,qBACAC,iCACAC,yBACAC,sBAEAL,yBAEAhB,gBACAA,aACAC,aACAK,iBAIAgB,uBAEA,eAEA,OACA,qBACA,uBACA,oBAGA,uBAEAtB,aACAuB,iBAIAvB,cACAuB,sBAKAvB,aACAuB,6BAGA,QACA,mBACAvB,gBACAA,aACAC,wBACAK,cAGAU,iCA2FA,c,4DC9WA,wHAA6vC,eAAG,G", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=template&id=b237504c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"login-container\">\r\n\t\t\r\n\t\t<!-- 中心Logo和名称 -->\r\n\t\t<view class=\"logo-container\">\r\n\t\t\t<text class=\"app-name\">鲁航校园</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 授权登录按钮 -->\r\n\t\t<view class=\"login-btn-area\">\r\n\t\t\t<button class=\"login-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">一键授权登录</button>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 协议勾选区域 -->\r\n\t\t<view class=\"agreement-area\">\r\n\t\t\t<view class=\"checkbox-container\" @click=\"toggleAgreement\">\r\n\t\t\t\t<view :class=\"['checkbox-circle', { 'checkbox-active': isAgreed }]\">\r\n\t\t\t\t\t<view v-if=\"isAgreed\" class=\"checkbox-inner\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<text class=\"agreement-text\">阅读并同意</text>\r\n\t\t\t<text class=\"agreement-link\" @click.stop=\"viewUserAgreement\">《用户协议》</text>\r\n\t\t\t<text class=\"agreement-text\">和</text>\r\n\t\t\t<text class=\"agreement-link\" @click.stop=\"viewPrivacyPolicy\">《隐私政策》</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport auth from '../../utils/auth.js';\r\n\timport request from '../../utils/request.js';\r\n\t\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisAgreed: false, // 是否同意协议\r\n\t\t\t\tredirect: '', // 登录后重定向的页面\r\n\t\t\t\tinviteUserId: '', // 邀请用户ID\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// 获取登录后需要跳转的页面\r\n\t\t\tif (options.redirect) {\r\n\t\t\t\tthis.redirect = decodeURIComponent(options.redirect);\r\n\t\t\t}\r\n\r\n\t\t\t// 获取邀请用户ID（从小程序码扫码或分享链接进入）\r\n\t\t\tif (options.userId) {\r\n\t\t\t\tthis.inviteUserId = options.userId;\r\n\t\t\t\tconsole.log('检测到邀请用户ID:', this.inviteUserId);\r\n\t\t\t}\r\n\r\n\t\t\t// 处理场景值（从小程序码扫码进入时的参数）\r\n\t\t\tif (options.scene) {\r\n\t\t\t\t// 解析场景值，格式可能是 \"userId=123\" 或直接是 \"123\"\r\n\t\t\t\tconst scene = decodeURIComponent(options.scene);\r\n\t\t\t\tif (scene.includes('userId=')) {\r\n\t\t\t\t\tthis.inviteUserId = scene.split('userId=')[1];\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果场景值直接是用户ID\r\n\t\t\t\t\tthis.inviteUserId = scene;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log('从场景值获取邀请用户ID:', this.inviteUserId);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 切换协议同意状态\r\n\t\t\ttoggleAgreement() {\r\n\t\t\t\tthis.isAgreed = !this.isAgreed;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 查看用户协议\r\n\t\t\tviewUserAgreement() {\r\n\t\t\t\t// 显示加载中\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 请求用户协议内容\r\n\t\t\t\trequest.get('/app/configure/user_agreement').then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tif (res && res.code === 200) {\r\n\t\t\t\t\t\t// 使用弹窗展示内容\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '用户协议',\r\n\t\t\t\t\t\t\tcontent: res.msg || '暂无内容',\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tconfirmText: '我知道了'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '获取协议内容失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取协议内容失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 查看隐私政策\r\n\t\t\tviewPrivacyPolicy() {\r\n\t\t\t\t// 显示加载中\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 请求隐私政策内容\r\n\t\t\t\trequest.get('/app/configure/privacy_agreement').then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tif (res && res.code === 200) {\r\n\t\t\t\t\t\t// 使用弹窗展示内容\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '隐私政策',\r\n\t\t\t\t\t\t\tcontent: res.msg || '暂无内容',\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tconfirmText: '我知道了'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '获取隐私政策内容失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取隐私政策内容失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取手机号\r\n\t\t\tgetPhoneNumber(e) {\r\n\t\t\t\tif (!this.isAgreed) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先阅读并同意协议',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 显示加载中\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '登录中...',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 获取手机号成功\r\n\t\t\t\tif (e.detail.code) {\r\n\t\t\t\t\t// 获取登录凭证\r\n\t\t\t\t\tuni.login({\r\n\t\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\t\tsuccess: (loginRes) => {\r\n\t\t\t\t\t\t\tif (loginRes.code) {\r\n                                console.log(loginRes.code, e.detail.code);\r\n\t\t\t\t\t\t\t\t// 直接调用后端登录接口\r\n\t\t\t\t\t\t\t\tthis.loginApi(loginRes.code, e.detail.code);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '获取登录凭证失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '登录失败，请重试',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 用户拒绝授权\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取手机号失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 调用登录API\r\n\t\t\tloginApi(jsCode, phoneCode) {\r\n\t\t\t\t// 构建请求参数\r\n\t\t\t\tconst requestData = {\r\n\t\t\t\t\tphoneCode: phoneCode\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 如果有邀请用户ID，添加到请求参数中\r\n\t\t\t\tif (this.inviteUserId) {\r\n\t\t\t\t\trequestData.inviteUserId = this.inviteUserId;\r\n\t\t\t\t\tconsole.log('登录时传递邀请用户ID:', this.inviteUserId);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 调用后端登录接口\r\n\t\t\t\trequest.post('/app/login?phoneCode='+ phoneCode +'&jsCode='+ jsCode + '&inviteUserId=' + this.inviteUserId, requestData).then(res => {\r\n\r\n\t\t\t\t\t// 保存token\r\n\t\t\t\t\tauth.setToken(res.token);\r\n\r\n\t\t\t\t\t// 获取token后，立即调用/app/user接口获取用户信息\r\n\t\t\t\t\tthis.getUserInfo();\r\n\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: err.msg || '登录失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 获取用户信息\r\n\t\t\tgetUserInfo() {\r\n\t\t\t\t// 调用/app/user接口获取用户详细信息\r\n\t\t\t\trequest.get('/app/user').then(res => {\r\n\t\t\t\t\t// 保存用户信息，包含userid\r\n\t\t\t\t\tconst userInfo = {\r\n\t\t\t\t\t\tuserid: res.data.userId, // 根据接口返回字段调整\r\n\t\t\t\t\t\tnickName:  res.data.nickName || '微信用户',\r\n\t\t\t\t\t\tphone: res.data.phonenumber,\r\n\t\t\t\t\t\tavatar: res.data.avatar\r\n\t\t\t\t\t};\r\n\t\t\t\t\tauth.setUserInfo(userInfo);\r\n\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 登录成功后跳转\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t// 如果有重定向页面，跳转到该页面\r\n\t\t\t\t\t\tif (this.redirect) {\r\n\t\t\t\t\t\t\t// 判断是否为 tabbar 页面\r\n\t\t\t\t\t\t\tconst tabbarPages = [\r\n\t\t\t\t\t\t\t\t'/pages/index/index',\r\n\t\t\t\t\t\t\t\t'/pages/record/record',\r\n\t\t\t\t\t\t\t\t'/pages/mine/mine'\r\n\t\t\t\t\t\t\t];\r\n\r\n\t\t\t\t\t\t\tif (tabbarPages.includes(this.redirect)) {\r\n\t\t\t\t\t\t\t\t// 如果是 tabbar 页面，使用 switchTab\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: this.redirect\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 如果不是 tabbar 页面，使用 redirectTo\r\n\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\turl: this.redirect\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 默认跳转到首页\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: err.msg || '获取用户信息失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 即使获取用户信息失败，也清除token，要求重新登录\r\n\t\t\t\t\tauth.clearLoginInfo();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 这些方法已不再使用，可以删除\r\n\t\t\t/*\r\n\t\t\t// 获取用户信息\r\n\t\t\tgetUserProfile(phoneCode) {\r\n\t\t\t\t// 调用微信getUserProfile获取用户信息\r\n\t\t\t\tuni.getUserProfile({\r\n\t\t\t\t\tdesc: '用于完善用户资料', // 声明获取用户个人信息后的用途\r\n\t\t\t\t\tsuccess: (profileRes) => {\r\n\t\t\t\t\t\t// 获取用户信息成功\r\n\t\t\t\t\t\tconst userInfo = {\r\n\t\t\t\t\t\t\tname: profileRes.userInfo.nickName,\r\n\t\t\t\t\t\t\tavatar: profileRes.userInfo.avatarUrl,\r\n\t\t\t\t\t\t\tphone: '' // 手机号需要通过后端解密获取\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 调用登录接口\r\n\t\t\t\t\t\tthis.login(userInfo, phoneCode);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '授权失败，请重试',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tconsole.error('获取用户信息失败:', err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 登录方法\r\n\t\t\tlogin(userInfo, phoneCode) {\r\n\t\t\t\t// 获取微信登录凭证\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\tsuccess: (loginRes) => {\r\n\t\t\t\t\t\tif (loginRes.code) {\r\n\t\t\t\t\t\t\t// 调用后端登录接口\r\n\t\t\t\t\t\t\t// 实际开发中替换为真实接口\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t// 模拟请求成功\r\n\t\t\t\t\t\t\t\t// 模拟手机号(实际中应该由后端解密获取)\r\n\t\t\t\t\t\t\t\tuserInfo.phone = '133****5678';\r\n\t\t\t\t\t\t\t\t// 保存token\r\n\t\t\t\t\t\t\t\tauth.setToken('mock_token_123456');\r\n\t\t\t\t\t\t\t\t// 保存用户信息\r\n\t\t\t\t\t\t\t\tauth.setUserInfo(userInfo);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 登录成功后跳转\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t// 如果有重定向页面，跳转到该页面\r\n\t\t\t\t\t\t\t\t\tif (this.redirect) {\r\n\t\t\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: this.redirect\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t// 默认跳转到首页\r\n\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '获取登录凭证失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '登录失败，请重试',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t*/\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\npage {\r\n    background: linear-gradient( 180deg, #E1EDFF 0%, #FDFFFF 100%);\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.login-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n    justify-content: space-around;\r\n\twidth: 100%;\r\n\tmin-height: 100vh;\r\n\tposition: relative;\r\n}\r\n\r\n/* 顶部背景图 */\r\n.banner-image {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tz-index: -1;\r\n}\r\n\r\n/* Logo和名称 */\r\n.logo-container {\r\n\tmargin-top: 180rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n\r\n.logo-box {\r\n\twidth: 180rpx;\r\n\theight: 180rpx;\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tbox-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.logo-image {\r\n\twidth: 140rpx;\r\n\theight: 140rpx;\r\n}\r\n\r\n.app-name {\r\n\tfont-size: 60rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333333;\r\n\tmargin-top: 40rpx;\r\n}\r\n\r\n/* 登录按钮区域 */\r\n.login-btn-area {\r\n\twidth: 80%;\r\n\tpadding: 0 60rpx;\r\n\tmargin-top: 140rpx;\r\n}\r\n\r\n.login-btn {\r\n\twidth: 100%;\r\n\theight: 100rpx;\r\n\tline-height: 100rpx;\r\n\tbackground: linear-gradient(to right, #3a97fa, #3b87f7);\r\n\tcolor: #ffffff;\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tborder-radius: 50rpx;\r\n\tbox-shadow: 0 8rpx 16rpx rgba(59, 135, 247, 0.3);\r\n}\r\n\r\n/* 协议区域 */\r\n.agreement-area {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-top: 40rpx;\r\n}\r\n\r\n.checkbox-container {\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.checkbox-circle {\r\n\twidth: 36rpx;\r\n\theight: 36rpx;\r\n\tborder-radius: 50%;\r\n\tborder: 2rpx solid #cccccc;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.checkbox-active {\r\n\tborder-color: #3F8DF9;\r\n}\r\n\r\n.checkbox-inner {\r\n\twidth: 20rpx;\r\n\theight: 20rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #3F8DF9;\r\n}\r\n\r\n.agreement-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666666;\r\n}\r\n\r\n.agreement-link {\r\n\tfont-size: 28rpx;\r\n\tcolor: #3F8DF9;\r\n}\r\n</style> ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}