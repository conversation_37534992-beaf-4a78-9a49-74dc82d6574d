<view class="withdraw-container"><view class="content-area"><view class="available-amount-section"><text class="available-label">可提现金额</text><view class="amount-display"><text class="currency-symbol">¥</text><text class="amount-value">{{availableAmount}}</text></view></view><view class="withdraw-amount-section"><view class="section-header"><text class="section-title">提现金额</text><text data-event-opts="{{[['tap',[['withdrawAll',['$event']]]]]}}" class="all-btn" bindtap="__e">全部</text></view><view class="amount-input-container"><text class="currency-symbol">¥</text><input class="amount-input" type="digit" placeholder="请输入提现金额" data-event-opts="{{[['input',[['__set_model',['','withdrawAmount','$event',[]]],['onAmountInput',['$event']]]]]}}" value="{{withdrawAmount}}" bindinput="__e"/></view></view><view class="payment-code-section"><text class="section-title">微信收款码</text><view data-event-opts="{{[['tap',[['choosePaymentCode',['$event']]]]]}}" class="upload-container" bindtap="__e"><block wx:if="{{!paymentCodeLocalPath&&!paymentCodeImage}}"><view class="upload-placeholder"><uni-icons vue-id="a98c6ad8-1" type="camera" size="40" color="#999999" bind:__l="__l"></uni-icons><text class="upload-text">点击上传微信收款码</text></view></block><block wx:else><image class="payment-code-image" src="{{paymentCodeLocalPath||paymentCodeImage}}" mode="aspectFit"></image></block></view><text class="upload-tip">请上传清晰的微信收款码图片</text></view><view class="withdraw-btn-container"><button class="{{['withdraw-btn',[(!canWithdraw)?'disabled':'']]}}" disabled="{{!canWithdraw}}" data-event-opts="{{[['tap',[['submitWithdraw',['$event']]]]]}}" bindtap="__e">立即提现</button></view></view></view>