@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #F6F7F9;
  min-height: 100vh;
}
.record-container {
  padding-bottom: 20rpx;
}
/* 标签页样式 */
.tab-container {
  display: flex;
  justify-content: space-around;
  background-color: #FFFFFF;
  padding-top: 10rpx;
  border-bottom: 1px solid #EEEEEE;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
}
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 20rpx 0;
}
.tab-text {
  font-size: 28rpx;
  color: #333333;
  padding-bottom: 16rpx;
}
.tab-count {
  font-size: 24rpx;
  color: #3F8DF9;
  margin-left: 4rpx;
}
.active .tab-text {
  font-weight: 500;
  color: #3F8DF9;
}
.active-line {
  position: absolute;
  bottom: 0;
  width: 80rpx;
  height: 6rpx;
  background-color: #3F8DF9;
  border-radius: 3rpx;
}
/* 订单列表样式 */
.order-list {
  padding: 20rpx;
}
/* 加载状态样式 */
.loading-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);
}
.loading-tip text {
  font-size: 30rpx;
  color: #3F8DF9;
}
/* 空订单提示样式 */
.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);
}
.empty-tip text {
  font-size: 30rpx;
  color: #999999;
}
.order-item {
  background-color: #FFFFFF;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.05);
}
/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.order-type {
  display: flex;
  align-items: center;
}
.ticket-icon {
  margin-right: 10rpx;
}
.ticket-type {
  font-size: 28rpx;
  color: #333333;
}
.order-status {
  font-size: 28rpx;
}
.pending {
  color: #3F8DF9;
}
.unpaid {
  color: #FF9500;
}
.completed {
  color: #888888;
}
.refunded {
  color: #00C851;
}
.refunding {
  color: #FF6B6B;
}
/* 路线信息 */
.order-route {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.route-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  flex: 1;
}
.route-price {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
}
/* 出发时间 */
.order-time {
  margin-bottom: 20rpx;
}
.time-text {
  font-size: 28rpx;
  color: #666666;
}
/* 分割线 */
.order-divider {
  height: 1rpx;
  background-color: #EEEEEE;
  margin: 20rpx 0;
}
/* 支付信息和操作按钮合并行 */
.order-payment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.payment-part {
  flex: 1;
}
.actions-part {
  display: flex;
  justify-content: flex-end;
}
/* 支付信息 */
.payment-text {
  font-size: 28rpx;
  color: #666666;
}
/* 操作按钮 */
.action-btn {
  font-size: 26rpx;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 30rpx;
  margin-left: 20rpx;
  background-color: #FFFFFF;
  border-radius: 30rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.cancel-btn {
  color: #666666;
  border: 1rpx solid #DDDDDD;
}
.refund-btn {
  color: #666666;
  border: 1rpx solid #DDDDDD;
}
.qr-btn {
  color: #3F8DF9;
  border: 1rpx solid #3F8DF9;
}
/* 群二维码弹窗样式 */
.qr-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.qr-popup-content {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}
.qr-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.qr-popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}
.qr-popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}
.close-icon {
  font-size: 40rpx;
  color: #999999;
  line-height: 1;
}
.qr-popup-body {
  padding: 40rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qr-code-image {
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto 30rpx;
  border-radius: 10rpx;
}
.qr-loading {
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 10rpx;
}
.qr-loading text {
  color: #999999;
  font-size: 28rpx;
}
.qr-popup-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}
.qr-popup-footer {
  padding: 20rpx 40rpx 40rpx;
}
.qr-save-btn {
  width: 100%;
  height: 80rpx;
  background-color: #333333;
  color: #FFFFFF;
  font-size: 32rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
