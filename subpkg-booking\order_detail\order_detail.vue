<template>
	<view class="order-detail-container">
		<!-- 订单状态 -->
		<view class="status-section">
			<text class="status-title">订单状态</text>
			<view class="status-content">
				<view class="status-icon">
					<image :src="orderStatus === '待出行' ? '/static/icons/status1.png' : '/static/icons/status2.png'"
						class="status-img"></image>
				</view>
				<text class="status-text" :style="{ color: orderStatus === '待出行' ? '#3F8DF9' : '#303133' }">{{orderStatus}}</text>
			</view>

			<!-- 退款理由模块 -->
			<view v-if="orderStatus === '拒绝退款' && reasonRefusal" class="refusal-reason-section">
				<text class="refusal-reason-title">退款理由</text>
				<text class="refusal-reason-content">{{reasonRefusal}}</text>
			</view>
		</view>
		
		<!-- 班次信息 -->
		<view class="trip-section">
			<text class="section-title">班次信息</text>
			<view class="trip-time">
				<text class="trip-date">{{tripInfo.date}}</text>
				<text class="trip-weekday">{{tripInfo.weekday}}</text>
				<text class="trip-hour">{{tripInfo.time}}</text>
			</view>

			<view class="trip-route">
				<view class="route-stations">
					<view class="station-dot start"></view>
					<view class="station-line"></view>
					<view class="station-dot end"></view>
				</view>
				<view class="station-names">
					<text class="departure-station">{{tripInfo.departure}}</text>
					<text class="arrival-station">{{tripInfo.arrival}}</text>
				</view>
			</view>
			
			<view class="navigation-link" v-if="orderStatus === '待出行'" @click="openNavigation">
				<text class="navigation-text">上车地点导航</text>
				<uni-icons type="right" size="16" color="#333333"></uni-icons>
			</view>
			<view class="ticket-count" v-else>
				<text class="ticket-count-text">成人票×{{passengers.length}}</text>
			</view>
		</view>
		
		<!-- 乘客信息 -->
		<view class="passenger-section">
			<view class="section-header" @click="togglePassengerList">
				<text class="section-title">乘客信息</text>
				<view class="collapse-btn">
					<text class="collapse-text">收起</text>
					<uni-icons :type="isPassengerCollapsed ? 'bottom' : 'top'" size="16" color="#3F8DF9"></uni-icons>
				</view>
			</view>
			
			<view class="passenger-list" v-show="!isPassengerCollapsed">
				<view class="passenger-item" v-for="(passenger, index) in passengers" :key="index">
					<text class="passenger-name">{{passenger.name}}</text>
					<text class="passenger-id">{{passenger.idCard}}</text>
					<text class="passenger-type">{{passenger.type}}</text>
				</view>
			</view>
		</view>
		
		<!-- 支付信息 -->
		<view class="payment-section">
			<view class="payment-item">
				<text class="payment-label">实付款</text>
				<text class="payment-value price">¥{{paymentInfo.actualPayment}}</text>
			</view>
			
			<view class="payment-item">
				<text class="payment-label">订单编号</text>
				<view class="order-id">
					<text class="payment-value">{{paymentInfo.orderId}}</text>
					<text class="copy-btn" @click="copyOrderId">复制</text>
				</view>
			</view>
			
			<view class="payment-item">
				<text class="payment-label">下单时间</text>
				<text class="payment-value">{{paymentInfo.orderTime}}</text>
			</view>
			
			<view class="payment-item">
				<text class="payment-label">付款时间</text>
				<text class="payment-value">{{paymentInfo.payTime}}</text>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button class="consult-btn" open-type="contact">
				<uni-icons type="phone" size="20" color="#3F8DF9"></uni-icons>
				<text class="action-text">咨询</text>
			</button>
			<button class="qr-code-btn" @click="showGroupQrCode">群二维码</button>
			<button class="after-sale-btn" @click="openAfterSale">售后服务</button>
		</view>

		<!-- 群二维码弹窗 -->
		<view class="qr-popup" v-if="showQrPopup" @click="hideGroupQrCode">
			<view class="qr-popup-content" @click.stop>
				<view class="qr-popup-header">
					<text class="qr-popup-title">购票成功</text>
					<view class="qr-popup-close" @click="hideGroupQrCode">
						<text class="close-icon">×</text>
					</view>
				</view>
				<view class="qr-popup-body">
					<image v-if="groupQrCodeUrl" :src="groupQrCodeUrl" class="qr-code-image" mode="aspectFit"></image>
					<view v-else class="qr-loading">
						<text>加载中...</text>
					</view>
					<text class="qr-popup-desc">扫码进群，专业售后服务和时效通知</text>
				</view>
				<view class="qr-popup-footer">
					<button class="qr-save-btn" @click="saveQrCode">保存二维码</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { orderApi, configApi } from '@/utils/api.js';

export default {
	data() {
		return {
			orderStatus: '待出行', // 默认为待出行状态
			isPassengerCollapsed: false,
			orderInfo: null,
			passengers: [],
			paymentInfo: {
				actualPayment: '0.00',
				orderId: '',
				orderTime: '',
				payTime: ''
			},
			// 原始订单数据
			orderData: null,
			// 班次信息
			tripInfo: {
				date: '',
				weekday: '',
				time: '',
				departure: '',
				arrival: ''
			},
			showQrPopup: false,  // 群二维码弹窗显示状态
			groupQrCodeUrl: '',  // 群二维码图片URL
			reasonRefusal: ''  // 退款理由
		}
	},
	onLoad(options) {
		// 获取传递过来的订单ID
		const orderId = options.id;

		if(orderId) {
			console.log('Loaded order id:', orderId);

			// 从本地存储获取订单数据
			const orderData = uni.getStorageSync('orderDetailData');
			if (orderData && orderData.id == orderId) {
				this.orderData = orderData;
				this.processOrderData(orderData);
			} else {
				// 如果本地没有数据，可以调用接口获取
				console.warn('订单数据不存在，需要从接口获取');
				uni.showToast({
					title: '订单数据加载失败',
					icon: 'none'
				});
			}
		}
	},
	onUnload() {
		// 页面卸载时清理本地存储的订单数据
		uni.removeStorageSync('orderDetailData');
	},
	methods: {
		// 处理订单数据
		processOrderData(orderData) {
			// 设置订单状态
			this.orderStatus = orderData.status;

			// 设置退款理由（当状态为拒绝退款时）
			this.reasonRefusal = orderData.reasonRefusal || '';

			// 设置班次信息
			this.tripInfo = {
				date: this.formatDateForDisplay(orderData.originalData.upTicketTime),
				weekday: this.getWeekday(orderData.originalData.upTicketTime),
				time: this.formatTimeForDisplay(orderData.originalData.upTicketTime),
				departure: orderData.originalData.upAddress || '未知',
				arrival: orderData.originalData.downAddress || '未知'
			};

			// 设置乘客信息（从原始数据中提取）
			this.passengers = [{
				name: orderData.originalData.name,
				idCard: this.maskIdCard(orderData.originalData.idNumber),
				type: '成人票'
			}];

			// 设置支付信息
			this.paymentInfo = {
				actualPayment: parseFloat(orderData.originalData.payAmout || 0).toFixed(2),
				orderId: orderData.originalData.orderNo,
				orderTime: this.formatDateTime(orderData.originalData.upTicketTime),
				payTime: this.formatDateTime(orderData.originalData.upTicketTime)
			};
		},

		// 格式化日期显示（MM-dd格式）
		formatDateForDisplay(dateStr) {
			if (!dateStr) return '待定';
			try {
				const date = new Date(dateStr);
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${month}-${day}`;
			} catch (error) {
				return '待定';
			}
		},

		// 格式化时间显示（HH:mm 出发格式）
		formatTimeForDisplay(dateStr) {
			if (!dateStr) return '待定 出发';
			try {
				const date = new Date(dateStr);
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${hours}:${minutes} 出发`;
			} catch (error) {
				return '待定 出发';
			}
		},

		// 获取星期几
		getWeekday(dateStr) {
			if (!dateStr) return '';
			try {
				const date = new Date(dateStr);
				const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
				return weekdays[date.getDay()];
			} catch (error) {
				return '';
			}
		},

		// 格式化完整日期时间
		formatDateTime(dateStr) {
			if (!dateStr) return '待定';
			try {
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			} catch (error) {
				return '待定';
			}
		},

		// 身份证号脱敏处理
		maskIdCard(idCard) {
			if (!idCard || idCard.length < 8) return idCard;
			return idCard.substring(0, 4) + '**********' + idCard.substring(idCard.length - 4);
		},

		// 切换乘客信息显示/隐藏
		togglePassengerList() {
			this.isPassengerCollapsed = !this.isPassengerCollapsed;
		},
		
		// 打开导航
		openNavigation() {
			uni.showToast({
				title: '打开导航功能开发中',
				icon: 'none'
			});
		},
		
		// 复制订单编号
		copyOrderId() {
			uni.setClipboardData({
				data: this.paymentInfo.orderId,
				success: () => {
					uni.showToast({
						title: '订单号已复制',
						icon: 'success'
					});
				}
			});
		},
		

		
		// 打开售后服务
		async openAfterSale() {
			try {
				// 检查是否有订单数据
				if (!this.orderData || !this.orderData.id) {
					uni.showToast({
						title: '订单信息不存在',
						icon: 'none'
					});
					return;
				}

				// 显示确认弹框
				const result = await new Promise((resolve) => {
					uni.showModal({
						title: '申请售后',
						content: '确定要申请售后吗？',
						success: (res) => {
							resolve(res.confirm);
						}
					});
				});

				if (!result) {
					return; // 用户取消
				}

				// 显示加载状态
				uni.showLoading({
					title: '申请中...'
				});

				// 调用申请售后接口
				const response = await orderApi.applyRefund(this.orderData.id);

				uni.hideLoading();

				if (response && response.code === 200) {
					uni.showToast({
						title: '申请售后成功',
						icon: 'success'
					});

					// 可以选择返回上一页或刷新当前页面数据
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					uni.showToast({
						title: response.msg || '申请售后失败',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('申请售后失败:', error);
				uni.showToast({
					title: '申请售后失败，请重试',
					icon: 'none'
				});
			}
		},
		
		// 获取订单详情（实际开发中使用）
		fetchOrderDetail(orderId) {
			// 这里实现获取订单详情的接口调用逻辑
			// 比如：
			// this.$http.get(`/api/orders/${orderId}`).then(res => {
			//     this.orderInfo = res.data;
			//     this.passengers = res.data.passengers;
			//     this.paymentInfo = res.data.paymentInfo;
			// })
		},

		// 显示群二维码弹窗
		async showGroupQrCode() {
			try {
				// 显示弹窗
				this.showQrPopup = true;

				// 如果已经有二维码URL，直接显示
				if (this.groupQrCodeUrl) {
					return;
				}

				// 调用接口获取群二维码
				const response = await configApi.getConfig('group_qr_code');

				if (response && response.code === 200) {
					this.groupQrCodeUrl = response.msg || '';
				} else {
					uni.showToast({
						title: '获取群二维码失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取群二维码失败:', error);
				uni.showToast({
					title: '获取群二维码失败',
					icon: 'none'
				});
			}
		},

		// 隐藏群二维码弹窗
		hideGroupQrCode() {
			this.showQrPopup = false;
		},

		// 保存二维码到相册
		async saveQrCode() {
			if (!this.groupQrCodeUrl) {
				uni.showToast({
					title: '二维码加载中，请稍后',
					icon: 'none'
				});
				return;
			}

			try {
				// 显示加载提示
				uni.showLoading({
					title: '正在保存...'
				});

				// 先下载图片到本地
				const downloadResult = await new Promise((resolve, reject) => {
					uni.downloadFile({
						url: this.groupQrCodeUrl,
						success: (res) => {
							if (res.statusCode === 200) {
								resolve(res.tempFilePath);
							} else {
								reject(new Error('下载失败'));
							}
						},
						fail: reject
					});
				});

				// 保存到相册
				await new Promise((resolve, reject) => {
					uni.saveImageToPhotosAlbum({
						filePath: downloadResult,
						success: resolve,
						fail: reject
					});
				});

				uni.hideLoading();
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
			} catch (error) {
				uni.hideLoading();
				console.error('保存失败:', error);

				let errorMsg = '保存失败，请重试';
				if (error.errMsg && error.errMsg.includes('auth')) {
					errorMsg = '请授权访问相册';
				} else if (error.errMsg && error.errMsg.includes('download')) {
					errorMsg = '图片下载失败';
				}

				uni.showToast({
					title: errorMsg,
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #F6F7F9;
	min-height: 100vh;
}

.order-detail-container {
	padding-bottom: 120rpx; /* 为底部操作栏预留空间 */
}

/* 公共样式 */
.section-title {
	font-size: 32rpx;
	color: #333333;
	font-weight: bold;
}

/* 订单状态 */
.status-section {
	background-color: #FFFFFF;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.status-title {
	font-size: 32rpx;
	color: #333333;
	font-weight: bold;
	margin-bottom: 20rpx;
	display: block;
}

.status-content {
	display: flex;
	align-items: center;
	// padding: 30rpx 0;
}

.status-icon {
	margin-right: 20rpx;
}

.status-img {
	width: 80rpx;
	height: 80rpx;
}

.status-text {
	font-size: 45rpx;
	font-weight: bold;
}

/* 退款理由模块 */
.refusal-reason-section {
	margin-top: 30rpx;
	padding-top: 30rpx;
	border-top: 1rpx solid #EEEEEE;
}

.refusal-reason-title {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 15rpx;
	display: block;
}

.refusal-reason-content {
	font-size: 30rpx;
	color: #EE0A24;
	line-height: 1.5;
	display: block;
}

/* 班次信息 */
.trip-section {
	background-color: #FFFFFF;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.trip-time {
	display: flex;
	align-items: center;
	margin: 30rpx 0;
}

.trip-date {
	font-size: 30rpx;
	color: #333333;
	font-weight: bold;
	margin-right: 20rpx;
}

.trip-weekday {
	font-size: 28rpx;
	color: #666666;
	margin-right: 20rpx;
}

.trip-hour {
	font-size: 28rpx;
	color: #666666;
}

.trip-route {
	display: flex;
	align-items: flex-start;
	// margin-bottom: 30rpx;
}

.route-stations {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 20rpx;
	padding-top: 17rpx;
}

.station-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
}

.station-dot.start {
	background-color: #3F8DF9;
}

.station-dot.end {
	background-color: #FF7744;
}

.station-line {
	width: 2rpx;
	height: 60rpx;
	background-color: #DDDDDD;
	margin: 5rpx 0;
}

.station-names {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.departure-station, .arrival-station {
	font-size: 32rpx;
	color: #333333;
	font-weight: bold;
	line-height: 65rpx;
}

.navigation-link {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.navigation-text {
	font-size: 28rpx;
	color: #333333;
	margin-right: 10rpx;
}

.ticket-count {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.ticket-count-text {
	font-size: 28rpx;
	color: #333333;
}

/* 乘客信息 */
.passenger-section {
	background-color: #FFFFFF;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.collapse-btn {
	display: flex;
	align-items: center;
}

.collapse-text {
	font-size: 28rpx;
	color: #3F8DF9;
	margin-right: 5rpx;
}

.passenger-list {
	border-top: 1rpx solid #EEEEEE;
	padding-top: 20rpx;
}

.passenger-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
}

.passenger-name {
	font-size: 30rpx;
	color: #333333;
	font-weight: bold;
	margin-right: 20rpx;
}

.passenger-id {
	font-size: 28rpx;
	color: #999999;
	flex: 1;
}

.passenger-type {
	font-size: 24rpx;
	color: #999999;
	background-color: #F6F7F9;
	padding: 4rpx 12rpx;
	border-radius: 4rpx;
}

/* 支付信息 */
.payment-section {
	background-color: #FFFFFF;
	padding: 30rpx;
}

.payment-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.payment-label {
	font-size: 28rpx;
	color: #666666;
}

.payment-value {
	font-size: 28rpx;
	color: #333333;
	max-width: 400rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.payment-value.price {
	color: #EE0A24;
	font-weight: bold;
}

.order-id {
	display: flex;
	align-items: center;
}

.copy-btn {
	font-size: 28rpx;
	color: #3F8DF9;
	margin-left: 20rpx;
}

/* 底部操作栏 */
.bottom-actions {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	background-color: #FFFFFF;
	padding: 20rpx 30rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	z-index: 10;
}

.consult-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-right: 30rpx;
	width: 100rpx;
	background-color: transparent;
	border: none;
	padding: 0;
	font-size: inherit;
	line-height: inherit;
}

.action-text {
	font-size: 24rpx;
	color: #333333;
	margin-top: 5rpx;
}

.qr-code-btn {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	background-color: #FFFFFF;
	color: #3F8DF9;
	border: 1rpx solid #3F8DF9;
	border-radius: 40rpx;
	font-size: 30rpx;
	font-weight: normal;
	margin-right: 20rpx;
}

.after-sale-btn {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	background-color: #FFFFFF;
	color: #3F8DF9;
	border: 1rpx solid #3F8DF9;
	border-radius: 40rpx;
	font-size: 30rpx;
	font-weight: normal;
}

/* 群二维码弹窗样式 */
.qr-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}

.qr-popup-content {
	background-color: #FFFFFF;
	border-radius: 20rpx;
	width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.qr-popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.qr-popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.qr-popup-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;
	background-color: #f5f5f5;
}

.close-icon {
	font-size: 40rpx;
	color: #999999;
	line-height: 1;
}

.qr-popup-body {
	padding: 40rpx;
	text-align: center;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.qr-code-image {
	width: 400rpx;
	height: 400rpx;
	margin: 0 auto 30rpx;
	border-radius: 10rpx;
}

.qr-loading {
	width: 400rpx;
	height: 400rpx;
	margin: 0 auto 30rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 10rpx;
}

.qr-loading text {
	color: #999999;
	font-size: 28rpx;
}

.qr-popup-desc {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.5;
}

.qr-popup-footer {
	padding: 20rpx 40rpx 40rpx;
}

.qr-save-btn {
	width: 100%;
	height: 80rpx;
	background-color: #333333;
	color: #FFFFFF;
	font-size: 32rpx;
	border-radius: 40rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}
</style> 