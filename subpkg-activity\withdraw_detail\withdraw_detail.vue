<template>
	<view class="withdraw-detail-container">
		<!-- 内容区域 -->
		<view class="content-area">
			<!-- 金额显示区域 -->
			<view class="amount-section">
				<view class="amount-icon">
					<text class="currency-symbol">¥</text>
				</view>
				<view class="amount-display">
					<text class="amount-value">- {{withdrawAmount}}</text>
				</view>
				<text class="status-text">进行中</text>
			</view>

			<!-- 详情信息 -->
			<view class="detail-section">
				<view class="detail-item">
					<text class="detail-label">时间</text>
					<text class="detail-value">{{withdrawTime}}</text>
				</view>
				<view class="detail-item">
					<text class="detail-label">提现位置</text>
					<text class="detail-value">微信零钱</text>
				</view>
			</view>

			<!-- 订单进度 -->
			<view class="progress-section">
				<text class="progress-title">订单进度</text>
				<view class="progress-list">
					<view class="progress-item completed">
						<view class="progress-icon">
							<view class="icon-circle completed">
								<uni-icons type="checkmarkempty" size="16" color="#ffffff"></uni-icons>
							</view>
						</view>
						<view class="progress-content">
							<text class="progress-step">创建充值订单</text>
							<text class="progress-time">更新于{{createTime}}</text>
							<text class="progress-desc">此过程可能需要1小时</text>
						</view>
					</view>
					<view class="progress-item waiting">
						<view class="progress-icon">
							<view class="icon-circle waiting">
								<view class="waiting-dot"></view>
							</view>
						</view>
						<view class="progress-content">
							<text class="progress-step">等待中...</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				withdrawAmount: '100,000', // 提现金额
				withdrawTime: '2024-12-8 18:43:25', // 提现时间
				createTime: '2023-12-8 18:43:25', // 创建时间
			}
		},
		onLoad(options) {
			// 从上一页获取提现金额
			if (options.amount) {
				// 格式化金额显示，添加千分位分隔符
				const amount = parseFloat(options.amount);
				this.withdrawAmount = this.formatAmount(amount);
			}

			// 设置当前时间
			this.setCurrentTime();
		},
		methods: {
			// 格式化金额，添加千分位分隔符
			formatAmount(amount) {
				return amount.toLocaleString();
			},

			// 设置当前时间
			setCurrentTime() {
				const now = new Date();
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0');
				const day = String(now.getDate()).padStart(2, '0');
				const hours = String(now.getHours()).padStart(2, '0');
				const minutes = String(now.getMinutes()).padStart(2, '0');
				const seconds = String(now.getSeconds()).padStart(2, '0');

				this.withdrawTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				this.createTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #F6F7F9;
}

.withdraw-detail-container {
	min-height: 100vh;
	background-color: #F6F7F9;
}

/* 内容区域 */
.content-area {
	padding: 40rpx 30rpx;
}

/* 金额显示区域 */
.amount-section {
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 60rpx 40rpx;
	margin-bottom: 30rpx;
	text-align: center;
}

.amount-icon {
	width: 120rpx;
	height: 120rpx;
	background-color: #3B99FC;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 40rpx;
}

.currency-symbol {
	font-size: 48rpx;
	color: #ffffff;
	font-weight: bold;
}

.amount-display {
	margin-bottom: 20rpx;
}

.amount-value {
	font-size: 72rpx;
	color: #333333;
	font-weight: bold;
}

.status-text {
	font-size: 28rpx;
	color: #999999;
}

/* 详情信息 */
.detail-section {
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #F5F5F5;
}

.detail-item:last-child {
	border-bottom: none;
}

.detail-label {
	font-size: 32rpx;
	color: #666666;
}

.detail-value {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

/* 订单进度 */
.progress-section {
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
}

.progress-title {
	font-size: 36rpx;
	color: #333333;
	font-weight: bold;
	display: block;
	margin-bottom: 40rpx;
}

.progress-list {
	position: relative;
}

.progress-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 40rpx;
	position: relative;
}

.progress-item:last-child {
	margin-bottom: 0;
}

.progress-item:not(:last-child)::after {
	content: '';
	position: absolute;
	left: 27rpx;
	top: 56rpx;
	width: 2rpx;
	height: 80rpx;
	background-color: #E5E5E5;
}

.progress-icon {
	margin-right: 30rpx;
	position: relative;
	z-index: 1;
}

.icon-circle {
	width: 56rpx;
	height: 56rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-circle.completed {
	background-color: #3B99FC;
}

.icon-circle.waiting {
	background-color: #E5E5E5;
	position: relative;
}

.waiting-dot {
	width: 16rpx;
	height: 16rpx;
	background-color: #999999;
	border-radius: 50%;
}

.progress-content {
	flex: 1;
	padding-top: 8rpx;
}

.progress-step {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
	display: block;
	margin-bottom: 8rpx;
}

.progress-time {
	font-size: 24rpx;
	color: #999999;
	display: block;
	margin-bottom: 4rpx;
}

.progress-desc {
	font-size: 24rpx;
	color: #999999;
	display: block;
}

.progress-item.completed .progress-step {
	color: #333333;
}

.progress-item.waiting .progress-step {
	color: #999999;
}
</style>
