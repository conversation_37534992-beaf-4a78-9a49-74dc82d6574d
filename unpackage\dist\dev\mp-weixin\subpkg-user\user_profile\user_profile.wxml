<view class="profile-container"><view class="avatar-section"><text class="section-label">头像</text><view class="avatar-wrapper"><button class="avatar-button" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><image class="avatar" src="{{userInfo.avatar}}"></image></button><view class="avatar-edit-icon"><image class="edit-icon" src="/static/icons/arrow_right.png"></image></view></view></view><view class="info-section"><text class="section-label">昵称</text><view class="info-input-wrapper"><input class="info-value" type="nickname" placeholder="请输入昵称" data-event-opts="{{[['blur',[['onNicknameChange',['$event']]]]]}}" value="{{userInfo.nickName}}" bindblur="__e"/><view class="avatar-edit-icon"><image class="edit-icon" src="/static/icons/arrow_right.png"></image></view></view></view><view class="info-section"><text class="section-label">手机号</text><view class="info-input-wrapper"><text class="info-value">{{userInfo.phone}}</text></view></view><view class="logout-button-wrapper"><button data-event-opts="{{[['tap',[['logout',['$event']]]]]}}" class="logout-button" bindtap="__e">退出登录</button></view></view>