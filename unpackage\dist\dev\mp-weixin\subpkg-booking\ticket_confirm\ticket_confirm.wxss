@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-466fd826 {
  background-color: #F5F5F5;
}
.content.data-v-466fd826 {
  display: flex;
  flex-direction: column;
  padding-bottom: 180rpx;
  /* 为底部的购票须知和支付栏预留空间 */
  background-color: #f5f7fa;
  /* 添加顶部边距 */
}
/* 加载状态 */
.loading-container.data-v-466fd826 {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 200rpx 0;
  min-height: 50vh;
}
.loading-text.data-v-466fd826 {
  font-size: 28rpx;
  color: #999;
}
/* 行程信息样式 */
.trip-info.data-v-466fd826 {
  background-color: #fff;
  padding: 34rpx 24rpx;
  margin-bottom: 24rpx;
}
.date-time.data-v-466fd826 {
  font-size: 24rpx;
  color: #303133;
  padding-bottom: 20rpx;
}
.route-info.data-v-466fd826 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.route-stations.data-v-466fd826 {
  display: flex;
  align-items: center;
}
.route-from.data-v-466fd826,
.route-to.data-v-466fd826 {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.route-arrow.data-v-466fd826 {
  margin: 0 10rpx;
  color: #999;
}
.bus-type.data-v-466fd826 {
  font-size: 26rpx;
  color: #000000;
  padding: 6rpx 16rpx;
  font-weight: bold;
}
.trip-details.data-v-466fd826 {
  padding-top: 20rpx;
}
.trip-detail-item.data-v-466fd826 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}
.detail-info.data-v-466fd826 {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
}
.detail-label.data-v-466fd826 {
  width: 50%;
  font-size: 24rpx;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  box-sizing: border-box;
}
.detail-separator.data-v-466fd826 {
  font-size: 22rpx;
  color: #ddd;
  margin: 0 10rpx;
  padding-top: 2rpx;
}
.arrow-icon.data-v-466fd826 {
  width: 32rpx;
  height: 32rpx;
}
/* 乘客选择样式 */
.passenger-section.data-v-466fd826 {
  background-color: #fff;
  padding: 34rpx 24rpx;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  margin: 0 24rpx 24rpx 24rpx;
}
.section-title.data-v-466fd826 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.passenger-item.data-v-466fd826 {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.passenger-checkbox.data-v-466fd826 {
  margin-right: 20rpx;
}
.checkbox-circle.data-v-466fd826 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}
.checkbox-active.data-v-466fd826 {
  border-color: #3F8DF9;
}
.checkbox-inner.data-v-466fd826 {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #3F8DF9;
}
.passenger-info.data-v-466fd826 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  overflow: hidden;
}
.passenger-name.data-v-466fd826 {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-right: 10rpx;
  white-space: nowrap;
}
.passenger-id.data-v-466fd826 {
  font-size: 26rpx;
  color: #999;
  margin-right: 20rpx;
  white-space: nowrap;
}
.passenger-type-text.data-v-466fd826 {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f7fa;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  white-space: nowrap;
}
.passenger-arrow.data-v-466fd826 {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-passenger.data-v-466fd826 {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  justify-content: center;
  background: #F7F8FA;
}
.add-icon.data-v-466fd826 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.add-text.data-v-466fd826 {
  font-size: 30rpx;
  color: #3F8DF9;
}
.passenger-phone.data-v-466fd826 {
  display: flex;
  padding: 20rpx 0;
  border-top: 1px solid #f0f0f0;
  align-items: center;
}
.phone-label.data-v-466fd826 {
  font-size: 28rpx;
  color: #303133;
  margin-right: 10rpx;
}
.required.data-v-466fd826 {
  color: #ff4757;
  font-size: 28rpx;
}
.phone-input.data-v-466fd826 {
  height: 80rpx;
  padding: 0 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
}
.phone-input.data-v-466fd826:focus {
  border-color: #3F8DF9;
}
.phone-value.data-v-466fd826 {
  font-size: 28rpx;
  color: #333;
}
/* 备注信息样式 */
.remark-section.data-v-466fd826 {
  display: flex;
  padding: 20rpx 0;
  border-top: 1px solid #f0f0f0;
  align-items: center;
}
.remark-label.data-v-466fd826 {
  font-size: 28rpx;
  color: #303133;
  margin-right: 10rpx;
}
.remark-input.data-v-466fd826 {
  padding: 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  resize: none;
  width: 80%;
}
.remark-input.data-v-466fd826:focus {
  border-color: #3F8DF9;
}
/* 价格信息样式 */
.price-section.data-v-466fd826 {
  background-color: #fff;
  padding: 34rpx 24rpx;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  margin: 0 24rpx 24rpx 24rpx;
}
.price-item.data-v-466fd826 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
}
.price-label.data-v-466fd826 {
  font-size: 28rpx;
  color: #333;
}
.price-value.data-v-466fd826 {
  font-size: 28rpx;
  color: #333;
}
.price-discount.data-v-466fd826 {
  font-size: 28rpx;
  color: #EE0A24;
}
.coupon-item.data-v-466fd826 {
  padding: 20rpx 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}
.coupon-select.data-v-466fd826 {
  display: flex;
  align-items: center;
}
.coupon-text.data-v-466fd826 {
  font-size: 28rpx;
  color: #999;
  margin-right: 10rpx;
}
.total-price.data-v-466fd826 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0 0;
}
.total-label.data-v-466fd826 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.total-value.data-v-466fd826 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
/* 购票须知样式 */
.terms-section.data-v-466fd826 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  position: fixed;
  bottom: 100rpx;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 9;
}
.terms-checkbox.data-v-466fd826 {
  margin-right: 10rpx;
}
.terms-text.data-v-466fd826 {
  font-size: 26rpx;
  color: #666;
}
.terms-link.data-v-466fd826 {
  font-size: 26rpx;
  color: #3F8DF9;
}
/* 底部支付栏样式 */
.payment-bar.data-v-466fd826 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  border-top: 1rpx solid #f0f0f0;
}
.payment-total.data-v-466fd826 {
  flex: 1;
  display: flex;
  align-items: baseline;
}
.payment-label.data-v-466fd826 {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}
.payment-currency.data-v-466fd826 {
  font-size: 30rpx;
  color: #EE0A24;
  font-weight: bold;
  margin-right: 4rpx;
}
.payment-value.data-v-466fd826 {
  font-size: 42rpx;
  color: #EE0A24;
  font-weight: bold;
}
.payment-button.data-v-466fd826 {
  background-color: #3F8DF9;
  color: #fff;
  font-size: 32rpx;
  padding: 20rpx 60rpx;
  border-radius: 50rpx;
}
/* 优惠券已选择状态样式 */
.coupon-selected.data-v-466fd826 {
  font-size: 28rpx;
  color: #ff9500;
  font-weight: bold;
  margin-right: 10rpx;
}
/* 优惠券选择弹窗样式 */
.coupon-popup.data-v-466fd826 {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 11;
}
.coupon-mask.data-v-466fd826 {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.coupon-container.data-v-466fd826 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 12;
  -webkit-animation: slideUp-data-v-466fd826 0.3s ease-out;
          animation: slideUp-data-v-466fd826 0.3s ease-out;
}
@-webkit-keyframes slideUp-data-v-466fd826 {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUp-data-v-466fd826 {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.coupon-header.data-v-466fd826 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  position: relative;
  border-bottom: 1px solid #f5f5f5;
}
.coupon-close.data-v-466fd826 {
  position: absolute;
  left: 30rpx;
  font-size: 46rpx;
  color: #333;
  line-height: 1;
}
.coupon-title.data-v-466fd826 {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}
.coupon-list.data-v-466fd826 {
  max-height: 60vh;
  overflow-y: auto;
  padding: 30rpx 20rpx;
}
.coupon-item.data-v-466fd826 {
  position: relative;
  margin-bottom: 30rpx;
}
.coupon-content.data-v-466fd826 {
  background-color: #FF7744;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  position: relative;
}
.coupon-amount.data-v-466fd826 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  padding-right: 20rpx;
  position: relative;
}
.coupon-amount.data-v-466fd826::after {
  content: "";
  position: absolute;
  top: -20rpx;
  bottom: -20rpx;
  right: 0;
  width: 2rpx;
  background-color: rgba(255, 255, 255, 0.3);
}
.coupon-currency.data-v-466fd826 {
  font-size: 36rpx;
  color: #fff;
  font-weight: bold;
  margin-right: 4rpx;
}
.coupon-value.data-v-466fd826 {
  font-size: 80rpx;
  color: #fff;
  font-weight: bold;
  line-height: 1;
}
.coupon-condition.data-v-466fd826 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 10rpx;
}
.coupon-info.data-v-466fd826 {
  flex: 1;
  padding-left: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.coupon-type.data-v-466fd826 {
  font-size: 36rpx;
  color: #fff;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.coupon-expire.data-v-466fd826 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}
.coupon-select-icon.data-v-466fd826 {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 30rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border: 2rpx solid transparent;
}
.selected .select-inner.data-v-466fd826 {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background-color: #3F8DF9;
}
/* 优惠券锯齿边 */
.coupon-edge-left.data-v-466fd826, .coupon-edge-right.data-v-466fd826 {
  position: absolute;
  top: 50%;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: #f5f7fa;
}
.coupon-edge-left.data-v-466fd826 {
  left: -15rpx;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.coupon-edge-right.data-v-466fd826 {
  right: -15rpx;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.coupon-footer.data-v-466fd826 {
  padding: 20rpx 30rpx 50rpx;
}
.coupon-confirm-btn.data-v-466fd826 {
  background-color: #333;
  color: #fff;
  font-size: 34rpx;
  padding: 24rpx;
  border-radius: 50rpx;
  text-align: center;
}
