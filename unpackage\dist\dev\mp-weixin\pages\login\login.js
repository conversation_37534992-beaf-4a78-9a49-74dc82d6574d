(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/login/login"],{44:function(e,n,t){"use strict";(function(e,n){var i=t(4);t(26);i(t(25));var o=i(t(45));e.__webpack_require_UNI_MP_PLUGIN__=t,n(o.default)}).call(this,t(1)["default"],t(2)["createPage"])},45:function(e,n,t){"use strict";t.r(n);var i=t(46),o=t(48);for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);t(50);var c,s=t(33),d=Object(s["default"])(o["default"],i["render"],i["staticRenderFns"],!1,null,null,null,!1,i["components"],c);d.options.__file="pages/login/login.vue",n["default"]=d.exports},46:function(e,n,t){"use strict";t.r(n);var i=t(47);t.d(n,"render",(function(){return i["render"]})),t.d(n,"staticRenderFns",(function(){return i["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return i["recyclableRender"]})),t.d(n,"components",(function(){return i["components"]}))},47:function(e,n,t){"use strict";var i;t.r(n),t.d(n,"render",(function(){return o})),t.d(n,"staticRenderFns",(function(){return c})),t.d(n,"recyclableRender",(function(){return r})),t.d(n,"components",(function(){return i}));var o=function(){var e=this,n=e.$createElement;e._self._c},r=!1,c=[];o._withStripped=!0},48:function(e,n,t){"use strict";t.r(n);var i=t(49),o=t.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(r);n["default"]=o.a},49:function(e,n,t){"use strict";(function(e){var i=t(4);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=i(t(30)),r=i(t(41)),c={data:function(){return{isAgreed:!1,redirect:"",inviteUserId:""}},onLoad:function(e){if(e.redirect&&(this.redirect=decodeURIComponent(e.redirect)),e.userId&&(this.inviteUserId=e.userId,console.log("检测到邀请用户ID:",this.inviteUserId)),e.scene){var n=decodeURIComponent(e.scene);n.includes("userId=")?this.inviteUserId=n.split("userId=")[1]:this.inviteUserId=n,console.log("从场景值获取邀请用户ID:",this.inviteUserId)}},methods:{toggleAgreement:function(){this.isAgreed=!this.isAgreed},viewUserAgreement:function(){e.showLoading({title:"加载中..."}),r.default.get("/app/configure/user_agreement").then((function(n){e.hideLoading(),n&&200===n.code?e.showModal({title:"用户协议",content:n.msg||"暂无内容",showCancel:!1,confirmText:"我知道了"}):e.showToast({title:"获取协议内容失败",icon:"none"})})).catch((function(n){e.hideLoading(),e.showToast({title:"获取协议内容失败",icon:"none"})}))},viewPrivacyPolicy:function(){e.showLoading({title:"加载中..."}),r.default.get("/app/configure/privacy_agreement").then((function(n){e.hideLoading(),n&&200===n.code?e.showModal({title:"隐私政策",content:n.msg||"暂无内容",showCancel:!1,confirmText:"我知道了"}):e.showToast({title:"获取隐私政策内容失败",icon:"none"})})).catch((function(n){e.hideLoading(),e.showToast({title:"获取隐私政策内容失败",icon:"none"})}))},getPhoneNumber:function(n){var t=this;this.isAgreed?(e.showLoading({title:"登录中...",mask:!0}),n.detail.code?e.login({provider:"weixin",success:function(i){i.code?(console.log(i.code,n.detail.code),t.loginApi(i.code,n.detail.code)):(e.hideLoading(),e.showToast({title:"获取登录凭证失败",icon:"none"}))},fail:function(){e.hideLoading(),e.showToast({title:"登录失败，请重试",icon:"none"})}}):(e.hideLoading(),e.showToast({title:"获取手机号失败，请重试",icon:"none"}))):e.showToast({title:"请先阅读并同意协议",icon:"none"})},loginApi:function(n,t){var i=this,c={phoneCode:t};this.inviteUserId&&(c.inviteUserId=this.inviteUserId,console.log("登录时传递邀请用户ID:",this.inviteUserId)),r.default.post("/app/login?phoneCode="+t+"&jsCode="+n+"&inviteUserId="+this.inviteUserId,c).then((function(e){o.default.setToken(e.token),i.getUserInfo()})).catch((function(n){e.hideLoading(),e.showToast({title:n.msg||"登录失败，请重试",icon:"none"})}))},getUserInfo:function(){var n=this;r.default.get("/app/user").then((function(t){var i={userid:t.data.userId,nickName:t.data.nickName||"微信用户",phone:t.data.phonenumber,avatar:t.data.avatar};o.default.setUserInfo(i),e.hideLoading(),e.showToast({title:"登录成功",icon:"success"}),setTimeout((function(){if(n.redirect){var t=["/pages/index/index","/pages/record/record","/pages/mine/mine"];t.includes(n.redirect)?e.switchTab({url:n.redirect}):e.redirectTo({url:n.redirect})}else e.switchTab({url:"/pages/index/index"})}),1e3)})).catch((function(n){e.hideLoading(),e.showToast({title:n.msg||"获取用户信息失败",icon:"none"}),o.default.clearLoginInfo()}))}}};n.default=c}).call(this,t(2)["default"])},50:function(e,n,t){"use strict";t.r(n);var i=t(51),o=t.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(r);n["default"]=o.a},51:function(e,n,t){}},[[44,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map